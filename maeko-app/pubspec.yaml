name: amverton
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html

version: 0.0.9+3

environment:
  sdk: ">=2.17.0 <3.0.0"

# Dependencies specify other packages that your pacokage needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  hugeicons: ^0.0.9
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.0.16
  http: ^1.0.0
  flutter_easyloading: ^3.0.5
  awesome_dialog: ^3.0.2
  carousel_indicator: ^1.0.6
  flutter_cache_manager: ^3.3.0
  cached_network_image: ^3.2.0
  carousel_slider: ^4.2.1
  dots_indicator: ^3.0.0
  auto_size_text: ^3.0.0
  animated_text_kit: ^4.2.1
  dropdown_button2: ^2.1.2
  shimmer: ^2.0.0
  url_launcher: ^6.1.8
  share_plus: ^7.2.2
  sign_in_with_apple: ^6.1.1
  flutter_svg: ^2.0.10+1
  intl: ^0.18.0
  flutter_widget_from_html: ^0.10.0
  firebase_messaging: ^14.3.0
  firebase_core: ^2.8.0
  sliding_up_panel: ^2.0.0+1
  google_maps_flutter: ^2.2.3
  package_info_plus: ^4.2.0
  connectivity_plus: ^4.0.0
  geolocator: ^9.0.2
  pin_input_text_field: ^4.4.1
  flutter_switch: ^0.3.2
  win32: ^5.5.3
  webview_flutter: ^4.2.0
  easy_stepper: ^0.5.2+1
  rxdart: ^0.27.7
  firebase_auth: ^4.6.2
  google_sign_in: ^6.1.4
  flutter_facebook_auth: ^7.1.1
  toggle_switch: ^2.1.0
  syncfusion_flutter_gauges: ^24.2.7
  flutter_contacts: ^1.1.7+1
  decimal: ^2.3.3
  flutter_riverpod: ^2.4.10
  google_fonts: 6.1.0
  chewie: ^1.7.5

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.2.0
  hive_generator: ^2.0.0
  flutter_launcher_icons: ^0.13.1

dependency_overrides:
  http: 0.13.4

flutter_icons:
  image_path: "assets/icons/App Icon/Logo.png"
  android: true
  ios: true
  remove_alpha_ios: true
  # dart run flutter_launcher_icons
  # flutter pub run flutter_launcher_icons:main

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analyflutsis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^1.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/icons/
    - assets/icons/A/
    - assets/icons/A/Menu Bar/
    - assets/icons/B/
    - assets/icons/B/Menu Bar/
    - assets/icons/App Icon/
    - assets/icons/home/
    - assets/images/
    - assets/dialogs/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Helvetica
      fonts:
        - asset: assets/fonts/Helvetica.ttf
        - asset: assets/fonts/Helvetica-Bold.ttf
          weight: 700
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins.ttf
        - asset: assets/fonts/Poppins-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
