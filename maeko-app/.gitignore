# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Web related
lib/generated_plugin_registrant.dart

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release
android/app/build.gradle
android/app/src/main/res/mipmap-hdpi/ic_launcher.png
android/app/src/main/res/mipmap-mdpi/ic_launcher.png
android/app/src/main/res/mipmap-xhdpi/ic_launcher.png
android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png
android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png
android/build.gradle
android/gradle.properties
android/gradle/wrapper/gradle-wrapper.properties
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>
lib/Constant/important_variables.dart
