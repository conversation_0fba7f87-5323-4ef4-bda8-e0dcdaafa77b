import 'package:flutter/material.dart';
import 'package:amverton/Model/points_history.dart';  // Import your PointsHistory model

class PointsModel extends ChangeNotifier {
  // List of PointsHistory objects to store the transaction history
  List<PointsHistory> _pointsHistoryList = [];

  // Constructor that accepts a list of PointsHistory objects
  PointsModel([List<PointsHistory> pointsHistoryList = const []]) {
    _pointsHistoryList = pointsHistoryList;
  }

  // Getter to retrieve the most recent transactions (first 3)
  List<PointsHistory> get recentTransactions => _pointsHistoryList.take(3).toList();

  // Getter to retrieve all transactions
  List<PointsHistory> get allTransactions => _pointsHistoryList;

  // Method to add a new PointsHistory to the list
  void addTransaction(PointsHistory pointsHistory) {
    _pointsHistoryList.insert(0, pointsHistory);  // Add at the beginning of the list
    notifyListeners();  // Notify listeners to update UI
  }

  // Method to load more transactions (for example, from an API or local storage)
  void loadMoreTransactions(List<PointsHistory> pointsHistoryList) {
    if (pointsHistoryList.isNotEmpty) {
      _pointsHistoryList.addAll(pointsHistoryList);  // Add more transactions to the list
      notifyListeners();  // Notify listeners to update UI
    }
  }

  // Optional: Method to initialize the transaction history (e.g., for testing or first load)
  void initializeTransactions(List<PointsHistory> initialTransactions) {
    _pointsHistoryList = initialTransactions;
    notifyListeners();
  }
}
