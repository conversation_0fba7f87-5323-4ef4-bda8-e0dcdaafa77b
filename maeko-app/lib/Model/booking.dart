class Booking {
  Booking({
    required this.id,
    required this.name,
    required this.countryCode,
    required this.phone,
    required this.numberOfGuests,
    required this.depositAmount,
    required this.status,
    required this.dateTime,
    // required this.outlet,
    // required this.table,
    // required this.payment,
  });

  int id;
  String name;
  String countryCode;
  String phone;
  int numberOfGuests;
  String depositAmount;
  String status;
  DateTime dateTime;

  // Outlet outlet;
  // String table;
  // List<Payment>? payment;

  factory Booking.fromJson(Map<String, dynamic> json) => Booking(
        id: json["id"],
        name: json["name"],
        countryCode: json["country_code"],
        phone: json["phone"],
        numberOfGuests: json["number_of_guests"],
        depositAmount: json["deposit_amount"],
        status: json["status"],
        dateTime: DateTime.parse(json["datetime"]),
        // outlet: json["outlet"] == null ? null : Outlet.fromJson(json["outlet"]),
        // table: json["table"],
        // payment: json["payments"]==null?null:List<Payment>.from(
        //     json["payments"].map((x) => Payment.fromJson(x))),
      );
}

class Payment {
  Payment({
    required this.id,
  });

  int id;

  factory Payment.fromJson(Map<String, dynamic> json) => Payment(
        id: json["id"],
      );
}
