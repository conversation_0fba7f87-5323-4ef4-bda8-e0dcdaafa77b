import 'package:hive/hive.dart';

part 'app_settings.g.dart';

@HiveType(typeId: 8)
class AppSettings {
  AppSettings({
    required this.logins,
    required this.deliveryOptions,
  });

  @HiveField(0)
  Logins? logins;
  @HiveField(1)
  DeliveryOptions? deliveryOptions;

  factory AppSettings.fromJson(Map<String, dynamic> json) => AppSettings(
        logins: json["logins"] == null ? null : Logins.fromJson(json["logins"]),
        deliveryOptions: json["delivery_options"] == null
            ? null
            : DeliveryOptions.fromJson(json["delivery_options"]),
      );
}

@HiveType(typeId: 9)
class Logins {
  Logins({
    required this.hasPasswordLogin,
    required this.hasSmsLogin,
    required this.hasWhatsAppLogin,
    required this.hasFacebookLogin,
    required this.hasGoogleLogin,
    required this.hasAppleLogin,
  });

  @HiveField(0)
  int hasPasswordLogin;
  @HiveField(1)
  int hasSmsLogin;
  @HiveField(2)
  int hasWhatsAppLogin;
  @HiveField(3)
  int hasFacebookLogin;
  @HiveField(4)
  int hasGoogleLogin;
  @HiveField(5)
  int hasAppleLogin;

  factory Logins.fromJson(Map<String, dynamic> json) => Logins(
        hasPasswordLogin: json["has_password_login"],
        hasSmsLogin: json["has_sms_login"],
        hasWhatsAppLogin: json["has_whatspp_login"],
        hasFacebookLogin: json["has_facebook_login"],
        hasGoogleLogin: json["has_google_login"],
        hasAppleLogin: json["has_apple_login"],
      );
}

@HiveType(typeId: 10)
class DeliveryOptions {
  DeliveryOptions({
    required this.hasDelivery,
    required this.hasPickup,
  });

  @HiveField(0)
  int hasDelivery;
  @HiveField(1)
  int hasPickup;

  factory DeliveryOptions.fromJson(Map<String, dynamic> json) =>
      DeliveryOptions(
        hasDelivery: json["has_delivery"],
        hasPickup: json["has_self_pickup"],
      );
}
