class CreditHistory {
  CreditHistory({
    required this.id,
    required this.credit,
    required this.total,
    required this.paymentStatus,
    required this.createdAt,
  });

  int id;
  String credit;
  String total;
  String paymentStatus;
  DateTime createdAt;

  factory CreditHistory.fromJson(Map<String, dynamic> json) => CreditHistory(
        id: json["id"],
        credit: json["credit"],
        total: json["total"],
        paymentStatus: json["payment_status"],
        createdAt: DateTime.parse(json["created_at"]),
      );
}
