
// Branch Model
class Branch {
  final int id;
  final int countryId;
  final String code;
  final String slug;
  final String name;
  final String tagline;
  final String description;
  final String image1;
  final String image2;
  final String webpageUrl;
  final String bookingPageUrl;
  final int sequence;
  final String createdAt;
  final String updatedAt;
  final String? deletedAt;

  Branch({
    required this.id,
    required this.countryId,
    required this.code,
    required this.slug,
    required this.name,
    required this.tagline,
    required this.description,
    required this.image1,
    required this.image2,
    required this.webpageUrl,
    required this.bookingPageUrl,
    required this.sequence,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  factory Branch.fromJson(Map<String, dynamic> json) {
    return Branch(
      id: json['id'] ?? 0,
      countryId: json['country_id'] ?? 0,
      code: json['code'] ?? '',
      slug: json['slug'] ?? '',
      name: json['name'] ?? '',
      tagline: json['tagline'] ?? '',
      description: json['description'] ?? '',
      image1: json['image_1'] ?? '',
      image2: json['image_2'] ?? '',
      webpageUrl: json['webpage_url'] ?? '',
      bookingPageUrl: json['booking_page_url'] ?? '',
      sequence: json['sequence'] ?? 0,
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      deletedAt: json['deleted_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'country_id': countryId,
      'code': code,
      'slug': slug,
      'name': name,
      'tagline': tagline,
      'description': description,
      'image_1': image1,
      'image_2': image2,
      'webpage_url': webpageUrl,
      'booking_page_url': bookingPageUrl,
      'sequence': sequence,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'deleted_at': deletedAt,
    };
  }

  // Convenience getters
  DateTime? get createdAtAsDateTime {
    try {
      return DateTime.parse(createdAt);
    } catch (e) {
      return null;
    }
  }
  
  List<String> get images => [image1, image2].where((img) => img.isNotEmpty).toList();
}