class Contacts {
  Contacts({
    required this.id,
    required this.countryCode,
    required this.phone,
    required this.name,
  });

  int id;
  String? countryCode;
  String? phone;
  String? name;

  factory Contacts.fromJson(Map<String, dynamic> json) => Contacts(
        id: json["id"],
        countryCode: json["country_code"] == null ? null : json["country_code"],
        phone: json["phone"] == null ? null : json["phone"],
        name: json["name"] == null ? null : json["name"],
      );
}
