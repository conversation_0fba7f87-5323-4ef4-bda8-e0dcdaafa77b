class Product {
  final int restaurantId;
  final String name;
  final String image;
  final int sequence;

  Product({
    required this.restaurantId,
    required this.name,
    required this.image,
    required this.sequence,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      restaurantId: json['restaurant_id'],
      name: json['name'] ?? '',
      image: json['image'] ?? '',
      sequence: json['sequence'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'restaurant_id': restaurantId,
      'name': name,
      'image': image,
      'sequence': sequence,
    };
  }
}
