class Support {
  Support({
    required this.email,
    required this.messenger,
    required this.whatsapp,
    required this.telegram,
  });

  List<SocialMedia>? email;
  List<SocialMedia>? messenger;
  List<SocialMedia>? whatsapp;
  List<SocialMedia>? telegram;

  factory Support.fromJson(Map<String, dynamic> json) => Support(
        email: json["EMAIL"] == null
            ? null
            : List<SocialMedia>.from(
                json["EMAIL"].map((x) => SocialMedia.fromJson(x))),
        messenger: json["MESSENGER"] == null
            ? null
            : List<SocialMedia>.from(
                json["MESSENGER"].map((x) => SocialMedia.fromJson(x))),
        whatsapp: json["WHATSAPP"] == null
            ? null
            : List<SocialMedia>.from(
                json["WHATSAPP"].map((x) => SocialMedia.fromJson(x))),
        telegram: json["TELEGRAM"] == null
            ? null
            : List<SocialMedia>.from(
                json["TELEGRAM"].map((x) => SocialMedia.fromJson(x))),
      );
}

class SocialMedia {
  SocialMedia({
    required this.id,
    required this.startAt,
    required this.endAt,
    required this.platform,
    required this.username,
  });

  int id;
  String startAt;
  String endAt;
  String platform;
  String username;

  factory SocialMedia.fromJson(Map<String, dynamic> json) => SocialMedia(
        id: json["id"],
        startAt: json["start_at"],
        endAt: json["end_at"],
        platform: json["platform"],
        username: json["username"],
      );
}
