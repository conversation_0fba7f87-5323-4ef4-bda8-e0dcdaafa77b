import 'package:hive/hive.dart';

part 'outlet.g.dart';

@HiveType(typeId: 5)
class Outlet {
  Outlet({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.email,
    required this.phone,
    required this.image,
    required this.isMain,
    required this.distance,
    required this.operationHours,
  });

  @HiveField(0)
  int id;
  @HiveField(1)
  String name;
  @HiveField(2)
  String address;
  @HiveField(3)
  String? latitude;
  @HiveField(4)
  String? longitude;
  @HiveField(5)
  String? email;
  @HiveField(6)
  String? phone;
  @HiveField(7)
  String? image;
  @HiveField(8)
  int isMain;
  @HiveField(9)
  double? distance;
  @HiveField(10)
  List<OperationHour>? operationHours;

  factory Outlet.fromJson(Map<String, dynamic> json) => Outlet(
        id: json["id"],
        name: json["name"],
        address: json["address"],
        latitude: json["latitude"] == null ? null : json["latitude"],
        longitude: json["longitude"] == null ? null : json["longitude"],
        email: json["email"] == null ? null : json["email"],
        phone: json["phone"] == null ? null : json["phone"],
        image: json["image"] == null ? null : json["image"],
        isMain: json["is_main"],
        distance: json["distance"] == null ? null : json["distance"].toDouble(),
        operationHours: json["operation_hours"] == null
            ? null
            : List<OperationHour>.from(
                json["operation_hours"].map((x) => OperationHour.fromJson(x))),
      );
}

@HiveType(typeId: 6)
class OperationHour {
  OperationHour({
    required this.id,
    required this.name,
    required this.hours,
  });

  @HiveField(0)
  int id;
  @HiveField(1)
  String name;
  @HiveField(2)
  List<Hour>? hours;

  factory OperationHour.fromJson(Map<String, dynamic> json) => OperationHour(
        id: json["id"],
        name: json["name"],
        hours: json["hours"] == null
            ? null
            : List<Hour>.from(json["hours"].map((x) => Hour.fromJson(x))),
      );
}

@HiveType(typeId: 7)
class Hour {
  Hour({
    required this.id,
    required this.startAt,
    required this.endAt,
  });

  @HiveField(0)
  int id;
  @HiveField(1)
  String startAt;
  @HiveField(2)
  String endAt;

  factory Hour.fromJson(Map<String, dynamic> json) => Hour(
        id: json["id"],
        startAt: json["start_at"],
        endAt: json["end_at"],
      );
}
