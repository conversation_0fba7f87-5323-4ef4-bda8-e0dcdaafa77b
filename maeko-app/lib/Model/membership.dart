
// Membership Model
class Membership {
  final int id;
  final int tier;
  final String name;
  final int minimumSpend;
  final String diningDiscount;
  final String roomDiscount;
  final int expireMonths;
  final String upgradeRewardPoint;
  final int isPaid;
  final String cardImage;

  Membership({
    required this.id,
    required this.tier,
    required this.name,
    required this.minimumSpend,
    required this.diningDiscount,
    required this.roomDiscount,
    required this.expireMonths,
    required this.upgradeRewardPoint,
    required this.isPaid,
    required this.cardImage,
  });

  factory Membership.fromJson(Map<String, dynamic> json) {
    return Membership(
      id: json['id'] ?? 0,
      tier: json['tier'] ?? 0,
      name: json['name'] ?? '',
      minimumSpend: json['minimum_spend'] ?? 0,
      diningDiscount: json['dining_discount'] ?? '0.00',
      roomDiscount: json['room_discount'] ?? '0.00',
      expireMonths: json['expire_months'] ?? 0,
      upgradeRewardPoint: json['upgrade_reward_point'] ?? '0.00',
      isPaid: json['is_paid'] ?? 0,
      cardImage: json['card_image'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tier': tier,
      'name': name,
      'minimum_spend': minimumSpend,
      'dining_discount': diningDiscount,
      'room_discount': roomDiscount,
      'expire_months': expireMonths,
      'upgrade_reward_point': upgradeRewardPoint,
      'is_paid': isPaid,
      'card_image': cardImage,
    };
  }

  // Convenience getters
  double get diningDiscountAsDouble => double.tryParse(diningDiscount) ?? 0.0;
  double get roomDiscountAsDouble => double.tryParse(roomDiscount) ?? 0.0;
  double get upgradeRewardPointAsDouble => double.tryParse(upgradeRewardPoint) ?? 0.0;
  bool get isPaidMembership => isPaid == 1;
}