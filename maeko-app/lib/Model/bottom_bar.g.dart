// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bottom_bar.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BottomBarAdapter extends TypeAdapter<BottomBar> {
  @override
  final int typeId = 4;

  @override
  BottomBar read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BottomBar(
      fields[0] as String,
      fields[1] as String,
      fields[2] as String,
    );
  }

  @override
  void write(BinaryWriter writer, BottomBar obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.name)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.icon);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BottomBarAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
