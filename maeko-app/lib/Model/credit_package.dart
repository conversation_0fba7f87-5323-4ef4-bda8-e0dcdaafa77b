class CreditPackage {
  CreditPackage({
    required this.id,
    required this.name,
    required this.credit,
    required this.price,
    required this.discount,
    required this.discountedPrice,
  });

  int id;
  String name;
  String credit;
  String price;
  String discount;
  String discountedPrice;

  factory CreditPackage.fromJson(Map<String, dynamic> json) => CreditPackage(
        id: json["id"],
        name: json["name"],
        credit: json["credit"],
        price: json["price"],
        discount: json["discount"],
        discountedPrice: json["discounted_price"],
      );
}
