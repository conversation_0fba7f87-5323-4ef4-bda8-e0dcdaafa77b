// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'outlet.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class OutletAdapter extends TypeAdapter<Outlet> {
  @override
  final int typeId = 5;

  @override
  Outlet read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Outlet(
      id: fields[0] as int,
      name: fields[1] as String,
      address: fields[2] as String,
      latitude: fields[3] as String?,
      longitude: fields[4] as String?,
      email: fields[5] as String?,
      phone: fields[6] as String?,
      image: fields[7] as String?,
      isMain: fields[8] as int,
      distance: fields[9] as double?,
      operationHours: (fields[10] as List?)?.cast<OperationHour>(),
    );
  }

  @override
  void write(BinaryWriter writer, Outlet obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.address)
      ..writeByte(3)
      ..write(obj.latitude)
      ..writeByte(4)
      ..write(obj.longitude)
      ..writeByte(5)
      ..write(obj.email)
      ..writeByte(6)
      ..write(obj.phone)
      ..writeByte(7)
      ..write(obj.image)
      ..writeByte(8)
      ..write(obj.isMain)
      ..writeByte(9)
      ..write(obj.distance)
      ..writeByte(10)
      ..write(obj.operationHours);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OutletAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class OperationHourAdapter extends TypeAdapter<OperationHour> {
  @override
  final int typeId = 6;

  @override
  OperationHour read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return OperationHour(
      id: fields[0] as int,
      name: fields[1] as String,
      hours: (fields[2] as List?)?.cast<Hour>(),
    );
  }

  @override
  void write(BinaryWriter writer, OperationHour obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.hours);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OperationHourAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class HourAdapter extends TypeAdapter<Hour> {
  @override
  final int typeId = 7;

  @override
  Hour read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Hour(
      id: fields[0] as int,
      startAt: fields[1] as String,
      endAt: fields[2] as String,
    );
  }

  @override
  void write(BinaryWriter writer, Hour obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.startAt)
      ..writeByte(2)
      ..write(obj.endAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HourAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
