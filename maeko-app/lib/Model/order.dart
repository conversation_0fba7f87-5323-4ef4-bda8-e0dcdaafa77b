import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Model/product.dart';

class Order {
  Order({
    required this.id,
    required this.number,
    required this.subtotal,
    required this.discount,
    required this.tax,
    required this.serviceTax,
    required this.total,
    this.point,
    this.credit,
    this.amountToPay,
    this.deliveryMethod,
    this.deliveryTrackingUrl,
    this.remark,
    required this.status,
    required this.paymentStatus,
    required this.createdAt,
    this.pickupAt,
    this.deliverAt,
    this.updatedAt,
    this.deletedAt,
    required this.paymentMethod,
    this.outlet,
    this.deliveryAddress,
    required this.items,
    this.userPayments,
  });

  int id;
  String number;
  String subtotal;
  String discount;
  String tax;
  String serviceTax;
  String total;
  String? point;
  String? credit;
  String? amountToPay;
  String? deliveryMethod;
  String? deliveryTrackingUrl;
  String? remark;
  String status;
  String paymentStatus;
  DateTime createdAt;
  DateTime? pickupAt;
  DateTime? deliverAt;
  DateTime? updatedAt;
  DateTime? deletedAt;
  PaymentMethod? paymentMethod;
  Outlet? outlet;
  DeliveryAddress? deliveryAddress;
  List<Item>? items;
  List<UserPayment>? userPayments;

  factory Order.fromJson(Map<String, dynamic> json) => Order(
        id: json["id"],
        number: json["number"],
        subtotal: json["subtotal"],
        discount: json["discount"],
        tax: json["tax"],
        serviceTax: json["service_tax"],
        total: json["total"],
        point: json["point"],
        credit: json["credit"],
        amountToPay: json["amount_to_pay"],
        deliveryMethod: json["delivery_method"],
        deliveryTrackingUrl: json["delivery_tracking_url"],
        remark: json["remark"],
        status: json["status"],
        paymentStatus: json["payment_status"],
        createdAt: DateTime.parse(json["created_at"]),
        pickupAt: json["pickup_at"] == null
            ? null
            : DateTime.parse(json["pickup_at"]),
        deliverAt: json["deliver_at"] == null
            ? null
            : DateTime.parse(json["deliver_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        deletedAt: json["deleted_at"] == null
            ? null
            : DateTime.parse(json["deleted_at"]),
        paymentMethod: json["payment_method"] == null
            ? null
            : PaymentMethod.fromJson(json["payment_method"]),
        outlet: json["outlet"] == null ? null : Outlet.fromJson(json["outlet"]),
        deliveryAddress: json["delivery_address"] == null
            ? null
            : DeliveryAddress.fromJson(json["delivery_address"]),
        items: json["items"] == null
            ? null
            : List<Item>.from(json["items"].map((x) => Item.fromJson(x))),
        userPayments: json["user_payments"] == null
            ? null
            : List<UserPayment>.from(
                json["user_payments"].map((x) => UserPayment.fromJson(x))),
      );
}

class Item {
  Item({
    required this.id,
    required this.unitPrice,
    required this.quantity,
    required this.price,
    this.remark,
    this.product,
    this.bundleItems,
    this.modifierItems,
  });

  int id;
  String unitPrice;
  int quantity;
  String price;
  String? remark;
  Product? product;
  List<BundleItem>? bundleItems;
  List<ModifierItem>? modifierItems;

  factory Item.fromJson(Map<String, dynamic> json) => Item(
        id: json["id"],
        unitPrice: json["unit_price"],
        quantity: json["quantity"],
        price: json["price"],
        remark: json["remark"],
        product:
            json["product"] == null ? null : Product.fromJson(json["product"]),
        bundleItems: json["bundle_items"] == null
            ? null
            : List<BundleItem>.from(
                json["bundle_items"].map((x) => BundleItem.fromJson(x))),
        modifierItems: json["modifier_options"] == null
            ? null
            : List<ModifierItem>.from(
                json["modifier_options"].map((x) => ModifierItem.fromJson(x))),
      );
}

class PaymentMethod {
  final int id;
  final String code;
  final String name;

  PaymentMethod(this.id, this.code, this.name);

  factory PaymentMethod.fromJson(Map<String, dynamic> json) {
    return PaymentMethod(json['id'], json['code'], json['name']);
  }
}

class DeliveryAddress {
  DeliveryAddress({
    required this.id,
    required this.address1,
    required this.address2,
    required this.postcode,
    required this.city,
    required this.state,
    required this.country,
    required this.name,
    required this.phone,
    required this.latitude,
    required this.longitude,
    required this.createdAt,
    required this.updatedAt,
  });

  int id;
  String address1;
  String? address2;
  String postcode;
  String city;
  String state;
  String country;
  String name;
  String phone;
  String latitude;
  String longitude;
  DateTime createdAt;
  DateTime updatedAt;

  factory DeliveryAddress.fromJson(Map<String, dynamic> json) =>
      DeliveryAddress(
        id: json["id"],
        address1: json["address_1"],
        address2: json["address_2"],
        postcode: json["postcode"],
        city: json["city"],
        state: json["state"],
        country: json["country"],
        name: json["name"],
        phone: json["phone"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        createdAt: DateTime.parse(json["created_at"]),
        updatedAt: DateTime.parse(json["updated_at"]),
      );
}

class UserPayment {
  UserPayment({
    required this.id,
    required this.number,
    required this.amount,
    required this.status,
    required this.paidAt,
    required this.createdAt,
    required this.updatedAt,
  });

  int id;
  String number;
  String amount;
  String status;
  DateTime paidAt;
  DateTime createdAt;
  DateTime updatedAt;

  factory UserPayment.fromJson(Map<String, dynamic> json) => UserPayment(
        id: json["id"],
        number: json["number"],
        amount: json["amount"],
        status: json["status"],
        paidAt: DateTime.parse(json["paid_at"]),
        createdAt: DateTime.parse(json["created_at"]),
        updatedAt: DateTime.parse(json["updated_at"]),
      );
}
