// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_settings.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AppSettingsAdapter extends TypeAdapter<AppSettings> {
  @override
  final int typeId = 8;

  @override
  AppSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AppSettings(
      logins: fields[0] as Logins?,
      deliveryOptions: fields[1] as DeliveryOptions?,
    );
  }

  @override
  void write(BinaryWriter writer, AppSettings obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.logins)
      ..writeByte(1)
      ..write(obj.deliveryOptions);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AppSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LoginsAdapter extends TypeAdapter<Logins> {
  @override
  final int typeId = 9;

  @override
  Logins read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Logins(
      hasPasswordLogin: fields[0] as int,
      hasSmsLogin: fields[1] as int,
      hasWhatsAppLogin: fields[2] as int,
      hasFacebookLogin: fields[3] as int,
      hasGoogleLogin: fields[4] as int,
      hasAppleLogin: fields[5] as int,
    );
  }

  @override
  void write(BinaryWriter writer, Logins obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.hasPasswordLogin)
      ..writeByte(1)
      ..write(obj.hasSmsLogin)
      ..writeByte(2)
      ..write(obj.hasWhatsAppLogin)
      ..writeByte(3)
      ..write(obj.hasFacebookLogin)
      ..writeByte(4)
      ..write(obj.hasGoogleLogin)
      ..writeByte(5)
      ..write(obj.hasAppleLogin);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LoginsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class DeliveryOptionsAdapter extends TypeAdapter<DeliveryOptions> {
  @override
  final int typeId = 10;

  @override
  DeliveryOptions read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return DeliveryOptions(
      hasDelivery: fields[0] as int,
      hasPickup: fields[1] as int,
    );
  }

  @override
  void write(BinaryWriter writer, DeliveryOptions obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.hasDelivery)
      ..writeByte(1)
      ..write(obj.hasPickup);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeliveryOptionsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
