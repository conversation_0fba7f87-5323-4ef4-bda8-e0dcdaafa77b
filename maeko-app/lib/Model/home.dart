import 'package:amverton/Model/apiUser.dart';
import 'package:amverton/Model/restaurant.dart';
import 'package:amverton/Model/branch.dart';
import 'package:amverton/Model/membership.dart';

class Home {
  Home({
    this.user,
    this.membership,
    this.restaurants,
    this.branches,
  });

  User? user;
  Membership? membership;
  List<Restaurant>? restaurants;
  List<Branch>? branches;

  factory Home.fromJson(Map<String, dynamic> json) => Home(
      user: json['user'] != null ? User.fromJson(json['user']) : null,
      membership: json['membership'] != null ? Membership.fromJson(json['membership']) : null,
      restaurants: (json['restaurants'] as List<dynamic>?)
          ?.map((item) => Restaurant.fromJson(item))
          .toList(),
      branches: (json['branches'] as List<dynamic>?)
          ?.map((item) => Branch.fromJson(item))
          .toList(),
      );

  Map<String, dynamic> toJson() => {
    'user': user?.toJson(),
    'membership': membership?.toJson(),
    'restaurants': restaurants?.map((item) => item.toJson()).toList(),
    'branches': branches?.map((item) => item.toJson()).toList(),
  };
}