class PointsHistory {
  PointsHistory({
    required this.id,
    required this.before,
    required this.difference,
    required this.after,
    required this.description,
    required this.createdAt,
    required this.type,
  });

  int id;
  String before;
  String difference;
  String after;
  String description;
  DateTime createdAt;
  String type;

  factory PointsHistory.fromJson(Map<String, dynamic> json) => PointsHistory(
        id: json["id"],
        before: json["before"],
        difference: json["difference"],
        after: json["after"],
        description: json["description"],
        createdAt: DateTime.parse(json["created_at"]),
        type: json["type"],
      );
}
