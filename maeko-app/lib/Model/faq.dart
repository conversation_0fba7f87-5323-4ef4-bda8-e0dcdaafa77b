class Faq {
  Faq({
    required this.categories,
    required this.questions,
  });

  List<Category>? categories;
  List<Question>? questions;

  factory Faq.fromJson(Map<String, dynamic> json) => Faq(
        categories: json["categories"] == null
            ? null
            : List<Category>.from(
                json["categories"].map((x) => Category.fromJson(x))),
        questions: json["questions"] == null
            ? null
            : List<Question>.from(
                json["questions"].map((x) => Question.fromJson(x))),
      );
}

class Category {
  Category({
    required this.id,
    required this.name,
    required this.image,
    required this.questions,
  });

  int id;
  String name;
  String? image;
  List<Question>? questions;

  factory Category.fromJson(Map<String, dynamic> json) => Category(
        id: json["id"],
        name: json["name"],
        image: json["image"] == null ? null : json["image"],
        questions: json["questions"] == null
            ? null
            : List<Question>.from(
                json["questions"].map((x) => Question.fromJson(x))),
      );
}

class Question {
  Question({
    required this.id,
    required this.questionCategoryId,
    required this.question,
    required this.answer,
    required this.category,
  });

  int id;
  int questionCategoryId;
  String question;
  String answer;
  Category? category;

  factory Question.fromJson(Map<String, dynamic> json) => Question(
        id: json["id"],
        questionCategoryId: json["question_category_id"],
        question: json["question"],
        answer: json["answer"],
        category: json["category"] == null
            ? null
            : Category.fromJson(json["category"]),
      );
}
