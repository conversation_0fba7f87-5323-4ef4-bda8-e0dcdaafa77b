import 'package:amverton/Model/product.dart';

class Cart {
  Cart({
    required this.id,
    required this.quantity,
    required this.remark,
    required this.price,
    required this.product,
  });

  int id;
  int quantity;
  String? remark;
  String price;
  Product product;

  factory Cart.fromJson(Map<String, dynamic> json) => Cart(
      id: json["id"],
      quantity: json["quantity"],
      remark: json["remark"] == null ? null : json["remark"],
      price: json["price"],
      product: Product.fromJson(json["product"]));
}
