import 'package:amverton/Model/points_history.dart';


class Scanqr {
  Scanqr({
    this.qrcode,
    this.points,
  });

  String? qrcode;
  List<PointsHistory>? points;

  factory Scanqr.fromJson(Map<String, dynamic> json) => Scanqr(
        qrcode: json["qrcode"] == null ? null : json["qrcode"],
        points: json["points"] == null
            ? null
            : List<PointsHistory>.from(
                json["points"].map((x) => PointsHistory.fromJson(x)))
      );
}
