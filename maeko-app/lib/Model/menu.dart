import 'package:amverton/Model/product.dart';

class MenuCategory {
  MenuCategory({
    required this.id,
    required this.name,
    required this.sequence,
    required this.icon,
    required this.banner,
    required this.products,
  });

  int id;
  String name;
  int sequence;
  String? icon;
  String? banner;
  List<Product> products;

  factory MenuCategory.fromJson(Map<String, dynamic> json) => MenuCategory(
        id: json["id"],
        name: json["name"],
        sequence: json["sequence"],
        icon: json["icon"] == null ? null : json["icon"],
        banner: json["banner"] == null ? null : json["banner"],
        products: List<Product>.from(
            json["products"].map((x) => Product.fromJson(x))),
      );
}
