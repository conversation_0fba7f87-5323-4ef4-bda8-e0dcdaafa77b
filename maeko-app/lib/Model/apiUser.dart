import 'package:amverton/Model/membership.dart';

// User Model
class User {
  final int id;
  final int? referrerId;
  final String code;
  final String firstName;
  final String lastName;
  final String phone;
  final String username;
  final String dateOfBirth;
  final String? gender;
  final String? race;
  final String point;
  final String totalSpend;
  final String? membershipExpireOn;
  final String createdAt;
  final String updatedAt;
  final String? deletedAt;
  final Membership membership;

  User({
    required this.id,
    this.referrerId,
    required this.code,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.username,
    required this.dateOfBirth,
    this.gender,
    this.race,
    required this.point,
    required this.totalSpend,
    this.membershipExpireOn,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.membership,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? 0,
      referrerId: json['referrer_id'],
      code: json['code'] ?? '',
      firstName: json['first_name'] ?? '',
      lastName: json['last_name'] ?? '',
      phone: json['phone'] ?? '',
      username: json['username'] ?? '',
      dateOfBirth: json['date_of_birth'] ?? '',
      gender: json['gender'],
      race: json['race'],
      point: json['point'] ?? '0.00',
      totalSpend: json['total_spend'] ?? '0.00',
      membershipExpireOn: json['membership_expire_on'],
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      deletedAt: json['deleted_at'],
      membership: Membership.fromJson(json['membership'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'referrer_id': referrerId,
      'code': code,
      'first_name': firstName,
      'last_name': lastName,
      'phone': phone,
      'username': username,
      'date_of_birth': dateOfBirth,
      'gender': gender,
      'race': race,
      'point': point,
      'total_spend': totalSpend,
      'membership_expire_on': membershipExpireOn,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'deleted_at': deletedAt,
      'membership': membership.toJson(),
    };
  }

  // Convenience getters
  String get fullName => '$firstName $lastName';
  
  DateTime? get dateOfBirthAsDateTime {
    try {
      return DateTime.parse(dateOfBirth);
    } catch (e) {
      return null;
    }
  }
  
  DateTime? get createdAtAsDateTime {
    try {
      return DateTime.parse(createdAt);
    } catch (e) {
      return null;
    }
  }
  
  double get pointAsDouble => double.tryParse(point) ?? 0.0;
  double get totalSpendAsDouble => double.tryParse(totalSpend) ?? 0.0;
}

