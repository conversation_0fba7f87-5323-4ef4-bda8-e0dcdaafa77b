class CheckInEvent {
  CheckInEvent({
    required this.id,
    required this.title,
    required this.startOn,
    required this.endOn,
    required this.type,
    required this.rewards,
  });

  int id;
  String title;
  String startOn;
  String endOn;
  String type;
  List<CheckInReward>? rewards;

  factory CheckInEvent.fromJson(Map<String, dynamic> json) => CheckInEvent(
        id: json["id"],
        title: json["title"],
        startOn: json["start_on"],
        endOn: json["end_on"],
        type: json["type"],
        rewards: json["rewards"] == null
            ? null
            : List<CheckInReward>.from(
                json["rewards"].map((x) => CheckInReward.fromJson(x))),
      );
}

class CheckInReward {
  CheckInReward({
    required this.id,
    required this.day,
    required this.label,
    required this.type,
    required this.title,
    required this.point,
    required this.image,
    required this.isClaimed,
  });

  int id;
  int day;
  String label;
  String type;
  String title;
  String point;
  String? image;
  int isClaimed;

  factory CheckInReward.fromJson(Map<String, dynamic> json) => CheckInReward(
        id: json["id"],
        day: json["day"],
        label: json["label"],
        type: json["type"],
        title: json["title"],
        point: json["point"],
        image: json["image"] == null ? null : json["image"],
        isClaimed: json["is_claimed"],
      );
}
