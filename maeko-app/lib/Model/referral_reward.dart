import 'package:amverton/Model/voucher.dart';

class ReferralReward {
  ReferralReward({
    required this.id,
    required this.invite,
    required this.type,
    required this.point,
    required this.sequence,
    required this.isClaimed,
    required this.isEligible,
    required this.voucher,
  });

  int id;
  int invite;
  String type;
  String point;
  int sequence;
  bool isClaimed;
  bool isEligible;
  Voucher? voucher;

  factory ReferralReward.fromJson(Map<String, dynamic> json) => ReferralReward(
        id: json["id"],
        invite: json["invite"],
        type: json["type"],
        point: json["point"],
        sequence: json["sequence"],
        isClaimed: json["is_claimed"],
        isEligible: json["is_eligible"],
        voucher:
            json["voucher"] == null ? null : Voucher.fromJson(json["voucher"]),
      );
}
