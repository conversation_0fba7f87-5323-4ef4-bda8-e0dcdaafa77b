// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'address.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AddressAdapter extends TypeAdapter<Address> {
  @override
  final int typeId = 11;

  @override
  Address read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Address(
      id: fields[0] as int,
      address1: fields[1] as String,
      address2: fields[2] as String?,
      postcode: fields[3] as String,
      city: fields[4] as String,
      name: fields[5] as String,
      countryCode: fields[6] as String,
      phone: fields[7] as String,
      isDefault: fields[8] as int,
      country: fields[9] as Country?,
      state: fields[10] as States?,
    );
  }

  @override
  void write(BinaryWriter writer, Address obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.address1)
      ..writeByte(2)
      ..write(obj.address2)
      ..writeByte(3)
      ..write(obj.postcode)
      ..writeByte(4)
      ..write(obj.city)
      ..writeByte(5)
      ..write(obj.name)
      ..writeByte(6)
      ..write(obj.countryCode)
      ..writeByte(7)
      ..write(obj.phone)
      ..writeByte(8)
      ..write(obj.isDefault)
      ..writeByte(9)
      ..write(obj.country)
      ..writeByte(10)
      ..write(obj.state);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AddressAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
