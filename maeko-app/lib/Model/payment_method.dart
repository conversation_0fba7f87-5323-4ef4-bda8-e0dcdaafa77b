class PaymentMethod {
  final int id;
  final String code;
  final String name;
  final List<Brand> brands;

  PaymentMethod(this.id, this.code, this.name, this.brands);

  @override
  String toString() {
    return 'PaymentMethod{id: $id, code: $code, name: $name, brands: ${brands.join(', ')}}';
  }

  factory PaymentMethod.fromJson(Map<String, dynamic> json) {
    var brands = (json['brands'] as List)
        .map((brandJson) => Brand.fromJson(brandJson))
        .toList();
    return PaymentMethod(json['id'], json['code'], json['name'], brands);
  }
}

class Brand {
  final int id;
  final String code;
  final String name;

  Brand(this.id, this.code, this.name);

  @override
  String toString() {
    return 'Brand{id: $id, code: $code, name: $name}';
  }

  factory Brand.fromJson(Map<String, dynamic> json) {
    return Brand(json['id'], json['code'], json['name']);
  }
}
