import 'package:amverton/Model/address.dart';
import 'package:amverton/Model/cart/brand.dart';
import 'package:amverton/Model/cart/payment_method.dart';

class CartPageVariable {
  String selectedVoucherIndex;
  String actualTotal;

  //point availale can be credit available also, is just the naming
  String pointType;
  double pointAvailable;
  double pointUsable;
  String pointUsed;
  List<String> schedules;
  String selectedSchedule;
  int selectedDeliverMethod; // 0 = Delivery; 1 = Pickup
  List<Address> addressList;
  int selectedAddressId;
  List<PaymentMethod> paymentMethods;
  List<Brand> paymentBrands;
  PaymentMethod? selectedPaymentMethod;
  Brand? selectedPaymentBrand;

  CartPageVariable({
    this.selectedVoucherIndex = "",
    this.actualTotal = "",
    this.pointType = "",
    this.pointAvailable = 0.00,
    this.pointUsable = 0.00,
    this.pointUsed = "",
    this.schedules = const [],
    this.selectedSchedule = "",
    this.selectedDeliverMethod = 0,
    this.addressList = const [],
    this.selectedAddressId = 0,
    this.paymentMethods = const [],
    this.paymentBrands = const [],
    this.selectedPaymentMethod,
    this.selectedPaymentBrand,
  });

  @override
  String toString() {
    return 'CartPageVariable{selectedVoucherIndex: $selectedVoucherIndex, actualTotal: $actualTotal, pointAvailable: $pointAvailable, pointUsable: $pointUsable, schedules: $schedules, selectedSchedule: $selectedSchedule, selectedDeliverMethod: $selectedDeliverMethod, addressList: $addressList, selectedAddressId: $selectedAddressId, paymentMethods: $paymentMethods, paymentBrands: $paymentBrands, selectedPaymentMethod: $selectedPaymentMethod, selectedPaymentBrand: $selectedPaymentBrand}';
  }
}
