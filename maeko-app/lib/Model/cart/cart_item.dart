import 'dart:convert';

import 'package:amverton/Model/product.dart';

class CartItem {
  int id;
  int quantity;
  String? remark;
  String price;
  Product product;

  CartItem({
    required this.id,
    required this.quantity,
    this.remark,
    required this.price,
    required this.product,
  });

  factory CartItem.fromJson(Map<String, dynamic> json) {
    return CartItem(
      id: json['id'],
      quantity: json['quantity'],
      remark: json['remark'],
      price: json['price'],
      product: Product.fromJson(json['product']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'quantity': quantity,
      'remark': remark,
      'price': price,
      'product': jsonEncode(product),
    };
  }
}
