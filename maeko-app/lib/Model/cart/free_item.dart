class FreeItem {
  int id;
  String spu;
  String name;
  String unit;
  int stock;
  String price;
  String description;
  String image;
  String type;

  FreeItem({
    required this.id,
    required this.spu,
    required this.name,
    required this.unit,
    required this.stock,
    required this.price,
    required this.description,
    required this.image,
    required this.type,
  });

  factory FreeItem.fromJson(Map<String, dynamic> json) {
    return FreeItem(
      id: json['id'],
      spu: json['spu'],
      name: json['name'],
      unit: json['unit'] ?? '',
      stock: json['stock'],
      price: json['price'],
      description: json['description'] ?? '',
      image: json['image'],
      type: json['type'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'spu': spu,
      'name': name,
      'unit': unit,
      'stock': stock,
      'price': price,
      'description': description,
      'image': image,
      'type': type,
    };
  }
}
