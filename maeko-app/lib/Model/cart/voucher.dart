class Voucher {
  int id;
  String code;
  String title;
  String description;
  int maximumRedemption;
  DateTime startOn;
  DateTime endOn;
  String image;
  Pivot pivot;

  Voucher({
    required this.id,
    required this.code,
    required this.title,
    required this.description,
    required this.maximumRedemption,
    required this.startOn,
    required this.endOn,
    required this.image,
    required this.pivot,
  });

  factory Voucher.fromJson(Map<String, dynamic> json) {
    return Voucher(
      id: json['id'],
      code: json['code'],
      title: json['title'],
      description: json['description'],
      maximumRedemption: json['maximum_redemption'],
      startOn: DateTime.parse(json['start_on']),
      endOn: DateTime.parse(json['end_on']),
      image: json['image'] ?? "",
      pivot: Pivot.fromJson(json['pivot']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'title': title,
      'description': description,
      'maximum_redemption': maximumRedemption,
      'start_on': startOn.toIso8601String(),
      'end_on': endOn.toIso8601String(),
      'image': image,
      'pivot': pivot.toJson(),
    };
  }
}

class Pivot {
  int? userId;
  int? voucherId;
  DateTime acquiredAt;
  DateTime? usedAt;
  String type;

  Pivot({
    required this.userId,
    required this.voucherId,
    required this.acquiredAt,
    this.usedAt,
    required this.type,
  });

  factory Pivot.fromJson(Map<String, dynamic> json) {
    return Pivot(
      userId: json['user_id'] ?? null,
      voucherId: json['voucher_id'] ?? null,
      acquiredAt: DateTime.parse(json['acquired_at']),
      usedAt: json['used_at'] != null ? DateTime.parse(json['used_at']) : null,
      type: json['type'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'voucher_id': voucherId,
      'acquired_at': acquiredAt.toIso8601String(),
      'used_at': usedAt?.toIso8601String(),
      'type': type,
    };
  }
}
