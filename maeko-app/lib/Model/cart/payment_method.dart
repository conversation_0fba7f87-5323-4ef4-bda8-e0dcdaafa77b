import 'package:amverton/Model/cart/brand.dart';

class PaymentMethod {
  int id;
  String code;
  String name;
  List<Brand> brands;

  PaymentMethod({
    required this.id,
    required this.code,
    required this.name,
    required this.brands,
  });

  factory PaymentMethod.fromJson(Map<String, dynamic> json) {
    return PaymentMethod(
      id: json['id'],
      code: json['code'],
      name: json['name'],
      brands: List<Brand>.from(json['brands'].map((x) => Brand.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'brands': List<dynamic>.from(brands.map((x) => x.toJson())),
    };
  }
}
