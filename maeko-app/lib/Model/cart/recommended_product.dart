class RecommendedProduct {
  final int id;
  final String spu;
  final String name;
  final String unit;
  final int stock;
  final String price;
  final String description;
  final String image;
  final String type;

  RecommendedProduct({
    required this.id,
    required this.spu,
    required this.name,
    required this.unit,
    required this.stock,
    required this.price,
    required this.description,
    required this.image,
    required this.type,
  });

  factory RecommendedProduct.fromJson(Map<String, dynamic> json) {
    return RecommendedProduct(
      id: json['id'],
      spu: json['spu'],
      name: json['name'],
      unit: json['unit'],
      stock: json['stock'],
      price: json['price'],
      description: json['description'],
      image: json['image'],
      type: json['type'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'spu': spu,
      'name': name,
      'unit': unit,
      'stock': stock,
      'price': price,
      'description': description,
      'image': image,
      'type': type,
    };
  }
}
