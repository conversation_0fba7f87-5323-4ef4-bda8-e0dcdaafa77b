import 'package:amverton/Model/outlet.dart' as GlobalOutlet;

class Outlet {
  int id;
  String name;
  String address;
  String latitude;
  String longitude;
  String? email;
  String? phone;
  String? bookingEmail;
  String? bookingPhone;
  String image;
  int isMain;

  Outlet({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    this.email,
    this.phone,
    this.bookingEmail,
    this.bookingPhone,
    required this.image,
    required this.isMain,
  });

  factory Outlet.fromJson(Map<String, dynamic> json) {
    return Outlet(
      id: json['id'],
      name: json['name'],
      address: json['address'],
      latitude: json['latitude'],
      longitude: json['longitude'],
      email: json['email'],
      phone: json['phone'],
      bookingEmail: json['booking_email'],
      bookingPhone: json['booking_phone'],
      image: json['image'],
      isMain: json['is_main'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'email': email,
      'phone': phone,
      'booking_email': bookingEmail,
      'booking_phone': bookingPhone,
      'image': image,
      'is_main': isMain,
    };
  }

  // Method to convert cart Outlet to global Outlet
  GlobalOutlet.Outlet toGlobalOutlet() {
    return GlobalOutlet.Outlet(
      id: this.id,
      name: this.name,
      address: this.address,
      latitude: this.latitude,
      longitude: this.longitude,
      email: this.email,
      phone: this.phone,
      image: this.image,
      isMain: this.isMain,
      distance: null,
      operationHours: null,
    );
  }
}
