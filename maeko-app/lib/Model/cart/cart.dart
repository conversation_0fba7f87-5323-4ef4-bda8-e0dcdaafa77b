import 'package:amverton/Model/address.dart';
import 'package:amverton/Model/cart/cart_item.dart';
import 'package:amverton/Model/cart/free_item.dart';
import 'package:amverton/Model/cart/outlet.dart';
import 'package:amverton/Model/cart/payment_method.dart';
import 'package:amverton/Model/cart/recommended_product.dart';
import 'package:amverton/Model/cart/voucher.dart';
import 'package:amverton/Model/country.dart';

class Cart {
  List<CartItem> items;
  FreeItem? freeItem;
  String subtotal;
  String discount;
  String tax;
  String serviceTax;
  String total;
  String checkoutWith;
  String creditAvailable;
  String creditUsable;
  String pointAvailable;
  String pointUsable;
  String pointEarned;
  bool voucherError;
  String voucherMessage;
  List<Address> addresses;
  Address? defaultAddress;
  Outlet outlet;
  List<String>? pickupHours;
  List<String>? deliveryHours;
  List<PaymentMethod> paymentMethods;
  List<Voucher> vouchers;
  List<RecommendedProduct> recommendedProducts;

  Cart({
    this.items = const [],
    this.freeItem,
    this.subtotal = '',
    this.discount = '',
    this.tax = '',
    this.serviceTax = '',
    this.total = '',
    this.checkoutWith = '',
    this.creditAvailable = '',
    this.creditUsable = '',
    this.pointAvailable = '',
    this.pointUsable = '',
    this.pointEarned = '',
    this.voucherError = false,
    this.voucherMessage = '',
    this.addresses = const [],
    Address? defaultAddress,
    Outlet? outlet,
    this.pickupHours = const [],
    this.deliveryHours = const [],
    this.paymentMethods = const [],
    this.vouchers = const [],
    this.recommendedProducts = const [],
  })  : defaultAddress = defaultAddress ??
            Address(
              id: 0,
              address1: '',
              address2: null,
              postcode: '',
              city: '',
              name: '',
              countryCode: '',
              phone: '',
              isDefault: 0,
              country: null,
              state: null,
            ),
        outlet = outlet ??
            Outlet(
              id: 0,
              name: '',
              address: '',
              latitude: '',
              longitude: '',
              email: null,
              phone: null,
              bookingEmail: null,
              bookingPhone: null,
              image: '',
              isMain: 0,
            );

  factory Cart.fromJson(Map<String, dynamic> json) {
    return Cart(
      items:
          List<CartItem>.from(json['items'].map((x) => CartItem.fromJson(x))),
      freeItem: json['free_item'] != null
          ? FreeItem.fromJson(json['free_item'])
          : null,
      subtotal: json['subtotal'],
      discount: json['discount'],
      tax: json['tax'],
      serviceTax: json['service_tax'],
      total: json['total'],
      checkoutWith: json['checkout_with'],
      creditAvailable: json['credit_available'],
      creditUsable: json['credit_usable'],
      pointAvailable: json['point_available'],
      pointUsable: json['point_usable'],
      pointEarned: json['point_earned'],
      voucherError: json['voucher_error'],
      voucherMessage: json['voucher_message'],
      addresses: json['addresses'] != null
          ? List<Address>.from(
              (json['addresses'] as List).map((x) => Address.fromJson(x)))
          : [],
      defaultAddress: json['default_address'] != null
          ? Address.fromJson(json['default_address'])
          : null,
      outlet: json['outlet'] != null ? Outlet.fromJson(json['outlet']) : null,
      pickupHours: json['pickup_hours'] != null
          ? List<String>.from(json['pickup_hours'])
          : null,
      deliveryHours: json['delivery_hours'] != null
          ? List<String>.from(json['delivery_hours'])
          : null,
      paymentMethods: json['payment_methods'] != null
          ? List<PaymentMethod>.from(
              json['payment_methods'].map((x) => PaymentMethod.fromJson(x)))
          : [],
      vouchers: json['vouchers'] != null
          ? List<Voucher>.from(json['vouchers'].map((x) => Voucher.fromJson(x)))
          : [],
      recommendedProducts: json['recommended_items'] != null
          ? List<RecommendedProduct>.from(json['recommended_items']
              .map((x) => RecommendedProduct.fromJson(x)))
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'items': List<dynamic>.from(items.map((x) => x.toJson())),
      'free_item': freeItem?.toJson(),
      'subtotal': subtotal,
      'discount': discount,
      'tax': tax,
      'service_tax': serviceTax,
      'total': total,
      'credit_available': creditAvailable,
      'credit_usable': creditUsable,
      'voucher_error': voucherError,
      'voucher_message': voucherMessage,
      'addresses': List<dynamic>.from(addresses.map((x) => x.toJson())),
      'default_address': defaultAddress,
      'outlet': outlet.toJson(),
      'pickup_hours': pickupHours,
      'delivery_hours': deliveryHours,
      'payment_methods':
          List<dynamic>.from(paymentMethods.map((x) => x.toJson())),
      'vouchers': List<dynamic>.from(vouchers.map((x) => x.toJson())),
    };
  }
}
