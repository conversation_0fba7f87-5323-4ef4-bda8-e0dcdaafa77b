class Voucher {
  Voucher({
    this.id = 0,
    this.code = '',
    this.title = '',
    this.description = '',
    this.maximumRedemption = 0,
    this.startOn = '',
    this.endOn = '',
    this.image,
    this.qrCode,
    this.pivot,
  });

  int id;
  String code;
  String title;
  String description;
  int maximumRedemption;
  String startOn;
  String endOn;
  String? image;
  String? qrCode;
  Pivot? pivot;

  factory Voucher.fromJson(Map<String, dynamic> json) => Voucher(
        id: json["id"],
        code: json["code"],
        title: json["title"],
        description: json["description"],
        maximumRedemption: json["maximum_redemption"],
        startOn: json["start_on"],
        endOn: json["end_on"],
        image: json["image"] == null ? null : json["image"],
        qrCode: json["qr_code"] == null ? null : json["qr_code"],
        pivot: json["pivot"] == null ? null : Pivot.fromJson(json["pivot"]),
      );
}

class Pivot {
  Pivot({
    required this.userId,
    required this.voucherId,
    required this.acquiredAt,
    required this.usedAt,
    required this.type,
    required this.code,
  });

  int? userId;
  int? voucherId;
  DateTime? acquiredAt;
  DateTime? usedAt;
  String? type;
  String? code;

  factory Pivot.fromJson(Map<String, dynamic> json) => Pivot(
        userId: json["user_id"] ?? null,
        voucherId: json["voucher_id"] ?? null,
        acquiredAt: json["acquired_at"] == null
            ? null
            : DateTime.parse(json["acquired_at"]),
        usedAt:
            json["used_at"] == null ? null : DateTime.parse(json["used_at"]),
        type: json["type"] == null ? null : json["type"],
        code: json["code"] == null ? null : json["code"],
      );
}
