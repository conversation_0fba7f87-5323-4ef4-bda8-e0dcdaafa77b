import 'package:hive/hive.dart';
import 'package:amverton/Model/membership.dart';
part 'user.g.dart';

@HiveType(typeId: 0)
class User {
  User({
    this.id = 0,
    this.referrerId,
    this.code = '',
    this.firstName = '',
    this.lastName = '',
    this.phone = '',
    this.username = '',
    this.dateOfBirth,
    this.gender,
    this.race,
    this.point = '0.00',
    this.totalSpend = '0.00',
    this.membershipExpireOn,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.membership,
  });

  @HiveField(0)
  int id;
  @HiveField(1)
  int? referrerId;
  @HiveField(2)
  String code;
  @HiveField(3)
  String firstName;
  @HiveField(4)
  String lastName;
  @HiveField(5)
  String phone;
  @HiveField(6)
  String username;
  @HiveField(7)
  DateTime? dateOfBirth;
  @HiveField(8)
  String? gender;
  @HiveField(9)
  String? race;
  @HiveField(10)
  String point;
  @HiveField(11)
  String totalSpend;
  @HiveField(12)
  DateTime? membershipExpireOn;
  @HiveField(13)
  DateTime? createdAt;
  @HiveField(14)
  DateTime? updatedAt;
  @HiveField(15)
  DateTime? deletedAt;
  @HiveField(16)
  Membership? membership;

  // Convenience getters
  String get fullName => '$firstName $lastName';
  double get pointAsDouble => double.tryParse(point) ?? 0.0;
  double get totalSpendAsDouble => double.tryParse(totalSpend) ?? 0.0;

  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json["id"] ?? 0,
        referrerId: json["referrer_id"],
        code: json["code"] ?? '',
        firstName: json["first_name"] ?? '',
        lastName: json["last_name"] ?? '',
        phone: json["phone"] ?? '',
        username: json["username"] ?? '',
        dateOfBirth: json["date_of_birth"] == null
            ? null
            : DateTime.tryParse(json["date_of_birth"]),
        gender: json["gender"],
        race: json["race"],
        point: json["point"] ?? '0.00',
        totalSpend: json["total_spend"] ?? '0.00',
        membershipExpireOn: json["membership_expire_on"] == null
            ? null
            : DateTime.tryParse(json["membership_expire_on"]),
        createdAt: json["created_at"] == null
            ? null
            : DateTime.tryParse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.tryParse(json["updated_at"]),
        deletedAt: json["deleted_at"] == null
            ? null
            : DateTime.tryParse(json["deleted_at"]),
        membership: json["membership"] == null
            ? null
            : Membership.fromJson(json["membership"]),
      );

}