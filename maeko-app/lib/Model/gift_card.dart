class GiftCard {
  GiftCard({
    required this.id,
    required this.receiverName,
    required this.senderName,
    required this.message,
    required this.amount,
    required this.image,
    required this.createdAt,
    required this.redeemedAt,
    required this.cancelledAt,
  });

  int id;
  String receiverName;
  String senderName;
  String? message;
  String amount;
  String image;
  DateTime? createdAt;
  DateTime? redeemedAt;
  DateTime? cancelledAt;

  factory GiftCard.fromJson(Map<String, dynamic> json) => GiftCard(
        id: json["id"],
        receiverName: json["receiver_name"],
        senderName: json["sender_name"],
        message: json["message"] == null ? null : json["message"],
        amount: json["amount"],
        image: json["image"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        redeemedAt: json["redeemed_at"] == null
            ? null
            : DateTime.parse(json["redeemed_at"]),
        cancelledAt: json["cancelled_at"] == null
            ? null
            : DateTime.parse(json["cancelled_at"]),
      );
}
