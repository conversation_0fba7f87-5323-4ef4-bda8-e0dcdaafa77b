class GiftCardDesign {
  GiftCardDesign({
    required this.id,
    required this.title,
    required this.image,
    required this.message,
  });

  int id;
  String? title;
  String? image;
  String? message;

  factory GiftCardDesign.fromJson(Map<String, dynamic> json) => GiftCardDesign(
        id: json["id"],
        title: json["title"] == null ? null : json["title"],
        image: json["image"] == null ? null : json["image"],
        message: json["message"] == null ? null : json["message"],
      );
}
