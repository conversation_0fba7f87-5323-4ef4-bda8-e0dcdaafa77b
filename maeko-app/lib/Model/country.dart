import 'package:hive/hive.dart';

part 'country.g.dart';

@HiveType(typeId: 12)
class Country {
  Country({
    required this.id,
    required this.name,
    required this.flag,
    required this.phoneCode,
    required this.states,
  });

  @HiveField(0)
  int id;
  @HiveField(1)
  String name;
  @HiveField(2)
  String? flag;
  @HiveField(3)
  String phoneCode;
  @HiveField(4)
  List<States>? states;

  factory Country.fromJson(Map<String, dynamic> json) => Country(
        id: json["id"],
        name: json["name"],
        flag: json["flag"] == null ? null : json["flag"],
        phoneCode: json["code"] == null ? null : json["code"],
        states: json["states"] == null
            ? null
            : List<States>.from(json["states"].map((x) => States.fromJson(x))),
      );
}

@HiveType(typeId: 13)
class States {
  States({
    required this.id,
    required this.name,
  });

  @HiveField(0)
  int id;
  @HiveField(1)
  String name;

  factory States.fromJson(Map<String, dynamic> json) => States(
        id: json["id"],
        name: json["name"],
      );
}
