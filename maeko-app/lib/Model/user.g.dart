// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserAdapter extends TypeAdapter<User> {
  @override
  final int typeId = 0;

  @override
  User read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return User(
      id: fields[0] as int,
      referrerId: fields[1] as int?,
      code: fields[2] as String,
      firstName: fields[3] as String,
      lastName: fields[4] as String,
      phone: fields[5] as String,
      username: fields[6] as String,
      dateOfBirth: fields[7] as DateTime?,
      gender: fields[8] as String?,
      race: fields[9] as String?,
      point: fields[10] as String,
      totalSpend: fields[11] as String,
      membershipExpireOn: fields[12] as DateTime?,
      createdAt: fields[13] as DateTime?,
      updatedAt: fields[14] as DateTime?,
      deletedAt: fields[15] as DateTime?,
      membership: fields[16] as Membership?,
    );
  }

  @override
  void write(BinaryWriter writer, User obj) {
    writer
      ..writeByte(17)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.referrerId)
      ..writeByte(2)
      ..write(obj.code)
      ..writeByte(3)
      ..write(obj.firstName)
      ..writeByte(4)
      ..write(obj.lastName)
      ..writeByte(5)
      ..write(obj.phone)
      ..writeByte(6)
      ..write(obj.username)
      ..writeByte(7)
      ..write(obj.dateOfBirth)
      ..writeByte(8)
      ..write(obj.gender)
      ..writeByte(9)
      ..write(obj.race)
      ..writeByte(10)
      ..write(obj.point)
      ..writeByte(11)
      ..write(obj.totalSpend)
      ..writeByte(12)
      ..write(obj.membershipExpireOn)
      ..writeByte(13)
      ..write(obj.createdAt)
      ..writeByte(14)
      ..write(obj.updatedAt)
      ..writeByte(15)
      ..write(obj.deletedAt)
      ..writeByte(16)
      ..write(obj.membership);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
