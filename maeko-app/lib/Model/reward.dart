import 'package:amverton/Model/check_in_event.dart';
import 'package:amverton/Model/voucher.dart';

class Reward {
  Reward({
    required this.me,
    required this.checkIn,
    required this.pointsRewards,
  });

  Me me;
  CheckIn? checkIn;
  List<PointsReward>? pointsRewards;

  factory Reward.fromJson(Map<String, dynamic> json) => Reward(
        me: Me.fromJson(json["me"]),
        checkIn: json["check_in"] == null
            ? null
            : CheckIn.fromJson(json["check_in"]),
        pointsRewards: json["rewards"] == null
            ? null
            : List<PointsReward>.from(
                json["rewards"].map((x) => PointsReward.fromJson(x))),
      );
}

class Me {
  Me({
    required this.point,
    required this.pointToBeExpired,
    required this.pointExpiredOn,
  });

  String point;
  String? pointToBeExpired;
  DateTime? pointExpiredOn;

  factory Me.fromJson(Map<String, dynamic> json) => Me(
        point: json["point"],
        pointToBeExpired: json["point_to_be_expired"] == null
            ? null
            : json["point_to_be_expired"],
        pointExpiredOn: json["point_expired_on"] == null
            ? null
            : DateTime.parse(json["point_expired_on"]),
      );
}

class CheckIn {
  CheckIn({
    required this.checkInEvent,
    required this.totalDaysCheckedIn,
    required this.hasCheckedInToday,
  });

  CheckInEvent? checkInEvent;
  int totalDaysCheckedIn;
  int hasCheckedInToday;

  factory CheckIn.fromJson(Map<String, dynamic> json) => CheckIn(
        checkInEvent:
            json["event"] == null ? null : CheckInEvent.fromJson(json["event"]),
        totalDaysCheckedIn: json["total_days_checked_in"],
        hasCheckedInToday: json["has_check_in_today"],
      );
}

class PointsReward {
  PointsReward({
    required this.id,
    required this.point,
    required this.sequence,
    required this.isEligible,
    required this.voucher,
  });

  int id;
  String point;
  int sequence;
  bool isEligible;
  Voucher voucher;

  factory PointsReward.fromJson(Map<String, dynamic> json) => PointsReward(
        id: json["id"],
        point: json["point"],
        sequence: json["sequence"],
        isEligible: json["is_eligible"],
        voucher: Voucher.fromJson(json["voucher"]),
      );
}
