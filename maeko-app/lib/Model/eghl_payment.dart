class EghlPayment {
  EghlPayment({
    required this.transactionType,
    required this.paymentMethod,
    required this.serviceId,
    required this.merchantPass,
    required this.paymentId,
    required this.orderNumber,
    required this.paymentDesc,
    required this.merchantName,
    required this.merchantCallBackUrl,
    required this.amount,
    required this.currencyCode,
    required this.custIp,
    required this.custName,
    required this.custEmail,
    required this.custPhone,
    required this.languageCode,
    required this.paymentGateway,
  });

  String? transactionType;
  String? paymentMethod;
  String? serviceId;
  String? merchantPass;
  String? paymentId;
  String? orderNumber;
  String? paymentDesc;
  String? merchantName;
  String? merchantCallBackUrl;
  String? amount;
  String? currencyCode;
  String? custIp;
  String? custName;
  String? custEmail;
  String? custPhone;
  String? languageCode;
  String? paymentGateway;

  factory EghlPayment.fromJson(Map<String, dynamic> json) => EghlPayment(
        transactionType:
            json["TransactionType"] == null ? null : json["TransactionType"],
        paymentMethod: json["PymtMethod"] == null ? null : json["PymtMethod"],
        serviceId: json["ServiceID"] == null ? null : json["ServiceID"],
        merchantPass:
            json["MerchantPassword"] == null ? null : json["MerchantPassword"],
        paymentId: json["PaymentID"] == null ? null : json["PaymentID"],
        orderNumber: json["OrderNumber"] == null ? null : json["OrderNumber"],
        paymentDesc: json["PaymentDesc"] == null ? null : json["PaymentDesc"],
        merchantName:
            json["MerchantName"] == null ? null : json["MerchantName"],
        merchantCallBackUrl: json["MerchantCallBackURL"] == null
            ? null
            : json["MerchantCallBackURL"],
        amount: json["Amount"] == null ? null : json["Amount"],
        currencyCode:
            json["CurrencyCode"] == null ? null : json["CurrencyCode"],
        custIp: json["CustIP"] == null ? null : json["CustIP"],
        custName: json["CustName"] == null ? null : json["CustName"],
        custEmail: json["CustEmail"] == null ? null : json["CustEmail"],
        custPhone: json["CustPhone"] == null ? null : json["CustPhone"],
        languageCode:
            json["LanguageCode"] == null ? null : json["LanguageCode"],
        paymentGateway:
            json["PaymentGateway"] == null ? null : json["PaymentGateway"],
      );
}
