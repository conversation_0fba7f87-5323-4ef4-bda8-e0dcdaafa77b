class User {
  final int id;
  final int? referrerId;
  final String code;
  final String firstName;
  final String lastName;
  final String phone;
  final String email;
  final String username;
  final String dateOfBirth;
  final String? gender;
  final String? race;
  final String point;
  final String totalSpend;
  final String? membershipExpireOn;
  final String? phoneVerifiedAt;
  final String createdAt;
  final String updatedAt;
  final String? deletedAt;
  final Membership membership;

  User({
    required this.id,
    this.referrerId,
    required this.code,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.email,
    required this.username,
    required this.dateOfBirth,
    this.gender,
    this.race,
    required this.point,
    required this.totalSpend,
    this.membershipExpireOn,
    this.phoneVerifiedAt,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.membership,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json['id'],
        referrerId: json['referrer_id'],
        code: json['code'],
        firstName: json['first_name'],
        lastName: json['last_name'],
        phone: json['phone'],
        email: json['email'],
        username: json['username'],
        dateOfBirth: json['date_of_birth'],
        gender: json['gender'],
        race: json['race'],
        point: json['point'],
        totalSpend: json['total_spend'],
        membershipExpireOn: json['membership_expire_on'],
        phoneVerifiedAt: json['phone_verified_at'],
        createdAt: json['created_at'],
        updatedAt: json['updated_at'],
        deletedAt: json['deleted_at'],
        membership: Membership.fromJson(json['membership']),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'referrer_id': referrerId,
        'code': code,
        'first_name': firstName,
        'last_name': lastName,
        'phone': phone,
        'email': email,
        'username': username,
        'date_of_birth': dateOfBirth,
        'gender': gender,
        'race': race,
        'point': point,
        'total_spend': totalSpend,
        'membership_expire_on': membershipExpireOn,
        'phone_verified_at': phoneVerifiedAt,
        'created_at': createdAt,
        'updated_at': updatedAt,
        'deleted_at': deletedAt,
        'membership': membership.toJson(),
      };
}

class Membership {
  final int id;
  final int tier;
  final String name;
  final int minimumSpend;
  final String diningDiscount;
  final String roomDiscount;
  final int expireMonths;
  final String upgradeRewardPoint;
  final int isPaid;
  final String cardImage;
  final List<Benefit> benefits;

  Membership({
    required this.id,
    required this.tier,
    required this.name,
    required this.minimumSpend,
    required this.diningDiscount,
    required this.roomDiscount,
    required this.expireMonths,
    required this.upgradeRewardPoint,
    required this.isPaid,
    required this.cardImage,
    required this.benefits,
  });

  factory Membership.fromJson(Map<String, dynamic> json) => Membership(
        id: json['id'],
        tier: json['tier'],
        name: json['name'],
        minimumSpend: json['minimum_spend'],
        diningDiscount: json['dining_discount'],
        roomDiscount: json['room_discount'],
        expireMonths: json['expire_months'],
        upgradeRewardPoint: json['upgrade_reward_point'],
        isPaid: json['is_paid'],
        cardImage: json['card_image'],
        benefits: (json['benefits'] as List)
            .map((e) => Benefit.fromJson(e))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'tier': tier,
        'name': name,
        'minimum_spend': minimumSpend,
        'dining_discount': diningDiscount,
        'room_discount': roomDiscount,
        'expire_months': expireMonths,
        'upgrade_reward_point': upgradeRewardPoint,
        'is_paid': isPaid,
        'card_image': cardImage,
        'benefits': benefits.map((e) => e.toJson()).toList(),
      };
}

class Benefit {
  final String icon;
  final String title;
  final String description;
  final int sequence;

  Benefit({
    required this.icon,
    required this.title,
    required this.description,
    required this.sequence,
  });

  factory Benefit.fromJson(Map<String, dynamic> json) => Benefit(
        icon: json['icon'],
        title: json['title'],
        description: json['description'],
        sequence: json['sequence'],
      );

  Map<String, dynamic> toJson() => {
        'icon': icon,
        'title': title,
        'description': description,
        'sequence': sequence,
      };
}