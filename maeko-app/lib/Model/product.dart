import 'package:amverton/Model/outlet.dart';

class Product {
  Product({
    required this.id,
    required this.spu,
    required this.name,
    required this.unit,
    required this.price,
    required this.description,
    required this.image,
    required this.type,
    required this.outlet,
    required this.category,
    required this.bundleCategory,
    required this.modifierCategory,
  });

  int id;
  String spu;
  String name;
  String? unit;
  String? price;
  String? description;
  String? image;
  String? type;
  Outlet? outlet;
  Category? category;
  List<BundleCategory>? bundleCategory;
  List<ModifierCategory>? modifierCategory;

  factory Product.fromJson(Map<String, dynamic> json) => Product(
        id: json["id"],
        spu: json["spu"],
        name: json["name"],
        unit: json["unit"] == null ? null : json["unit"],
        price: json["price"] == null ? null : json["price"],
        description: json["description"] == null ? null : json["description"],
        image: json["image"] == null ? null : json["image"],
        type: json["type"] == null ? null : json["type"],
        outlet: json["outlet"] == null ? null : Outlet.fromJson(json["outlet"]),
        category: json["category"] == null
            ? null
            : Category.fromJson(json["category"]),
        bundleCategory: json["bundle_choices"] == null
            ? null
            : List<BundleCategory>.from(
                json["bundle_choices"].map((x) => BundleCategory.fromJson(x))),
        modifierCategory: json["modifier_groups"] == null
            ? null
            : List<ModifierCategory>.from(json["modifier_groups"]
                .map((x) => ModifierCategory.fromJson(x))),
      );
}

class Category {
  Category({
    required this.id,
    required this.name,
    required this.sequence,
    required this.image,
  });

  int id;
  String name;
  int sequence;
  String? image;

  factory Category.fromJson(Map<String, dynamic> json) => Category(
        id: json["id"],
        name: json["name"],
        sequence: json["sequence"],
        image: json["image"] == null ? null : json["image"],
      );
}

class BundleCategory {
  BundleCategory({
    required this.id,
    required this.name,
    required this.minimumSelection,
    required this.maximumSelection,
    required this.bundleItems,
  });

  int id;
  String name;
  int minimumSelection;
  int maximumSelection;
  List<BundleItem> bundleItems;

  factory BundleCategory.fromJson(Map<String, dynamic> json) => BundleCategory(
        id: json["id"],
        name: json["name"],
        minimumSelection: json["minimum_selection"],
        maximumSelection: json["maximum_selection"],
        bundleItems: List<BundleItem>.from(
            json["items"].map((x) => BundleItem.fromJson(x))),
      );
}

class BundleItem {
  BundleItem({
    required this.id,
    required this.additionalPrice,
    required this.isSelected,
    required this.product,
  });

  int id;
  String additionalPrice;
  int? isSelected;
  Product product;

  factory BundleItem.fromJson(Map<String, dynamic> json) => BundleItem(
        id: json["id"],
        additionalPrice: json["additional_price"],
        isSelected: json["is_selected"] == null ? null : json["is_selected"],
        product: Product.fromJson(json["product"]),
      );
}

class ModifierCategory {
  ModifierCategory({
    required this.id,
    required this.name,
    required this.minimumSelection,
    required this.maximumSelection,
    required this.modifierItems,
  });

  int id;
  String name;
  int minimumSelection;
  int maximumSelection;
  List<ModifierItem> modifierItems;

  factory ModifierCategory.fromJson(Map<String, dynamic> json) =>
      ModifierCategory(
        id: json["id"],
        name: json["name"],
        minimumSelection: json["minimum_selection"],
        maximumSelection: json["maximum_selection"],
        modifierItems: List<ModifierItem>.from(
            json["options"].map((x) => ModifierItem.fromJson(x))),
      );
}

class ModifierItem {
  ModifierItem({
    required this.id,
    required this.name,
    required this.additionalPrice,
    required this.isSelected,
  });

  int id;
  String name;
  String additionalPrice;
  int? isSelected;

  factory ModifierItem.fromJson(Map<String, dynamic> json) => ModifierItem(
        id: json["id"],
        name: json["name"],
        additionalPrice: json["additional_price"],
        isSelected: json["is_selected"] == null ? null : json["is_selected"],
      );
}
