import 'package:hive/hive.dart';

part 'pages.g.dart';

class Pages {
  Pages({
    required this.id,
    required this.name,
    required this.title,
    required this.sequence,
    required this.isMenu,
    required this.sections,
  });

  int id;
  String name;
  String? title;
  int sequence;
  int isMenu;
  List<Section> sections;

  factory Pages.fromJson(Map<String, dynamic> json) => Pages(
        id: json["id"],
        name: json["name"],
        title: json["title"] == null ? null : json["title"],
        sequence: json["sequence"],
        isMenu: json["is_menu"],
        sections: List<Section>.from(
            json["sections"].map((x) => Section.fromJson(x))),
      );
}

@HiveType(typeId: 1)
class Section {
  Section({
    required this.id,
    required this.name,
    required this.sequence,
    required this.isSelected,
    required this.template,
  });

  @HiveField(0)
  int id;
  @HiveField(1)
  String name;
  @HiveField(2)
  int sequence;
  @HiveField(3)
  int isSelected;
  @HiveField(4)
  Template? template;

  factory Section.fromJson(Map<String, dynamic> json) => Section(
        id: json["id"],
        name: json["name"],
        sequence: json["sequence"],
        isSelected: json["is_selected"],
        template: json["template"] == null
            ? null
            : Template.fromJson(json["template"]),
      );
}

@HiveType(typeId: 2)
class Template {
  Template({
    required this.id,
    required this.name,
    required this.assets,
  });

  @HiveField(0)
  int id;
  @HiveField(1)
  String name;
  @HiveField(2)
  List<Asset> assets;

  factory Template.fromJson(Map<String, dynamic> json) => Template(
        id: json["id"],
        name: json["name"],
        assets: List<Asset>.from(json["assets"].map((x) => Asset.fromJson(x))),
      );
}

@HiveType(typeId: 3)
class Asset {
  Asset({
    required this.id,
    required this.name,
    required this.type,
    required this.data,
  });

  @HiveField(0)
  int id;
  @HiveField(1)
  String name;
  @HiveField(2)
  String type;
  @HiveField(3)
  String? data;

  factory Asset.fromJson(Map<String, dynamic> json) => Asset(
        id: json["id"],
        name: json["name"],
        type: json["type"],
        data: json["data"],
      );
}
