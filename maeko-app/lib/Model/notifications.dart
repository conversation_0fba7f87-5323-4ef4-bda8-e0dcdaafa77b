class Notifications {
  Notifications({
    required this.id,
    required this.title,
    required this.message,
    required this.image,
    required this.readAt,
    required this.createdAt,
  });

  int id;
  String title;
  String message;
  String? image;
  DateTime? readAt;
  DateTime? createdAt;

  factory Notifications.fromJson(Map<String, dynamic> json) => Notifications(
        id: json["id"],
        title: json["title"],
        message: json["message"],
        image: json["image"] == null ? null : json["image"],
        readAt:
            json["read_at"] == null ? null : DateTime.parse(json["read_at"]),
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
      );
}
