
// Restaurant Model
class Restaurant {
  final int id;
  final int branchId;
  final String slug;
  final String name;
  final String tagline;
  final String description;
  final String image1;
  final String image2;
  final String? dressCode;
  final int isFeatured;
  final String createdAt;
  final String updatedAt;
  final String? deletedAt;

  Restaurant({
    required this.id,
    required this.branchId,
    required this.slug,
    required this.name,
    required this.tagline,
    required this.description,
    required this.image1,
    required this.image2,
    this.dressCode,
    required this.isFeatured,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  factory Restaurant.fromJson(Map<String, dynamic> json) {
    return Restaurant(
      id: json['id'] ?? 0,
      branchId: json['branch_id'] ?? 0,
      slug: json['slug'] ?? '',
      name: json['name'] ?? '',
      tagline: json['tagline'] ?? '',
      description: json['description'] ?? '',
      image1: json['image_1'] ?? '',
      image2: json['image_2'] ?? '',
      dressCode: json['dress_code'],
      isFeatured: json['is_featured'] ?? 0,
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      deletedAt: json['deleted_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'branch_id': branchId,
      'slug': slug,
      'name': name,
      'tagline': tagline,
      'description': description,
      'image_1': image1,
      'image_2': image2,
      'dress_code': dressCode,
      'is_featured': isFeatured,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'deleted_at': deletedAt,
    };
  }

  // Convenience getters
  bool get isFeaturedRestaurant => isFeatured == 1;
  
  DateTime? get createdAtAsDateTime {
    try {
      return DateTime.parse(createdAt);
    } catch (e) {
      return null;
    }
  }
  
  List<String> get images => [image1, image2].where((img) => img.isNotEmpty).toList();
}