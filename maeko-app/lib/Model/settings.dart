class Settings {
  Settings({
    required this.id,
    required this.email,
    required this.sms,
    required this.pushNotification,
  });

  int id;
  int email;
  int sms;
  int pushNotification;

  factory Settings.fromJson(Map<String, dynamic> json) => Settings(
        id: json["id"],
        email: json["email"],
        sms: json["sms"],
        pushNotification: json["push_notification"],
      );
}
