import 'package:amverton/Model/country.dart';
import 'package:hive/hive.dart';

part 'address.g.dart';

@HiveType(typeId: 11)
class Address {
  Address({
    required this.id,
    required this.address1,
    required this.address2,
    required this.postcode,
    required this.city,
    required this.name,
    required this.countryCode,
    required this.phone,
    required this.isDefault,
    required this.country,
    required this.state,
  });

  @HiveField(0)
  int id;
  @HiveField(1)
  String address1;
  @HiveField(2)
  String? address2;
  @HiveField(3)
  String postcode;
  @HiveField(4)
  String city;
  @HiveField(5)
  String name;
  @HiveField(6)
  String countryCode;
  @HiveField(7)
  String phone;
  @HiveField(8)
  int isDefault;
  @HiveField(9)
  Country? country;
  @HiveField(10)
  States? state;

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        id: json["id"],
        address1: json["address_1"],
        address2: json["address_2"] == null ? null : json["address_2"],
        postcode: json["postcode"],
        city: json["city"],
        name: json["name"],
        countryCode: json["country_code"],
        phone: json["phone"],
        isDefault: json["is_default"],
        country:
            json["country"] == null ? null : Country.fromJson(json["country"]),
        state: json["state"] == null ? null : States.fromJson(json["state"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "address_1": address1,
        "address_2": address2,
        "postcode": postcode,
        "city": city,
        "name": name,
        "country_code": countryCode,
        "phone": phone,
        "is_default": isDefault,
        "country": country,
        "state": state,
      };
}
