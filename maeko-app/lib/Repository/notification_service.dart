import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:amverton/Repository/api_post.dart';

class NotificationService {
  final FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;

  initialise() async {
    await firebaseMessaging.requestPermission(
        sound: true, badge: true, alert: true);

    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
      alert: true, // Required to display a heads up notification
      badge: true,
      sound: true,
    );

    FirebaseMessaging.instance.getInitialMessage().then((value) {
      print("getInitialMessage");
    });

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print("onMessage");
      print(message.toMap());
      // if (message.notification?.title == "voucher redeem") {
      //   if (Platform.isAndroid) {
      //     Navigator.pop(navigatorKey.currentState!.context);
      //     // Navigator.push(navigatorKey.currentState!.context,
      //     //     MaterialPageRoute(builder: (context) => const NotificationPage()));
      //   }
      // }
    });

    // FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) async {
    //   print("onMessageOpenedApp");
    //   print(message.toMap());
    //   if (message.notification?.title == "voucher redeem") {
    //     if (Platform.isIOS) {
    //       Navigator.pop(navigatorKey.currentState!.context);
    //       // Navigator.push(navigatorKey.currentState!.context,
    //       //     MaterialPageRoute(builder: (context) => const NotificationPage()));
    //     }
    //   }
    // });
  }

  saveNotificationToken() async {
    try {
      String? token = await firebaseMessaging.getToken();

      ApiPost apiPost = ApiPost();
      Map? data = await apiPost.saveNotificationToken(token);
      print("saveNotificationToken: $data");
    } catch (e) {
      print("Errors found in saveNotificationToken(Notification Service): $e");
    }
  }
}
