import 'dart:async';

import 'package:geolocator/geolocator.dart';

class LocationService {
  static Future<Position?> checkAndroidLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    Position? position;

    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        print("Location permissions are denied");
      } else {
        try {
          position = await Geolocator.getCurrentPosition(
              desiredAccuracy: LocationAccuracy.high);
        } catch (e) {
          print("Can't get location");
        }
      }
    } else if (permission == LocationPermission.deniedForever) {
      print("Location permissions are permanently denied.");
    } else {
      try {
        position = await Geolocator.getCurrentPosition(
            desiredAccuracy: LocationAccuracy.high);
      } catch (e) {
        print("Can't get location");
      }
    }
    return position;
  }

  static Future<Position?> getCurrentPosition() async {
    try {
      //not using, last known position is way faster
      //timeLimit is a must for ios device
      //get current position function in ios will lead no response, not even null.
      //this lead to future bug awaiting response
      // Position? position = await Geolocator.getCurrentPosition(
      //   desiredAccuracy: LocationAccuracy.high,
      //   timeLimit: const Duration(milliseconds: 500),
      // );
      Position? position = await Geolocator.getLastKnownPosition();

      return position;
    } on TimeoutException catch (e) {
      print('Location request timed out: $e');
      Position? position = await Geolocator.getLastKnownPosition();
      return position;
    } catch (e) {
      print(e.toString());
      return null;
    }
  }
}
