import 'package:flutter/material.dart';

List<Route<dynamic>?> routeStacks = [];

class MyNavigatorObserver extends NavigatorObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    routeStacks.add(route);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    routeStacks.removeLast();
  }

  @override
  void didRemove(Route route, Route? previousRoute) {
    Route? routeStack = routeStacks.firstWhere(
        (element) => element!.settings.name == route.settings.name,
        orElse: () => null);

    if (routeStack != null) {
      print(routeStack);
      routeStacks.remove(route);
    } else {
      routeStacks.removeLast();
    }
  }

  @override
  void didReplace({Route? newRoute, Route? oldRoute}) {
    routeStacks.removeLast();
    routeStacks.add(newRoute!);
  }
}
