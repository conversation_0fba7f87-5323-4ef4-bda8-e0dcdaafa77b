import 'package:amverton/View/Booking/booking_page.dart';
import 'package:amverton/View/Home/Section/reward_template1.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:amverton/Model/app_settings.dart';
import 'package:amverton/Model/bottom_bar.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/View/Account/Section/balance_template1.dart'
    as Account;
import 'package:amverton/View/Account/Section/balance_template2.dart'
    as Account;
import 'package:amverton/View/Account/Section/balance_template3.dart'
    as Account;
import 'package:amverton/View/Account/Section/balance_template4.dart'
    as Account;
import 'package:amverton/View/Account/Section/profile_template1.dart';
import 'package:amverton/View/Account/Section/profile_template2.dart';
import 'package:amverton/View/Account/account_page.dart';
import 'package:amverton/View/Booking/Section/outlet_template1.dart' as Booking;
import 'package:amverton/View/Booking/booking_outlet.dart';
import 'package:amverton/View/Cart/cart_page.dart';
import 'package:amverton/View/Home/Section/balance_template1.dart' as Home;
import 'package:amverton/View/Home/Section/banner_template1.dart' as Home;
import 'package:amverton/View/Home/Section/reward_template1.dart' as Home;
import 'package:amverton/View/Home/Section/game_template1.dart';
import 'package:amverton/View/Home/Section/order_template1.dart';
import 'package:amverton/View/Home/Section/referral_template1.dart';
import 'package:amverton/View/Home/Section/referral_template2.dart';
import 'package:amverton/View/Home/Section/slideshow_template1.dart';
import 'package:amverton/View/Home/Section/welcome_template1.dart';
import 'package:amverton/View/Home/home_page.dart';
import 'package:amverton/View/Scanqr/Scanqr_page.dart';
import 'package:amverton/View/Menu/Section/outlet_template1.dart' as Menu;
import 'package:amverton/View/Menu/menu_outlet.dart';
import 'package:amverton/View/Restaurant/restaurant_outlet.dart';
import 'package:amverton/View/Restaurant/restaurant_page.dart';
import 'package:amverton/View/OrderMenu/Section/outlet_template1.dart' as Order;
import 'package:amverton/View/OrderMenu/order_menu_page.dart';
import 'package:amverton/View/Outlet/Section/map_template1.dart';
import 'package:amverton/View/Outlet/Section/map_template2.dart';
import 'package:amverton/View/Outlet/outlet_page.dart';
import 'package:amverton/View/Rewards/Section/balance_template1.dart' as Reward;
import 'package:amverton/View/Rewards/Section/balance_template2.dart' as Reward;
import 'package:amverton/View/Rewards/Section/check_in_template1.dart'
    as Reward;
import 'package:amverton/View/Rewards/Section/check_in_template2.dart'
    as Reward;
import 'package:amverton/View/Rewards/Section/check_in_template3.dart'
    as Reward;
import 'package:amverton/View/Rewards/Section/features_template1.dart';
import 'package:amverton/View/Rewards/Section/voucher_template3.dart';
import 'package:amverton/View/Rewards/rewards_page.dart';
import 'package:amverton/View/Startup/intro_template1.dart';

import 'package:hive/hive.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

Widget? introWidget;
List<Widget> defaultBar = [];

late SubAsset loginAsset;
List<SubAsset> registerAsset = [];
late SubAsset forgotPassAsset;
late SubAsset resetPassAsset;

List<Widget> homeWidget = [];
String dailyPopUpTheme = "L";
Widget? dailyPopUpWidget;

String cartTheme = "L";

Widget? bookingMainWidget;
String bookingMainTemplate = "1";
String bookingTheme = "L";

Widget? restaurantMainWidget;
String restaurantMainTemplate = "1";
String restaurantTheme = "L";

Widget? orderMainWidget;
String orderMainTemplate = "1";
String orderTheme = "L";

Widget? menuMainWidget;
String menuMainTemplate = "1";
String menuTheme = "L";

Widget? outletMainWidget;
String outletMainTemplate = "1";
String outletTheme = "L";

List<Widget> rewardsWidget = [];

List<Widget> accountWidget = [];
List<SubAsset> accountAsset = [];
late SubAsset editProfileAsset;

late GlobalKey<HomePageState> homePageKey;
late GlobalKey<OrderMenuPageState> orderMenuPageKey;
late GlobalKey<AccountPageState> accountPageKey;

getGlobal(List<Asset> global) async {
  Box boxGlobal = Hive.box('boxGlobal');

  // icon
  await boxGlobal.put("iconSet", "A");

  //theme color
  Box boxColor = await Hive.openBox('boxColor');

  String? colorP1 = global
      .firstWhereOrNull((element) => element.name == "PRIMARY_LIGHT")
      ?.data;
  if (colorP1 != null) await boxColor.put("primaryLight", "0xff$colorP1");

  String? colorP2 = global
      .firstWhereOrNull((element) => element.name == "PRIMARY_DARK")
      ?.data;
  if (colorP2 != null) await boxColor.put("primaryDark", "0xff$colorP2");

  String? colorS1 = global
      .firstWhereOrNull((element) => element.name == "SECONDARY_LIGHT")
      ?.data;
  if (colorS1 != null) await boxColor.put("secondaryLight", "0xff$colorS1");

  String? colorS2 = global
      .firstWhereOrNull((element) => element.name == "SECONDARY_DARK")
      ?.data;
  if (colorS2 != null) await boxColor.put("secondaryDark", "0xff$colorS2");

  // dark color
  String? colorD1 =
      global.firstWhereOrNull((element) => element.name == "DARK")?.data;
  if (colorD1 != null) await boxColor.put("dark", "0xff$colorD1");

  String? colorD2 =
      global.firstWhereOrNull((element) => element.name == "GRAY")?.data;
  if (colorD2 != null) await boxColor.put("gray", "0xff$colorD2");

  String? colorD3 =
      global.firstWhereOrNull((element) => element.name == "LIGHT")?.data;
  if (colorD3 != null) await boxColor.put("light", "0xff$colorD3");

  String? colorD4 =
      global.firstWhereOrNull((element) => element.name == "DISABLED")?.data;
  if (colorD4 != null) await boxColor.put("disabled", "0xff$colorD4");
}

updateAssets(List<Pages> pages) async {
  for (Pages page in pages) {
    for (Section section in page.sections) {
      if (section.template != null) {
        var boxAssets = await Hive.openBox<Asset>(
            '${page.name}+${section.name}+${section.template!.name[section.template!.name.length - 1]}');

        if (section.template!.assets.isNotEmpty) {
          if (boxAssets.isEmpty) {
            boxAssets.addAll(section.template!.assets);
          } else {
            List<Asset> oldAssets = boxAssets.values.toList();

            for (Asset newAsset in section.template!.assets) {
              int index = oldAssets
                  .indexWhere((element) => element.name == newAsset.name);
              if (index == -1) {
                // new assets added
                oldAssets.add(newAsset);
              } else {
                // edit old assets
                oldAssets[index] = newAsset;
              }
            }
            await boxAssets.clear();
            await boxAssets.addAll(oldAssets);
          }
        }
      }
    }
  }
}

getIntroWidget(Pages page) async {
  if (page.sections.isNotEmpty) {
    Section section = page.sections.first;
    if (section.isSelected == 1 && section.template != null) {
      for (Asset asset in section.template!.assets) {
        if (asset.name.contains("IMAGE")) {
          if (asset.data != null) {
            await DefaultCacheManager()
                .downloadFile(asset.data!)
                .then((value) {});
          }
        }
      }
      if (section.template!.name[1] == "1") {
        introWidget = IntroTemplate1(theme: section.template!.name[0]);
      }
    }
  }
}

getBottomBarList(List<Pages> pages) async {
  Box boxGlobal = await Hive.openBox("boxGlobal");
  AppSettings? appSettings = boxGlobal.get("appSettings") ?? null;
  List<BottomBar> bottomBarList = [];

  for (Pages page in pages) {
    if (page.isMenu == 1 && page.title != null) {
        print(page.name);
       print(page.isMenu);
          print(page.title);
      String icon = "";
      if (page.name == "HOME") {
        icon = "Home 01";
      } else if (page.name == "RESTAURANT") {
        icon = "tabler_tools-kitchen-2";
      } else if (page.name == "SCANQR") {
        icon = "Home 01";
      } else if (page.name == "REWARDS") {
        icon = "Game 06";
      } else if (page.name == "CART") {
        icon = "Home 01";
        if (page.sections.isNotEmpty) {
          cartTheme = page.sections
                  .firstWhereOrNull((element) => element.name == "MAIN")
                  ?.template
                  ?.name[0] ??
              "L";
        }
      } else if (page.name == "ORDER") {
        icon = "Menu 02";
        if (page.sections.isNotEmpty) {
          orderTheme = page.sections
                  .firstWhereOrNull((element) => element.name == "MAIN")
                  ?.template
                  ?.name[0] ??
              "L";
        }
      } else if (page.name == "MENU") {
        icon = "tabler_tools-kitchen-2";
        if (page.sections.isNotEmpty) {
          menuTheme = page.sections
                  .firstWhereOrNull((element) => element.name == "MAIN")
                  ?.template
                  ?.name[0] ??
              "L";
        }
      } else if (page.name == "OUTLETS") {
        icon = "map-04";
        if (page.sections.isNotEmpty) {
          outletTheme = page.sections
                  .firstWhereOrNull((element) => element.name == "MAIN")
                  ?.template
                  ?.name[0] ??
              "L";
        }
      } else if (page.name == "BOOKINGS") {
        icon = "Booking 02";
        if (page.sections.isNotEmpty) {
          bookingTheme = page.sections
                  .firstWhereOrNull((element) => element.name == "MAIN")
                  ?.template
                  ?.name[0] ??
              "L";
        }
      } else if (page.name == "ACCOUNT") {
        icon = "Me 01";
      }
      bottomBarList.add(BottomBar(page.name, page.title!, icon));
    }
  }

  for (Pages page in pages) {
    if (page.name == "ORDER" &&
        (appSettings?.deliveryOptions?.hasDelivery == 0 &&
            appSettings?.deliveryOptions?.hasPickup == 0)) {
      int orderIndex =
          bottomBarList.indexWhere((element) => element.name == "ORDER");
      bottomBarList.removeAt(orderIndex);
    }
  }

  await boxGlobal.put("bottomBar", bottomBarList);
}

Future<List<Widget>> getDefaultBar() async {
  homePageKey = GlobalKey<HomePageState>();
  orderMenuPageKey = GlobalKey<OrderMenuPageState>();
  accountPageKey = GlobalKey<AccountPageState>();

  defaultBar.clear();

  var boxGlobal = Hive.box("boxGlobal");
  print("dddddd");
  List bottomBarList = boxGlobal.get("bottomBar") ?? [];
    print("eeeee");
  for (BottomBar each in bottomBarList) {
      print("ffffff");
    if (each.name == "HOME") {
      defaultBar.add(HomePage(
        key: homePageKey,
      ));
    } else if (each.name == "RESTAURANT") {
      defaultBar.add(const RestaurantOutlet());
    } else if (each.name == "SCANQR") {
      defaultBar.add(const ScanqrPage());
    } else if (each.name == "REWARDS") {
      defaultBar.add(const RewardsPage());
    } else if (each.name == "CART") {
      defaultBar.add(const CartPage());
    } else if (each.name == "ORDER") {
      defaultBar.add(SizedBox());
    } else if (each.name == "MENU") {
      defaultBar.add(MenuOutlet());
    } else if (each.name == "OUTLETS") {
      defaultBar.add(const OutletPage());
    } else if (each.name == "BOOKINGS") {
      defaultBar.add(const BookingOutlet());
    } else if (each.name == "ACCOUNT") {
      defaultBar.add(AccountPage(
        key: accountPageKey,
      ));
    }
  }

  return defaultBar;
}

getAuthWidget(List<Pages> pages) async {
  for (Pages page in pages) {
    if (page.name == "REGISTER") {
      if (page.sections.isNotEmpty) {
        for (Section section in page.sections) {
          if (section.template != null) {
            registerAsset.add(SubAsset.auth(section.name,
                section.template!.name[0], section.template!.name[1]));
          }
        }
      }
    } else if (page.name == "LOGIN") {
      if (page.sections.isNotEmpty) {
        for (Section section in page.sections) {
          if (section.template != null) {
            loginAsset = SubAsset.auth(section.name, section.template!.name[0],
                section.template!.name[1]);
          }
        }
      }
    } else if (page.name == "FORGOT_PASSWORD") {
      if (page.sections.isNotEmpty) {
        for (Section section in page.sections) {
          if (section.template != null) {
            forgotPassAsset = SubAsset.auth(section.name,
                section.template!.name[0], section.template!.name[1]);
          }
        }
      }
    } else if (page.name == "RESET_PASSWORD") {
      if (page.sections.isNotEmpty) {
        for (Section section in page.sections) {
          if (section.template != null) {
            resetPassAsset = SubAsset.auth(section.name,
                section.template!.name[0], section.template!.name[1]);
          }
        }
      }
    }
  }
}

getHomeWidget(List sections) async {
  homeWidget.clear();

  for (Section section in sections) {
    if (section.isSelected == 1 && section.template != null) {
      if (section.name == "SLIDESHOW") {
        if (section.template!.name[1] == "1") {
          homeWidget.add(SlideshowTemplate1(theme: section.template!.name[0]));
        }
      } else if (section.name == "WELCOME") {
        if (section.template!.name[1] == "1") {
          homeWidget.add(WelcomeTemplate1(theme: section.template!.name[0]));
        }
      } else if (section.name == "BALANCE") {
        if (section.template!.name[1] == "1") {
          homeWidget
              .add(Home.BalanceTemplate1(theme: section.template!.name[0]));
        }
      } else if (section.name == "BANNER") {
        if (section.template!.name[1] == "1") {
          homeWidget
              .add(Home.BannerTemplate1(theme: section.template!.name[0]));
        }
      } 
      // else if (section.name == "GAMIFICATION") {
      //   if (section.template!.name[1] == "1") {
      //     homeWidget.add(GameTemplate1(theme: section.template!.name[0]));
      //   }
      // } else if (section.name == "REFERRAL") {
      //   if (section.template!.name[1] == "1") {
      //     homeWidget.add(ReferralTemplate1(theme: section.template!.name[0]));
      //   }
      // } else if (section.name == "REWARDS") {
      //   if (section.template!.name[1] == "1") {
      //     homeWidget.add(RewardTemplate1(theme: section.template!.name[0]));
      //   }
      // }
    }
  }
  homeWidget.clear();
  homeWidget.add(Home.BalanceTemplate1(theme: "L"));
  homeWidget.add(WelcomeTemplate1(theme: "L"));
  homeWidget.add(SlideshowTemplate1(theme: "L"));
  homeWidget.add(ReferralTemplate1(theme: "L"));
  //homeWidget.insert(1, OrderTemplate1(theme: "L"));
  //homeWidget.insert(1, OrderTemplate1(theme: "L"));
  ;
}

getBookingWidget(List sections) async {
  for (Section section in sections) {
    if (section.isSelected == 1 && section.template != null) {
      if (section.name == "MAIN") {
        bookingMainTemplate = section.template!.name[1];
        if (section.template!.name[1] == "1") {
          bookingMainWidget = BookingPage();
        }
      }
    }
  }
}

getRestaurantWidget(List sections) async {
  restaurantMainWidget = RestaurantPage();
  // for (Section section in sections) {
  //   if (section.isSelected == 1 && section.template != null) {
  //     if (section.name == "MAIN") {
  //       bookingMainTemplate = section.template!.name[1];
  //       if (section.template!.name[1] == "1") {
  //         bookingMainWidget = BookingPage();
  //       }
  //     }
  //   }
  // }
}

getOrderWidget(List sections) async {
  for (Section section in sections) {
    if (section.isSelected == 1 && section.template != null) {
      if (section.name == "MAIN") {
        orderMainTemplate = section.template!.name[1];
        if (section.template!.name[1] == "1") {
          orderMainWidget =
              Order.OutletTemplate1(theme: section.template!.name[0]);
        }
      }
    }
  }
}

getMenuWidget(List sections) async {
  for (Section section in sections) {
    if (section.isSelected == 1 && section.template != null) {
      if (section.name == "MAIN") {
        menuMainTemplate = section.template!.name[1];
        if (section.template!.name[1] == "1") {
          menuMainWidget =
              Menu.OutletTemplate1(theme: section.template!.name[0]);
        }
      }
    }
  }
}

getOutletWidget(List sections) async {
  for (Section section in sections) {
    if (section.isSelected == 1 && section.template != null) {
      if (section.name == "MAIN") {
        outletMainTemplate = section.template!.name[1];
        if (section.template!.name[1] == "1") {
          outletMainWidget = MapTemplate1(theme: section.template!.name[0]);
        } else if (section.template!.name[1] == "2") {
          outletMainWidget = MapTemplate2(theme: section.template!.name[0]);
        }
      }
    }
  }
}

getRewardsWidget(List sections) async {
  for (Section section in sections) {
    if (section.isSelected == 1 && section.template != null) {
      if (section.name == "BALANCE") {
        if (section.template!.name[1] == "1") {
          rewardsWidget
              .add(Reward.BalanceTemplate1(theme: section.template!.name[0]));
        } else if (section.template!.name[1] == "2") {
          rewardsWidget
              .add(Reward.BalanceTemplate2(theme: section.template!.name[0]));
        }
      } else if (section.name == "VOUCHERS") {
        if (section.template!.name[1] == "1") {
          rewardsWidget.add(VoucherTemplate3(theme: section.template!.name[0]));
        }
      } else if (section.name == "FEATURED") {
        if (section.template!.name[1] == "1") {
          rewardsWidget
              .add(FeaturesTemplate1(theme: section.template!.name[0]));
        }
      } else if (section.name == "CHECK_IN") {
        if (section.template!.name[1] == "1") {
          rewardsWidget
              .add(Reward.CheckInTemplate1(theme: section.template!.name[0]));
        }
      }
    }
  }

  // todo temp hardcode for amverton (remove if not using)
  rewardsWidget.clear();
  rewardsWidget.add(Reward.CheckInTemplate3(theme: "L"));
  rewardsWidget.add(VoucherTemplate3(theme: "L"));
  rewardsWidget.add(FeaturesTemplate1(theme: "L"));
  //
}

getAccountWidget(List sections) async {
  for (Section section in sections) {
    if (section.isSelected == 1 && section.template != null) {
      if (section.name == "PROFILE") {
        if (section.template!.name[1] == "1") {
          accountWidget.add(ProfileTemplate1(theme: section.template!.name[0]));
        }
      } else if (section.name == "BALANCE") {
        if (section.template!.name[1] == "1") {
          accountWidget
              .add(Account.BalanceTemplate1(theme: section.template!.name[0]));
        } else if (section.template!.name[1] == "2") {
          accountWidget
              .add(Account.BalanceTemplate2(theme: section.template!.name[0]));
        }
      } else if (section.name == "REFERRAL" ||
          section.name == "QUESTIONS" ||
          section.name == "ORDER_HISTORY" ||
          section.name == "FEEDBACK" ||
          section.name == "MEMBERSHIP" ||
          section.name == "POINT_HISTORY" ||
          section.name == "VOUCHERS" ||
          section.name == "PRIVACY_POLICY" ||
          section.name == "CUSTOMER_SUPPORT" ||
          section.name == "ABOUT_US" ||
          section.name == "SETTINGS" ||
          section.name == "TERMS_AND_CONDITIONS") {
        if (section.template != null) {
          Box<Asset> boxAsset = await Hive.openBox(
              "ACCOUNT+${section.name}+${section.template!.name[1]}");
          if (boxAsset.isNotEmpty) {
            if (section.name == "VOUCHERS") {
              accountAsset.insert(
                  2,
                  SubAsset.account(
                    section.name,
                    boxAsset.values
                            .firstWhereOrNull(
                                (element) => element.name == "TITLE")
                            ?.data ??
                        "",
                    section.template!.name[0],
                    section.template!.name[1],
                  ));
            } else {
              accountAsset.add(SubAsset.account(
                section.name,
                boxAsset.values
                        .firstWhereOrNull((element) => element.name == "TITLE")
                        ?.data ??
                    "",
                section.template!.name[0],
                section.template!.name[1],
              ));
            }
          }
        }
      } else if (section.name == "EDIT_PROFILE") {
        Box<Asset> boxAsset = await Hive.openBox(
            "ACCOUNT+${section.name}+${section.template!.name[1]}");
        if (boxAsset.isNotEmpty) {
          editProfileAsset = SubAsset.account(
            section.name,
            boxAsset.values
                    .firstWhereOrNull((element) => element.name == "TITLE")
                    ?.data ??
                "",
            section.template!.name[0],
            section.template!.name[1],
          );
        }
      }
    }
  }
  //todo hard code for obriens
  // SubAsset? membershipAsset = accountAsset
  //     .firstWhereOrNull((element) => element.sectionName == "MEMBERSHIP");
  // membershipAsset!.template = "2";
  // print(membershipAsset.template);

  // todo temp hardcode for ameverton (remove if not using)
  accountWidget.clear();
  //accountWidget.add(ProfileTemplate2(theme: "L"));
  // accountWidget.add(Account.BalanceTemplate3(theme: "L"));
  accountWidget.add(Home.BalanceTemplate1(theme: "L"));
  accountWidget.add(ReferralTemplate2(theme: "L"));
  //
}

class SubAsset {
  String sectionName;
  String? title;
  String theme;
  String template;

  SubAsset.auth(this.sectionName, this.theme, this.template);

  SubAsset.account(this.sectionName, this.title, this.theme, this.template);
}
