import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_contacts/contact.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;
import 'package:amverton/Model/feedback_criteria.dart';
import 'package:amverton/Repository/network_service.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hive/hive.dart';
import 'package:http/http.dart';
import 'api.dart' as api;

class ApiPost {
  final NetworkService networkService = NetworkService();

  Future<Map?> assets() async {
    try {
      Box box = Hive.box('box');

      String revisionNumber = "";
      // String revisionNumber = box.get("revision_number") ?? "";

      String secret = important_variables.secret;

      var queryParameters = revisionNumber.isEmpty
          ? {"secret": secret}
          : {
              "secret": secret,
              "revision_number": revisionNumber,
            };

      var uri = Uri.https(api.domain, api.projectName + api.assets);
      //Response response = await networkService.post(uri, body: queryParameters);

      // Simulated JSON response as a string
      const String mockJson = '''
      {
    "status": "success",
    "code": 200,
    "message": "App assets",
    "data": {
        "country_codes": [
            "+60"
        ],
        "globals": [
            {
                "id": 1,
                "name": "LOGO",
                "type": "IMAGE",
                "data": "http://localhost/amverton/merchant/public/storage/logos/MJnumNew8q6T9zOh5LeqYDfgCeiNYvPp3zl842Hr.webp"
            }
        ],
        "pages": [
            {
                "id": 1,
                "name": "INTRO",
                "title": "Intro",
                "sequence": 0,
                "is_selected": 1,
                "is_menu": 0,
                "sections": [
                    {
                        "id": 19,
                        "name": "MAIN",
                        "sequence": 1,
                        "is_selected": 1,
                        "template": {
                            "id": 19,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 2,
                                    "name": "TITLE_1",
                                    "type": "TEXT",
                                    "data": "Our Rooms & Suites"
                                },
                                {
                                    "id": 3,
                                    "name": "TITLE_2",
                                    "type": "TEXT",
                                    "data": "Riverine Splash"
                                },
                                {
                                    "id": 4,
                                    "name": "TITLE_3",
                                    "type": "TEXT",
                                    "data": "Special Rewards"
                                },
                                {
                                    "id": 5,
                                    "name": "SUBTITLE_1",
                                    "type": "TEXT",
                                    "data": "There’s a stay for all at Riverine Hotel"
                                },
                                {
                                    "id": 6,
                                    "name": "SUBTITLE_2",
                                    "type": "TEXT",
                                    "data": "Riverine Splash water park offers an array of slides, rides, and relaxing attractions for all ages."
                                },
                                {
                                    "id": 7,
                                    "name": "SUBTITLE_3",
                                    "type": "TEXT",
                                    "data": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor  incididunt ut labore"
                                },
                                {
                                    "id": 8,
                                    "name": "NEXT_BUTTON_TEXT_1",
                                    "type": "TEXT",
                                    "data": "Next"
                                },
                                {
                                    "id": 9,
                                    "name": "NEXT_BUTTON_TEXT_2",
                                    "type": "TEXT",
                                    "data": "Next"
                                },
                                {
                                    "id": 10,
                                    "name": "NEXT_BUTTON_TEXT_3",
                                    "type": "TEXT",
                                    "data": "Next"
                                },
                                {
                                    "id": 11,
                                    "name": "IMAGE_1",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/ZTF5IoQJGDNuFZo7xzT5kzzK4lDbTGb3KGw8yDVt.png"
                                },
                                {
                                    "id": 12,
                                    "name": "IMAGE_2",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/eAk8A0vQfcPfPHdq23gEbcBN3IsRSFiNFvu4HYNQ.png"
                                },
                                {
                                    "id": 13,
                                    "name": "IMAGE_3",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/nchPlMBSHGa6KYfDIOqEj1RKp0hzCjLot4mQz0Va.png"
                                }
                            ]
                        }
                    }
                ]
            },
            {
                "id": 2,
                "name": "REGISTER",
                "title": "Register",
                "sequence": 0,
                "is_selected": 1,
                "is_menu": 0,
                "sections": [
                    {
                        "id": 20,
                        "name": "STEP_1",
                        "sequence": 1,
                        "is_selected": 1,
                        "template": {
                            "id": 20,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 29,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "Register"
                                },
                                {
                                    "id": 30,
                                    "name": "SUBTITLE",
                                    "type": "TEXT",
                                    "data": "Step 1"
                                },
                                {
                                    "id": 31,
                                    "name": "HEADLINE",
                                    "type": "TEXT",
                                    "data": "Almost There"
                                },
                                {
                                    "id": 32,
                                    "name": "SUBHEADLINE",
                                    "type": "TEXT",
                                    "data": "Fill in your phone number"
                                },
                                {
                                    "id": 33,
                                    "name": "CONTINUE_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "Continue"
                                },
                                {
                                    "id": 34,
                                    "name": "EMAIL",
                                    "type": "TEXT",
                                    "data": "0"
                                },
                                {
                                    "id": 35,
                                    "name": "GENDER",
                                    "type": "TEXT",
                                    "data": "0"
                                },
                                {
                                    "id": 36,
                                    "name": "DATE_OF_BIRTH",
                                    "type": "TEXT",
                                    "data": "0"
                                }
                            ]
                        }
                    },
                    {
                        "id": 21,
                        "name": "STEP_2",
                        "sequence": 2,
                        "is_selected": 1,
                        "template": {
                            "id": 21,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 37,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "Register"
                                },
                                {
                                    "id": 38,
                                    "name": "SUBTITLE",
                                    "type": "TEXT",
                                    "data": "Step 2"
                                },
                                {
                                    "id": 39,
                                    "name": "HEADLINE",
                                    "type": "TEXT",
                                    "data": "Enter Verification Code"
                                },
                                {
                                    "id": 40,
                                    "name": "SUBHEADLINE",
                                    "type": "TEXT",
                                    "data": "6 digit codes has been sent to your register phone number."
                                },
                                {
                                    "id": 41,
                                    "name": "CONTINUE_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": null
                                },
                                {
                                    "id": 132,
                                    "name": "UNDELIVERED_TEXT",
                                    "type": "TEXT",
                                    "data": "Did not receive?"
                                },
                                {
                                    "id": 133,
                                    "name": "RESEND_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "Resend In"
                                },
                                {
                                    "id": 134,
                                    "name": "SUBMIT_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "Submit"
                                }
                            ]
                        }
                    },
                    {
                        "id": 22,
                        "name": "STEP_3",
                        "sequence": 3,
                        "is_selected": 1,
                        "template": {
                            "id": 22,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 42,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "Register"
                                },
                                {
                                    "id": 43,
                                    "name": "SUBTITLE",
                                    "type": "TEXT",
                                    "data": "Step 3"
                                },
                                {
                                    "id": 44,
                                    "name": "HEADLINE",
                                    "type": "TEXT",
                                    "data": "Almost There"
                                },
                                {
                                    "id": 45,
                                    "name": "SUBHEADLINE",
                                    "type": "TEXT",
                                    "data": "Fill in the details"
                                },
                                {
                                    "id": 46,
                                    "name": "UNDELIVERED_TEXT",
                                    "type": "TEXT",
                                    "data": null
                                },
                                {
                                    "id": 47,
                                    "name": "RESEND_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": null
                                },
                                {
                                    "id": 48,
                                    "name": "SUBMIT_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": null
                                },
                                {
                                    "id": 135,
                                    "name": "CONTINUE_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "Continue"
                                },
                                {
                                    "id": 136,
                                    "name": "EMAIL",
                                    "type": "TEXT",
                                    "data": "1"
                                },
                                {
                                    "id": 137,
                                    "name": "GENDER",
                                    "type": "TEXT",
                                    "data": "1"
                                },
                                {
                                    "id": 138,
                                    "name": "DATE_OF_BIRTH",
                                    "type": "TEXT",
                                    "data": "1"
                                },
                                {
                                    "id": 140,
                                    "name": "RACE",
                                    "type": "TEXT",
                                    "data": "1"
                                }
                            ]
                        }
                    }
                ]
            },
            {
                "id": 3,
                "name": "LOGIN",
                "title": "Login",
                "sequence": 0,
                "is_selected": 1,
                "is_menu": 0,
                "sections": [
                    {
                        "id": 23,
                        "name": "MAIN",
                        "sequence": 1,
                        "is_selected": 1,
                        "template": {
                            "id": 23,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 24,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "Login"
                                },
                                {
                                    "id": 25,
                                    "name": "CONTINUE_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "Continue"
                                },
                                {
                                    "id": 26,
                                    "name": "FORGOT_PASSWORD_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "Forgot Password"
                                },
                                {
                                    "id": 27,
                                    "name": "REGISTER_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "New User Register Here"
                                },
                                {
                                    "id": 118,
                                    "name": "IMAGE",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/em6M1itN4xhnaL6lMKeR8jh6Xapk69HS8LjjB6T7.webp"
                                }
                            ]
                        }
                    }
                ]
            },
            {
                "id": 4,
                "name": "FORGOT_PASSWORD",
                "title": "Forgot Password",
                "sequence": 0,
                "is_selected": 1,
                "is_menu": 0,
                "sections": [
                    {
                        "id": 24,
                        "name": "MAIN",
                        "sequence": 1,
                        "is_selected": 1,
                        "template": {
                            "id": 24,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 119,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "Forgot Password"
                                },
                                {
                                    "id": 120,
                                    "name": "HEADLINE",
                                    "type": "TEXT",
                                    "data": "Forgotten your password?"
                                },
                                {
                                    "id": 121,
                                    "name": "SUBHEADLINE",
                                    "type": "TEXT",
                                    "data": "Dont' worry, we got this. Please enter your registered phone number"
                                },
                                {
                                    "id": 122,
                                    "name": "SUBMIT_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "Submit"
                                }
                            ]
                        }
                    }
                ]
            },
            {
                "id": 5,
                "name": "RESET_PASSWORD",
                "title": "Reset Password",
                "sequence": 0,
                "is_selected": 1,
                "is_menu": 0,
                "sections": [
                    {
                        "id": 25,
                        "name": "MAIN",
                        "sequence": 1,
                        "is_selected": 1,
                        "template": {
                            "id": 25,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 123,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "Reset Password"
                                },
                                {
                                    "id": 124,
                                    "name": "HEADLINE",
                                    "type": "TEXT",
                                    "data": "Enter a new password"
                                },
                                {
                                    "id": 125,
                                    "name": "SUBHEADLINE",
                                    "type": "TEXT",
                                    "data": "Strong passwords include numbers, letters, and punctuation marks."
                                },
                                {
                                    "id": 126,
                                    "name": "SUBMIT_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "Submit"
                                }
                            ]
                        }
                    }
                ]
            },
            {
                "id": 6,
                "name": "DOWNLOAD",
                "title": "Download",
                "sequence": 0,
                "is_selected": 1,
                "is_menu": 0,
                "sections": [
                    {
                        "id": 26,
                        "name": "MAIN",
                        "sequence": 1,
                        "is_selected": 1,
                        "template": {
                            "id": 26,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 127,
                                    "name": "HEADLINE",
                                    "type": "TEXT",
                                    "data": "Download App"
                                },
                                {
                                    "id": 128,
                                    "name": "SUBHEADLINE",
                                    "type": "TEXT",
                                    "data": "Download App to enjoy membership benefits."
                                }
                            ]
                        }
                    }
                ]
            },
            {
                "id": 7,
                "name": "HOME",
                "title": "Home",
                "sequence": 1,
                "is_selected": 1,
                "is_menu": 1,
                "sections": [
                    {
                        "id": 1,
                        "name": "SLIDESHOW",
                        "sequence": 1,
                        "is_selected": 1,
                        "template": {
                            "id": 1,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 14,
                                    "name": "SLIDESHOW_1",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/0RQ0jWJFruthCXhpS7uGk3UOkNhP7qaryhNGyhwU.png"
                                },
                                {
                                    "id": 15,
                                    "name": "SLIDESHOW_2",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/WWWbrzjd6eyadDcX3YmtK0SVuG7LG22Ng5991Aac.png"
                                },
                                {
                                    "id": 16,
                                    "name": "SLIDESHOW_3",
                                    "type": "IMAGE",
                                    "data": null
                                },
                                {
                                    "id": 17,
                                    "name": "SLIDESHOW_4",
                                    "type": "IMAGE",
                                    "data": null
                                }
                            ]
                        }
                    },
                    {
                        "id": 2,
                        "name": "WELCOME",
                        "sequence": 2,
                        "is_selected": 1,
                        "template": {
                            "id": 2,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 18,
                                    "name": "GUEST_TITLE",
                                    "type": "TEXT",
                                    "data": "Welcome, Guest"
                                },
                                {
                                    "id": 19,
                                    "name": "MEMBER_TITLE",
                                    "type": "TEXT",
                                    "data": "Hi :name, welcome to the club!"
                                }
                            ]
                        }
                    },
                    {
                        "id": 3,
                        "name": "BALANCE",
                        "sequence": 3,
                        "is_selected": 1,
                        "template": {
                            "id": 3,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 20,
                                    "name": "CREDIT_LABEL",
                                    "type": "TEXT",
                                    "data": "Balance"
                                },
                                {
                                    "id": 21,
                                    "name": "CREDIT_UNIT",
                                    "type": "TEXT",
                                    "data": "RM"
                                },
                                {
                                    "id": 22,
                                    "name": "POINT_LABEL",
                                    "type": "TEXT",
                                    "data": "Points"
                                },
                                {
                                    "id": 23,
                                    "name": "POINT_UNIT",
                                    "type": "TEXT",
                                    "data": "points"
                                }
                            ]
                        }
                    },
                    {
                        "id": 5,
                        "name": "GAMIFICATION",
                        "sequence": 4,
                        "is_selected": 1,
                        "template": {
                            "id": 5,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 28,
                                    "name": "BANNER",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/wwHODDAe3tZ0aaLfUN3kvqlSW9FuA0sE0qdvbBsc.png"
                                },
                                {
                                    "id": 63,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "test"
                                },
                                {
                                    "id": 64,
                                    "name": "PLAY_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "test"
                                }
                            ]
                        }
                    },
                    {
                        "id": 7,
                        "name": "REFERRAL",
                        "sequence": 5,
                        "is_selected": 1,
                        "template": {
                            "id": 7,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 52,
                                    "name": "INVITE_FRIEND_TEXT",
                                    "type": "TEXT",
                                    "data": "Invite More Friends"
                                },
                                {
                                    "id": 53,
                                    "name": "NEW_SIGN_UP_TEXT",
                                    "type": "TEXT",
                                    "data": "New Sign Up"
                                },
                                {
                                    "id": 54,
                                    "name": "INVITE_FRIEND_BACKGROUND",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/qtBW729DZ3mBazFDypDJMSTOKqwmHeGBD03wuYSa.png"
                                },
                                {
                                    "id": 55,
                                    "name": "NEW_SIGN_UP_BACKGROUND",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/jVpYHeXdutJJV36fn0CrOKulDhJutBA3OIVSDAhQ.png"
                                },
                                {
                                    "id": 65,
                                    "name": "DESCRIPTION",
                                    "type": "TEXT",
                                    "data": "Invite more friends to join in the fun, the more invites the more rewards."
                                },
                                {
                                    "id": 66,
                                    "name": "BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "Invite Friends Now"
                                }
                            ]
                        }
                    },
                    {
                        "id": 6,
                        "name": "REWARDS",
                        "sequence": 6,
                        "is_selected": 1,
                        "template": {
                            "id": 6,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 50,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "Promotions"
                                },
                                {
                                    "id": 51,
                                    "name": "HORIZONTAL_1",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/1h4kiiHCsOK7su9jAW8gh0WeHyCd6Gv5v0LFIY4k.png"
                                },
                                {
                                    "id": 67,
                                    "name": "CLAIM_VOUCHER_TEXT",
                                    "type": "TEXT",
                                    "data": "Birthday Rewards"
                                },
                                {
                                    "id": 68,
                                    "name": "BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "More Details"
                                },
                                {
                                    "id": 69,
                                    "name": "REWARD_BACKGROUND",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/w1B0MTn2vCeLuRn0f2teauBkyaesU9hronVhITpd.png"
                                },
                                {
                                    "id": 70,
                                    "name": "DESCRIPTION",
                                    "type": "TEXT",
                                    "data": "Happy Birthday! heres a gift from Amverton Hotel to you."
                                }
                            ]
                        }
                    },
                    {
                        "id": 4,
                        "name": "BANNER",
                        "sequence": 7,
                        "is_selected": 1,
                        "template": {
                            "id": 4,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 49,
                                    "name": "BANNER",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/RPYyTHgpmyppB6DXGqM47DYDCNrbGH31D7dJkXSb.png"
                                }
                            ]
                        }
                    }
                ]
            },
            {
                "id": 8,
                "name": "BOOKINGS",
                "title": "Bookings",
                "sequence": 6,
                "is_selected": 1,
                "is_menu": 1,
                "sections": [
                    {
                        "id": 8,
                        "name": "MAIN",
                        "sequence": 1,
                        "is_selected": 1,
                        "template": {
                            "id": 8,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 56,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "Booking"
                                },
                                {
                                    "id": 57,
                                    "name": "BOOK_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "Book Now"
                                }
                            ]
                        }
                    }
                ]
            },
            {
                "id": 9,
                "name": "REWARDS",
                "title": "Rewards",
                "sequence": 7,
                "is_selected": 1,
                "is_menu": 1,
                "sections": [
                    {
                        "id": 9,
                        "name": "VOUCHERS",
                        "sequence": 2,
                        "is_selected": 1,
                        "template": {
                            "id": 9,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 58,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "Voucher Reward"
                                },
                                {
                                    "id": 59,
                                    "name": "REDEEM_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "redeem"
                                }
                            ]
                        }
                    },
                    {
                        "id": 10,
                        "name": "CHECK_IN",
                        "sequence": 3,
                        "is_selected": 1,
                        "template": {
                            "id": 10,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 60,
                                    "name": "GUEST_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "Login or Register"
                                },
                                {
                                    "id": 61,
                                    "name": "MEMBER_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "Check in"
                                },
                                {
                                    "id": 62,
                                    "name": "COMING_SOON_IMAGE",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/0j9aoZVqTWpluSLoetbz52e3awrzpvZJTQUQVPxP.png"
                                },
                                {
                                    "id": 139,
                                    "name": "INFORMATION",
                                    "type": "TEXT",
                                    "data": "<p>Log in daily to Concorde mobile app to collect vouchers and points. Redeem exclusive rewards with your earned coins and enjoy special perks!</p>"
                                }
                            ]
                        }
                    },
                    {
                        "id": 11,
                        "name": "FEATURED",
                        "sequence": 4,
                        "is_selected": 1,
                        "template": {
                            "id": 11,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 114,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "This Month's Special"
                                },
                                {
                                    "id": 115,
                                    "name": "REDEEM_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "Redeem"
                                }
                            ]
                        }
                    }
                ]
            },
            {
                "id": 10,
                "name": "ACCOUNT",
                "title": "Account",
                "sequence": 8,
                "is_selected": 1,
                "is_menu": 1,
                "sections": [
                    {
                        "id": 12,
                        "name": "PROFILE",
                        "sequence": 1,
                        "is_selected": 1,
                        "template": {
                            "id": 12,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 75,
                                    "name": "GUEST_TITLE",
                                    "type": "TEXT",
                                    "data": "Welcome, Guest"
                                },
                                {
                                    "id": 76,
                                    "name": "MEMBER_TITLE",
                                    "type": "TEXT",
                                    "data": "Hi :name, welcome to the club!"
                                }
                            ]
                        }
                    },
                    {
                        "id": 13,
                        "name": "BALANCE",
                        "sequence": 2,
                        "is_selected": 1,
                        "template": {
                            "id": 13,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 71,
                                    "name": "CREDIT_LABEL",
                                    "type": "TEXT",
                                    "data": "Balance"
                                },
                                {
                                    "id": 72,
                                    "name": "CREDIT_UNIT",
                                    "type": "TEXT",
                                    "data": "RM"
                                },
                                {
                                    "id": 73,
                                    "name": "POINT_LABEL",
                                    "type": "TEXT",
                                    "data": "Points"
                                },
                                {
                                    "id": 74,
                                    "name": "POINT_UNIT",
                                    "type": "TEXT",
                                    "data": "points"
                                }
                            ]
                        }
                    },
                    {
                        "id": 14,
                        "name": "ORDER_HISTORY",
                        "sequence": 3,
                        "is_selected": 1,
                        "template": {
                            "id": 14,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 77,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "My Orders"
                                }
                            ]
                        }
                    },
                    {
                        "id": 15,
                        "name": "MEMBERSHIP",
                        "sequence": 4,
                        "is_selected": 1,
                        "template": {
                            "id": 15,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 84,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "Membership"
                                },
                                {
                                    "id": 85,
                                    "name": "BENEFIT_TITLE",
                                    "type": "TEXT",
                                    "data": "My Benefit"
                                },
                                {
                                    "id": 86,
                                    "name": "BENEFIT_HEADLINE",
                                    "type": "TEXT",
                                    "data": "Enjoy these member's benefits'"
                                },
                                {
                                    "id": 87,
                                    "name": "BENEFIT_TEXT_1",
                                    "type": "TEXT",
                                    "data": "Member's Benefits"
                                },
                                {
                                    "id": 88,
                                    "name": "BENEFIT_TEXT_2",
                                    "type": "TEXT",
                                    "data": "Birthday Voucher"
                                },
                                {
                                    "id": 89,
                                    "name": "BENEFIT_TEXT_3",
                                    "type": "TEXT",
                                    "data": "Member Day Special"
                                },
                                {
                                    "id": 90,
                                    "name": "BENEFIT_TEXT_4",
                                    "type": "TEXT",
                                    "data": "VIP Voucher"
                                },
                                {
                                    "id": 91,
                                    "name": "TIER_TITLE_1",
                                    "type": "TEXT",
                                    "data": "Bronze"
                                },
                                {
                                    "id": 92,
                                    "name": "TIER_DESCRIPTION_1",
                                    "type": "TEXT",
                                    "data": "1.0% Coins rebate per RM spent."
                                },
                                {
                                    "id": 93,
                                    "name": "TIER_TITLE_2",
                                    "type": "TEXT",
                                    "data": "Silver"
                                },
                                {
                                    "id": 94,
                                    "name": "TIER_DESCRIPTION_2",
                                    "type": "TEXT",
                                    "data": "1.5% Coins rebate per RM spent."
                                },
                                {
                                    "id": 95,
                                    "name": "TIER_TITLE_3",
                                    "type": "TEXT",
                                    "data": "Gold"
                                },
                                {
                                    "id": 96,
                                    "name": "TIER_DESCRIPTION_3",
                                    "type": "TEXT",
                                    "data": "2.0% Coins rebate per RM spent."
                                },
                                {
                                    "id": 97,
                                    "name": "TIER_TITLE_4",
                                    "type": "TEXT",
                                    "data": "Diamond"
                                },
                                {
                                    "id": 98,
                                    "name": "TIER_DESCRIPTION_4",
                                    "type": "TEXT",
                                    "data": "2.5% Coins rebate per RM spent."
                                },
                                {
                                    "id": 99,
                                    "name": "TIER_TITLE_5",
                                    "type": "TEXT",
                                    "data": "Platinum"
                                },
                                {
                                    "id": 100,
                                    "name": "TIER_DESCRIPTION_5",
                                    "type": "TEXT",
                                    "data": "3.0% Coins rebate per RM spent."
                                },
                                {
                                    "id": 101,
                                    "name": "INFORMATION",
                                    "type": "TEXT",
                                    "data": "<p><strong>Unlock Exclusive Rewards with Amverton Cove's Tiered Membership!</strong></p>"
                                },
                                {
                                    "id": 102,
                                    "name": "BANNER",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/dkL3aLrfle0HeuiGrwvjvf2SMeqDTRTU01LYfA7a.png"
                                },
                                {
                                    "id": 103,
                                    "name": "BENEFIT_ICON_1",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/Wvyn9Lq9g5gyRRPG9pyKKxxw9syh9slCDMpgugWu.png"
                                },
                                {
                                    "id": 104,
                                    "name": "BENEFIT_ICON_2",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/0qmbijT089AwUxLSmgUw2myZ0fn4L0WbNtsXOYTv.png"
                                },
                                {
                                    "id": 105,
                                    "name": "BENEFIT_ICON_3",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/934ZKJvi2W330KSo5v1z04oJarvFeLD4pLSTnpaC.png"
                                },
                                {
                                    "id": 106,
                                    "name": "BENEFIT_ICON_4",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/zYTV0W6fcEMl611fVSC4VHY9USJ0GYdDlwigspfT.png"
                                }
                            ]
                        }
                    },
                    {
                        "id": 16,
                        "name": "REFERRAL",
                        "sequence": 5,
                        "is_selected": 1,
                        "template": {
                            "id": 16,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 107,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "Friends Invitations"
                                },
                                {
                                    "id": 108,
                                    "name": "HEADLINE",
                                    "type": "TEXT",
                                    "data": "Invite your friends to unlock greater rewards"
                                },
                                {
                                    "id": 109,
                                    "name": "SUBHEADLINE",
                                    "type": "TEXT",
                                    "data": "Share via social media"
                                },
                                {
                                    "id": 110,
                                    "name": "REWARD_TITLE",
                                    "type": "TEXT",
                                    "data": "Rewards"
                                },
                                {
                                    "id": 111,
                                    "name": "COLLECT_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "Collect"
                                },
                                {
                                    "id": 112,
                                    "name": "INFORMATION",
                                    "type": "TEXT",
                                    "data": "<h2>Invite Your Friends &amp; Earn Rewards!</h2>"
                                },
                                {
                                    "id": 113,
                                    "name": "BANNER",
                                    "type": "IMAGE",
                                    "data": "https://test.invoke.my/amverton/1.0.0/merchant/public/storage/assets/ERQuVjBFRuTB1nuXmJXImYqsiAwqFUYXQhDCMO0o.png"
                                }
                            ]
                        }
                    },
                    {
                        "id": 18,
                        "name": "CUSTOMER_SUPPORT",
                        "sequence": 6,
                        "is_selected": 1,
                        "template": {
                            "id": 18,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 81,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "Help Center"
                                },
                                {
                                    "id": 82,
                                    "name": "HEADLINE",
                                    "type": "TEXT",
                                    "data": "Chat with us"
                                },
                                {
                                    "id": 83,
                                    "name": "SUBHEADLINE",
                                    "type": "TEXT",
                                    "data": "How can we help, let's chat"
                                }
                            ]
                        }
                    },
                    {
                        "id": 17,
                        "name": "FEEDBACK",
                        "sequence": 7,
                        "is_selected": 1,
                        "template": {
                            "id": 17,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 78,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "Feedback"
                                },
                                {
                                    "id": 79,
                                    "name": "HEADLINE",
                                    "type": "TEXT",
                                    "data": "Your feedback makes a different"
                                },
                                {
                                    "id": 80,
                                    "name": "SUBHEADLINE",
                                    "type": "TEXT",
                                    "data": "Let us know, how can we help to improve"
                                }
                            ]
                        }
                    },
                    {
                        "id": 27,
                        "name": "SETTINGS",
                        "sequence": 8,
                        "is_selected": 1,
                        "template": {
                            "id": 27,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 116,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "Settings"
                                },
                                {
                                    "id": 117,
                                    "name": "BACKGROUND_COLOR",
                                    "type": "COLOR",
                                    "data": "000000"
                                }
                            ]
                        }
                    },
                    {
                        "id": 28,
                        "name": "VOUCHERS",
                        "sequence": 8,
                        "is_selected": 1,
                        "template": {
                            "id": 28,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 129,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "Rewards"
                                }
                            ]
                        }
                    },
                    {
                        "id": 29,
                        "name": "EDIT_PROFILE",
                        "sequence": 13,
                        "is_selected": 1,
                        "template": {
                            "id": 29,
                            "name": "L1",
                            "is_selected": 1,
                            "assets": [
                                {
                                    "id": 130,
                                    "name": "TITLE",
                                    "type": "TEXT",
                                    "data": "Edit Profile"
                                },
                                {
                                    "id": 131,
                                    "name": "SAVE_BUTTON_TEXT",
                                    "type": "TEXT",
                                    "data": "Save"
                                }
                            ]
                        }
                    }
                ]
            }
        ],
        "settings": {
            "logins": {
                "has_password_login": 0,
                "has_sms_login": 0,
                "has_whatspp_login": 0,
                "has_facebook_login": 0,
                "has_google_login": 0,
                "has_apple_login": 0
            },
            "delivery_options": {
                "has_delivery": 0,
                "has_self_pickup": 0
            }
        },
        "latest_revision_number": "1732159739"
    }
}
      ''';

      // Hardcoded Response object
      //Response response = Response(mockJson, 200);

      Map data = jsonDecode(mockJson);
      if (data["code"] == 400) {
        if (data['message'] == "The selected revision number is invalid.") {
          await box.delete("revision_number");
          return assets();
        }
      }
      return data;
    } catch (e) {
      print("Errors found in assets(POST): $e");
      return null;
    }
  }

  Future<int?> accessTokenRefresh() async {
    Box box = await Hive.openBox('box');
    String? refreshToken = box.get("refresh_token");

    if (refreshToken != null) {
      try {
        var queryParameters = {'refresh_token': refreshToken};

        var uri = Uri.https(api.domain,
            api.projectName + api.accessTokenRefresh, queryParameters);
        Response response = await networkService.post(uri, headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
        });

        Map data = jsonDecode(response.body);
        if (data['code'] == 200) {
          await box.put("access_token", data['data']['access_token']);
          await box.put("refresh_token", data['data']['refresh_token']);
          await box.put("is_login", true);
        } else {
          await box.delete("access_token");
          await box.delete("refresh_token");
          await box.put("is_login", false);
        }
        return data['code'];
      } catch (e) {
        print("Errors found in accessTokenRefresh(POST): $e");
        await box.delete("access_token");
        await box.delete("refresh_token");
        await box.put("is_login", false);
        return null;
      }
    } else {
      await box.delete("access_token");
      await box.delete("refresh_token");
      await box.put("is_login", false);
      return null;
    }
  }

  Future<Map?> login(
      String username, String password) async {
    try {
      var queryParameters = {
        "username": username,
        "password": password,
      };
    
      var uri = Uri.https(api.domain, api.projectName + api.login);
      Response response = await networkService.post(uri, body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 200) {
        Box box = await Hive.openBox('box');
        await box.put("access_token", data['data']['access_token']);
        await box.put("refresh_token", data['data']['refresh_token']);
        await box.put("is_login", true);
      }
      return data;
    } catch (e) {
      print("Errors found in login(POST): $e");
      return null;
    }
  }

  Future<Map?> loginWithPassword(
      String countryCode, String phoneNumber, String password) async {
    try {
      var queryParameters = {
        "country_code": countryCode,
        "phone": phoneNumber,
        "password": password,
      };

      var uri = Uri.https(api.domain, api.projectName + api.loginWithPassword);
      Response response = await networkService.post(uri, body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 200) {
        Box box = await Hive.openBox('box');
        await box.put("access_token", data['data']['access_token']);
        await box.put("refresh_token", data['data']['refresh_token']);
        await box.put("is_login", true);
      }
      return data;
    } catch (e) {
      print("Errors found in loginWithPassword(POST): $e");
      return null;
    }
  }

  Future<Map?> loginAsStaff(String username, String password) async {
    try {
      var queryParameters = {
        "username": username,
        "password": password,
      };

      var uri = Uri.https(api.domain, api.projectName + api.loginWithPassword);
      Response response = await networkService.post(uri, body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 200) {
        Box box = await Hive.openBox('box');
        await box.put("access_token", data['data']['access_token']);
        await box.put("refresh_token", data['data']['refresh_token']);
        await box.put("is_login", true);
      }
      return data;
    } catch (e) {
      print("Errors found in loginAsStaff(POST): $e");
      return null;
    }
  }

  Future<Map?> loginWithOTP(
      String countryCode, String phoneNumber, String sendVia) async {
    try {
      var queryParameters = {
        "country_code": countryCode,
        "send_via": sendVia,
        "phone": phoneNumber,
      };

      var uri = Uri.https(api.domain, api.projectName + api.loginWithOTP);
      Response response = await networkService.post(uri, body: queryParameters);

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in loginWithOTP(POST): $e");
      return null;
    }
  }

  Future<Map?> verifyOTPLogin(
      String countryCode, String phone, String code) async {
    try {
      var queryParameters = {
        "country_code": countryCode,
        "phone": phone,
        "code": code,
      };

      var uri = Uri.https(api.domain, api.projectName + api.verifyOTPLogin);
      Response response = await networkService.post(uri, body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 200) {
        Box box = await Hive.openBox('box');
        await box.put("access_token", data['data']['access_token']);
        await box.put("refresh_token", data['data']['refresh_token']);
        await box.put("is_login", true);
      }
      return data;
    } catch (e) {
      print("Errors found in verifyOTPLogin(POST): $e");
      return null;
    }
  }

  Future<Map?> loginWithSocial(String socialId) async {
    try {
      var queryParameters = {
        "social_id": socialId,
      };

      var uri = Uri.https(api.domain, api.projectName + api.loginWithSocial);
      Response response = await networkService.post(uri, body: queryParameters);

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in loginWithSocial(POST): $e");
      return null;
    }
  }

  Future<Map?> register(
      String firstName,
      String lastName,
      String phoneNumber,
      String? dob,
      String username,
      String password,
      String passwordConfirmation,
      String? email,
      String agree) async {
    try {

      var queryParameters = {
        "first_name": firstName,
        "last_name": lastName,
        "phone": phoneNumber,
        "date_of_birth": dob,
        "username": username,
        "password": password,
        "password_confirmation": passwordConfirmation,  
        "email": email, 
        "agree": agree,
      };

      var uri = Uri.https(api.domain, api.projectName + api.register);
      Response response = await networkService.post(uri, body: queryParameters);

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in register(POST): $e");
      return null;
    }
  }

  Future<Map?> socialRegister(
    String socialId,
    String countryCode,
    String phoneNumber,
    String name,
    String? disableNotification,
  ) async {
    try {
      var queryParameters = {
        "social_id": socialId,
        "country_code": countryCode,
        "phone": phoneNumber,
        "name": name,
        "disable_notification": disableNotification,
      };

      var uri = Uri.https(api.domain, api.projectName + api.socialRegister);
      Response response = await networkService.post(uri, body: queryParameters);

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in socialRegister(POST): $e");
      return null;
    }
  }

  Future<Map?> requestVerificationCode(
      String countryCode, String phoneNumber, String sendVia) async {
    try {
      var queryParameters = {
        "country_code": countryCode,
        "phone": phoneNumber,
        "send_via": sendVia,
      };

      var uri =
          Uri.https(api.domain, api.projectName + api.requestVerificationCode);
      Response response = await networkService.post(uri, body: queryParameters);
      
      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in requestVerificationCode(POST): $e");
      return null;
    }
  }

  Future<Map?> verifyPhone(
      String countryCode, String phoneNumber, String verificationCode) async {
    try {
      var queryParameters = {
        "country_code": countryCode,
        "phone": phoneNumber,
        "code": verificationCode,
      };

      var uri = Uri.https(api.domain, api.projectName + api.verifyPhone);
      Response response = await networkService.post(uri, body: queryParameters);

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in verifyPhone(POST): $e");
      return null;
    }
  }

  // Future<Map?> requestResetPasswordCode(
  //     String countryCode, String phoneNumber) async {
  //   try {
  //     var queryParameters = {
  //       "country_code": countryCode,
  //       "phone": phoneNumber,
  //     };

  //     var uri =
  //         Uri.https(api.domain, api.projectName + api.requestResetPasswordCode);
  //     Response response = await networkService.post(uri, body: queryParameters);

  //     Map data = jsonDecode(response.body);
  //     return data;
  //   } catch (e) {
  //     print("Errors found in requestResetPasswordCode(POST): $e");
  //     return null;
  //   }
  // }

  
  Future<Map?> requestResetPasswordCode(
      String email) async {
    try {
      var queryParameters = {
        "email": email
      };

      var uri =
          Uri.https(api.domain, api.projectName + api.requestResetPasswordCode);
      Response response = await networkService.post(uri, body: queryParameters);

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in requestResetPasswordCode(POST): $e");
      return null;
    }
  }

  Future<Map?> resetPassword(
      String verificationCode, String password, String passwordConfirm) async {
    try {
      var queryParameters = {
        "code": verificationCode,
        "new_password": password,
        "new_password_confirmation": passwordConfirm
      };

      var uri = Uri.https(api.domain, api.projectName + api.resetPassword);
      Response response = await networkService.post(uri, body: queryParameters);

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in resetPassword(POST): $e");
      return null;
    }
  }

  Future<Map?> checkIn() async {
    try {
      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.checkIn);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'});

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return checkIn();
        }
      }
      return data;
    } catch (e) {
      print("Errors found in checkIn(POST): $e");
      return null;
    }
  }

  Future<Map?> storeCart(
      int outletId,
      int productId,
      int quantity,
      String? remark,
      List<int>? bundleItemId,
      List<int>? modifierItemId) async {
    try {
      Map<String, dynamic> queryParameters = {
        "outlet_id": outletId.toString(),
        "product_id": productId.toString(),
        "quantity": quantity.toString(),
        "remark": remark,
        "bundle_item_ids": bundleItemId,
        "modifier_option_ids": modifierItemId,
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.storeCart);
      Response response = await networkService.post(uri,
          headers: {
            HttpHeaders.authorizationHeader: 'Bearer $accessToken',
            HttpHeaders.contentTypeHeader: 'application/json',
          },
          body: json.encode(queryParameters));

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return storeCart(outletId, productId, quantity, remark, bundleItemId,
              modifierItemId);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in storeCart(POST): $e");
      return null;
    }
  }

  Future<Map?> updateCart(
      int outletId,
      int? id,
      int? quantity,
      String? remark,
      int? voucherId,
      List<int>? bundleItemId,
      List<int>? modifierItemId) async {
    try {
      List item = [];

      if (id != null) {
        item = List.generate(
          1,
          (index) => {
            "id": id.toString(),
            "quantity": quantity.toString(),
            "remark": remark,
            "bundle_item_ids": bundleItemId,
            "modifier_option_ids": modifierItemId,
          },
        );
      }

      Map<String, dynamic> queryParameters = item.isEmpty
          ? {
              "outlet_id": outletId.toString(),
              "voucher_id": voucherId,
            }
          : {
              "outlet_id": outletId.toString(),
              "items": item,
              "voucher_id": voucherId,
            };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.updateCart);
      Response response = await networkService.post(uri,
          headers: {
            HttpHeaders.authorizationHeader: 'Bearer $accessToken',
            HttpHeaders.contentTypeHeader: 'application/json',
          },
          body: json.encode(queryParameters));

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return updateCart(outletId, id, quantity, remark, voucherId,
              bundleItemId, modifierItemId);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in updateCart(POST): $e");
      return null;
    }
  }

  Future<Map?> deleteCart(int id, int? voucherId) async {
    try {
      var queryParameters = voucherId == null
          ? {"id": id.toString()}
          : {"id": id.toString(), "voucher_id": voucherId.toString()};

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.deleteCart);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return deleteCart(id, voucherId);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in deleteCart(POST): $e");
      return null;
    }
  }

  Future<Map?> makeBooking(int outletId, String name, String countryCode,
      String phoneNumber, int numberGuests, String dateTime) async {
    try {
      var queryParameters = {
        "outlet_id": outletId.toString(),
        "name": name,
        "country_code": countryCode,
        "phone": phoneNumber,
        "number_of_guests": numberGuests.toString(),
        "datetime": dateTime,
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.makeBooking);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return makeBooking(
              outletId, name, countryCode, phoneNumber, numberGuests, dateTime);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in makeBooking(POST): $e");
      return null;
    }
  }

  Future<Map?> updateBooking(
      int id,
      int outletId,
      String name,
      String countryCode,
      String phoneNumber,
      int numberGuests,
      String dateTime) async {
    try {
      var queryParameters = {
        "id": id.toString(),
        "outlet_id": outletId.toString(),
        "name": name,
        "country_code": countryCode,
        "phone": phoneNumber,
        "number_of_guests": numberGuests,
        "datetime": dateTime,
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.updateBooking);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return updateBooking(id, outletId, name, countryCode, phoneNumber,
              numberGuests, dateTime);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in updateBooking(POST): $e");
      return null;
    }
  }

  Future<Map?> deleteBooking(int id) async {
    try {
      var queryParameters = {
        "id": id.toString(),
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.deleteBooking);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return deleteBooking(id);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in deleteBooking(POST): $e");
      return null;
    }
  }

  Future<Map?> makeOrder(
      List<int> cartId,
      int? voucherId,
      String paymentCode,
      String? brandCode,
      String deliveryOption,
      String? selectedSchedule,
      double? credit,
      double? point,
      int? addressId,
      String? remark) async {
    try {
      Map<String, dynamic> queryParameters = deliveryOption == "DELIVERY"
          ? {
              "cart_item_ids": cartId,
              "payment_code": brandCode ?? paymentCode,
              "delivery_method": deliveryOption,
              "credit": credit,
              "point": point,
              "voucher_id": voucherId,
              "address_id": addressId,
              "delivery_time": selectedSchedule,
              "remark": remark,
            }
          : {
              "cart_item_ids": cartId,
              "payment_code": brandCode ?? paymentCode,
              "delivery_method": deliveryOption,
              "credit": credit,
              "point": point,
              "voucher_id": voucherId,
              "address_id": addressId,
              "pickup_time": selectedSchedule,
              "remark": remark,
            };

      print(queryParameters);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.makeOrder);

      Response response = await networkService.post(
        uri,
        headers: {
          HttpHeaders.authorizationHeader: 'Bearer $accessToken',
          HttpHeaders.contentTypeHeader: 'application/json',
        },
        body: json.encode(queryParameters),
      );

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return makeOrder(
              cartId,
              voucherId,
              paymentCode,
              brandCode,
              deliveryOption,
              selectedSchedule,
              credit,
              point,
              addressId,
              remark);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in makeOrder(POST): $e");
      return null;
    }
  }

  Future<Map?> remakePayment(int id) async {
    try {
      var queryParameters = {
        "id": id.toString(),
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.remakePayment);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return remakePayment(id);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in remakePayment(POST): $e");
      return null;
    }
  }

  Future<Map?> reorder(int id) async {
    try {
      var queryParameters = {
        "id": id.toString(),
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.reorder);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return reorder(id);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in reorder(POST): $e");
      return null;
    }
  }

  Future<Map?> cancelOrder(int id) async {
    try {
      var queryParameters = {
        "id": id.toString(),
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.cancelOrder);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return cancelOrder(id);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in cancelOrder(POST): $e");
      return null;
    }
  }

  Future<Map?> redeemPointsRewards(int id) async {
    try {
      var queryParameters = {
        "id": id.toString(),
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri =
          Uri.https(api.domain, api.projectName + api.redeemPointsRewards);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return redeemPointsRewards(id);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in redeemPointsRewards(POST): $e");
      return null;
    }
  }

  Future<Map?> meUpdate(String name, String? gender, String? race, String? dob,
      String? email) async {
    try {
      if (gender == "-") gender = "";
      var queryParameters = {
        "name": name,
        "gender": gender.toString(),
        "race": race.toString(),
        "date_of_birth": dob,
        "email": email,
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.meUpdate);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return meUpdate(name, gender, race, dob, email);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in meUpdate(POST): $e");
      return null;
    }
  }

  Future<Map?> meUpdatePassword(
      String current, String newPass, String confirmNew) async {
    try {
      var queryParameters = {
        "current_password": current,
        'new_password': newPass,
        "new_password_confirmation": confirmNew,
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.meUpdatePassword);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return meUpdatePassword(current, newPass, confirmNew);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in meUpdatePassword(POST): $e");
      return null;
    }
  }

  Future<Map?> meDeleteAccount() async {
    try {
      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.meDeleteAccount);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'});

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return meDeleteAccount();
        }
      }
      return data;
    } catch (e) {
      print("Errors found in meDeleteAccount(POST): $e");
      return null;
    }
  }

  Future<Map?> submitFeedback(int outletId, List<FeedbackCriteria> criteriaList,
      List<int> rate, String comment) async {
    try {
      List criteria = List.generate(
        criteriaList.length,
        (index) => {
          "id": criteriaList[index].id.toString(),
          "rating": rate[index].toString()
        },
      );

      Map<String, dynamic> queryParameters = {
        "outlet_id": outletId.toString(),
        'feedback_criteria': criteria,
        "comment": comment,
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.submitFeedback);
      Response response = await networkService.post(uri,
          headers: {
            HttpHeaders.authorizationHeader: 'Bearer $accessToken',
            HttpHeaders.contentTypeHeader: 'application/json',
          },
          body: json.encode(queryParameters));

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return submitFeedback(outletId, criteriaList, rate, comment);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in submitFeedback(POST): $e");
      return null;
    }
  }

  Future<Map?> topUpCredit(int packageId, String paymentCode, String brandCode,
      Position? location) async {
    try {
      var queryParameters = location == null
          ? {
              "credit_pack_id": packageId.toString(),
              "payment_code": paymentCode,
              "brand_code": brandCode,
            }
          : {
              "credit_pack_id": packageId.toString(),
              "payment_code": paymentCode,
              "brand_code": brandCode,
              'latitude': location.latitude.toString(),
              'longitude': location.longitude.toString(),
            };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.topUpCredit);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return topUpCredit(packageId, paymentCode, brandCode, location);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in topUpCredit(POST): $e");
      return null;
    }
  }

  Future<Map?> markAsRead(int id) async {
    try {
      var queryParameters = {
        "id": id.toString(),
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.markAsRead);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return markAsRead(id);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in markAsRead(POST): $e");
      return null;
    }
  }

  Future<Map?> saveNotificationToken(String? token) async {
    try {
      var queryParameters = {
        "token": token,
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri =
          Uri.https(api.domain, api.projectName + api.saveNotificationToken);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return saveNotificationToken(token);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in saveNotificationToken(POST): $e");
      return null;
    }
  }

  Future<Map?> claimReferralRewards() async {
    try {
      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri =
          Uri.https(api.domain, api.projectName + api.claimReferralRewards);
      Response response = await networkService.post(
        uri,
        headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
      );

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return claimReferralRewards();
        }
      }
      return data;
    } catch (e) {
      print("Errors found in claimReferralRewards(POST): $e");
      return null;
    }
  }

  Future<Map?> updateSettings(
      bool pushNotification, bool email, bool sms) async {
    try {
      var queryParameters = {
        "push_notification": pushNotification ? "1" : "0",
        "email": email ? "1" : "0",
        "sms": sms ? "1" : "0",
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.updateSettings);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return updateSettings(pushNotification, email, sms);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in updateSettings(POST): $e");
      return null;
    }
  }

  Future<Map?> storeAddress(
    String address1,
    String? address2,
    String postcode,
    String city,
    String? stateId,
    String? countryId,
    String name,
    String phone,
    double? latitude,
    double? longitude,
  ) async {
    try {
      var queryParameters = {
        "address_1": address1,
        "address_2": address2,
        "postcode": postcode,
        "city": city,
        "state_id": stateId,
        "country_id": countryId,
        "name": name,
        "phone": phone,
        "latitude": latitude.toString(),
        "longitude": longitude.toString(),
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.storeAddress);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return storeAddress(address1, address2, postcode, city, stateId,
              countryId, name, phone, latitude, longitude);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in storeAddress(POST): $e");
      return null;
    }
  }

  Future<Map?> updateAddress(
    int id,
    String address1,
    String? address2,
    String postcode,
    String city,
    String? stateId,
    String? countryId,
    String name,
    String phone,
    double? latitude,
    double? longitude,
  ) async {
    try {
      var queryParameters = {
        "id": id.toString(),
        "address_1": address1,
        "address_2": address2,
        "postcode": postcode,
        "city": city,
        "state_id": stateId,
        "country_id": countryId,
        "name": name,
        "phone": phone,
        "latitude": latitude.toString(),
        "longitude": longitude.toString(),
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.updateAddress);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return updateAddress(id, address1, address2, postcode, city, stateId,
              countryId, name, phone, latitude, longitude);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in updateAddress(POST): $e");
      return null;
    }
  }

  Future<Map?> setDefaultAddress(int id) async {
    try {
      var queryParameters = {"id": id.toString()};

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.setDefaultAddress);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return setDefaultAddress(id);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in setDefaultAddress(POST): $e");
      return null;
    }
  }

  Future<Map?> deleteAddress(int id) async {
    try {
      var queryParameters = {"id": id.toString()};

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.deleteAddress);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return deleteAddress(id);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in deleteAddress(POST): $e");
      return null;
    }
  }

  Future<Map?> sendGiftCard(
      int giftCardDesignId,
      String amount,
      String senderName,
      String receiverName,
      String receiverCountryCode,
      String receiverPhone,
      String message) async {
    try {
      var queryParameters = {
        "gift_card_design_id": giftCardDesignId.toString(),
        "amount": amount,
        "sender_name": senderName,
        "receiver_name": receiverName,
        "receiver_country_code": receiverCountryCode,
        "receiver_phone": receiverPhone,
        "message": message,
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.sendGiftCard);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return sendGiftCard(giftCardDesignId, amount, senderName,
              receiverName, receiverCountryCode, receiverPhone, message);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in sendGiftCard(POST): $e");
      return null;
    }
  }

  Future<Map?> redeemGiftCard(int? id, String? code) async {
    try {
      var queryParameters = id == null ? {"code": code} : {"id": id.toString()};

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.redeemGiftCard);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return redeemGiftCard(id, code);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in redeemGiftCard(POST): $e");
      return null;
    }
  }

  Future<Map?> cancelSentGiftCard(int id) async {
    try {
      var queryParameters = {
        "id": id.toString(),
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.cancelSentGiftCard);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return cancelSentGiftCard(id);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in cancelSentGiftCard(POST): $e");
      return null;
    }
  }

  Future<Map?> storeContact(List<Contact> contactList) async {
    try {
      List contact = List.generate(
        contactList.length,
        (index) => {
          "name": contactList[index].displayName,
          "phone": contactList[index].phones.first.number,
        },
      );

      Map<String, dynamic> queryParameters = {
        'contacts': contact,
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.storeContact);

      Response response = await networkService.post(uri,
          headers: {
            HttpHeaders.authorizationHeader: 'Bearer $accessToken',
            HttpHeaders.contentTypeHeader: 'application/json',
          },
          body: json.encode(queryParameters));

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return storeContact(contactList);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in storeContact(POST): $e");
      return null;
    }
  }
}
