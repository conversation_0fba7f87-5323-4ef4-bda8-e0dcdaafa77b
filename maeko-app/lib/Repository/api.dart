// test server
const String domain = "test.invoke.my";
const String projectName = "concorde";
//const String projectName = "amverton/1.0.0";
const String inFront = "/api/public";


// API
// assets
const String assets = inFront + "/assets";

// auth
const String accessTokenRefresh = inFront + "/access-token/refresh";
const String login = inFront + "/login";
const String register = inFront + "/register";
const String requestResetPasswordCode = inFront + "/password/forgot/send";
const String resetPassword = inFront + "/password/reset";
const String requestVerificationCode = inFront + "/phone-verification/send";
const String verifyPhone = inFront + "/phone-verification/verify";

// home
const String home = inFront + "/home";

// points
const String pointList = inFront + "/users/points?filter=obtained";
const String pointObtain = inFront + "/users/points/obtain";
const String pointUsed = inFront + "/users/points/use";

// restaurant
const String restaurantList = inFront + "/restaurants";
const String restaurantDetail = inFront + "/restaurants/";

// memberships
const String memberships = inFront + "/memberships";

// referral
const String referral = inFront + "/referrals";


//below not concorde

const String loginWithPassword = inFront + "/login/password";
const String loginWithOTP = inFront + "/login/otp";
const String verifyOTPLogin = inFront + "/login/otp/verify";
const String loginWithSocial = inFront + "/login/social";

const String socialRegister = inFront + "/register/social";
//const String requestVerificationCode = inFront + "/phone/send";
//const String verifyPhone = inFront + "/phone/verify";
//const String requestResetPasswordCode = inFront + "/password/forgot";
//const String resetPassword = inFront + "/password/reset";

// home
const String checkIn = inFront + "/check-ins/store";

// cart
const String cartList = inFront + "/carts/items";
const String storeCart = inFront + "/carts/items/store";
const String updateCart = inFront + "/carts/items/update";
const String deleteCart = inFront + "/carts/items/destroy";

// outlet
const String outletList = inFront + "/outlets";
const String outletListSearch = inFront + "/outlets/search";
const String outletDetail = inFront + "/outlets/";

// menu
const String menu = inFront + "/menu";
const String menuSearch = inFront + "/menu/search";

// product
const String productDetail = inFront + "/products/";

// booking
const String bookingList = inFront + "/bookings";
const String bookingDetail = inFront + "/bookings/";
const String makeBooking = inFront + "/bookings/store";
const String updateBooking = inFront + "/bookings/update";
const String deleteBooking = inFront + "/bookings/destroy";

//order
const String orderList = inFront + "/orders";
const String orderDetail = inFront + "/orders/";
const String makeOrder = inFront + "/orders/store";
const String remakePayment = inFront + "/orders/payments/store";
const String cancelOrder = inFront + "/orders/cancel";
const String reorder = inFront + "/orders/remake";

// rewards
const String rewards = inFront + "/rewards";
const String redeemPointsRewards = inFront + "/point-rewards/redeem";

// vouchers
const String voucher = inFront + "/vouchers";
const String voucherDetail = inFront + "/vouchers/";

// me
const String me = inFront + "/me";
const String meUpdate = inFront + "/me/update";
const String meUpdatePassword = inFront + "/me/password/update";
const String meDeleteAccount = inFront + "/me/destroy";

// faq
const String faq = inFront + "/questions";
const String faqSearch = inFront + "/questions/search";

// feedback
const String feedbackCriteria = inFront + "/feedback/criteria";
const String submitFeedback = inFront + "/feedback/store";

// credit
const String creditHistory = inFront + "/credits";
const String creditPackage = inFront + "/credits/packs";
const String topUpCredit = inFront + "/credits/reload";

// point history
const String pointHistory = inFront + "/points";

// notification
const String notificationList = inFront + "/notifications";
const String notificationDetail = inFront + "/notifications/";
const String markAsRead = inFront + "/notifications/read";
const String saveNotificationToken = inFront + "/notifications/tokens/store";


const String claimReferralRewards = inFront + "/referrals/rewards/claim";



// customer support
const String customerSupport = inFront + "/customer-support";

// settings
const String settings = inFront + "/settings";
const String updateSettings = inFront + "/settings/update";

// address
const String addressList = inFront + "/addresses";
const String addressDetail = inFront + "/addresses/";
const String storeAddress = inFront + "/addresses/store";
const String updateAddress = inFront + "/addresses/update";
const String setDefaultAddress = inFront + "/addresses/default/update";
const String deleteAddress = inFront + "/addresses/destroy";
const String countryList = inFront + "/countries";

// gift card
const String giftCardList = inFront + "/gift-cards";
const String giftCardDesignList = inFront + "/gift-cards/designs";
const String sendGiftCard = inFront + "/gift-cards/send";
const String cancelSentGiftCard = inFront + "/gift-cards/cancel";
const String redeemGiftCard = inFront + "/gift-cards/redeem";

// contacts
const String contactList = inFront + "/contacts";
const String contactListSearch = inFront + "/contacts/search";
const String storeContact = inFront + "/contacts/store";
