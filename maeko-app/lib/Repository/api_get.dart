import 'dart:convert';
import 'dart:io';
import 'package:amverton/Repository/network_service.dart';
import 'package:hive/hive.dart';
import 'package:http/http.dart';
import 'api.dart' as api;
import 'api_post.dart';

class ApiGet {
  final NetworkService networkService = NetworkService();

  Future<Map?> home() async {
    try {
      Uri uri = Uri.https(api.domain, api.projectName + api.home);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });
      print("Response from home: ${response.body}");
      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return home();
        }
      }
      return data;
    } catch (e) {
      print("Errors found in home(GET): $e");
      return null;
    }
  }

  Future<Map?> cartList(int? outletId) async {
    try {
      final queryParameters =
          outletId == null ? null : {'outlet_id': outletId.toString()};

      Uri uri = Uri.https(
          api.domain, api.projectName + api.cartList, queryParameters);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token");

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return cartList(outletId);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in cartList(GET): $e");
      return null;
    }
  }

  Future<Map?> restaurantList() async {
    try {

      Uri uri = Uri.https(
          api.domain, api.projectName + api.restaurantList);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return restaurantList();
        }
      }
      return data;
    } catch (e) {
      print("Errors found in restaurantList(GET): $e");
      return null;
    }
  }

  Future<Map?> restaurantDetail(int id) async {
    try {
      Uri uri = Uri.https(
          api.domain, api.projectName + api.restaurantDetail + id.toString());

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in restaurantDetail(GET): $e");
      return null;
    }
  }


  Future<Map?> outletList(double? latitude, double? longitude) async {
    try {
      final queryParameters = {
        'latitude': latitude.toString(),
        'longitude': longitude.toString(),
      };

      Uri uri = Uri.https(
          api.domain, api.projectName + api.outletList, queryParameters);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return outletList(latitude, longitude);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in outletList(GET): $e");
      return null;
    }
  }

  Future<Map?> outletListSearch(String? query) async {
    try {
      final queryParameters = {'keyword': query};

      Uri uri = Uri.https(
          api.domain, api.projectName + api.outletListSearch, queryParameters);

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in outletListSearch(GET): $e");
      return null;
    }
  }

  Future<Map?> outletDetail(int id) async {
    try {
      Uri uri = Uri.https(
          api.domain, api.projectName + api.outletDetail + id.toString());

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in outletDetail(GET): $e");
      return null;
    }
  }

  Future<Map?> menu(int? outletId, int? addressId) async {
    try {
      final queryParameters = addressId != null
          ? {'address_id': addressId.toString()}
          : {'outlet_id': outletId.toString()};

      Uri uri =
          Uri.https(api.domain, api.projectName + api.menu, queryParameters);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in menu(GET): $e");
      return null;
    }
  }

  Future<Map?> menuSearch(int outletId, String? query) async {
    try {
      final queryParameters = query == null
          ? {'outlet_id': outletId.toString()}
          : {'outlet_id': outletId.toString(), 'keyword': query};

      Uri uri = Uri.https(
          api.domain, api.projectName + api.menuSearch, queryParameters);

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in menuSearch(GET): $e");
      return null;
    }
  }

  Future<Map?> productDetail(int id) async {
    try {
      Uri uri = Uri.https(
          api.domain, api.projectName + api.productDetail + id.toString());

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in productDetail(GET): $e");
      return null;
    }
  }

  Future<Map?> bookingList() async {
    try {
      Uri uri = Uri.https(api.domain, api.projectName + api.bookingList);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token", defaultValue: "");

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return bookingList();
        }
      }
      return data;
    } catch (e) {
      print("Errors found in bookingList(GET): $e");
      return null;
    }
  }

  Future<Map?> bookingDetail(int id) async {
    try {
      Uri uri = Uri.https(
          api.domain, api.projectName + api.bookingDetail + id.toString());

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token");

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return bookingDetail(id);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in bookingDetail(GET): $e");
      return null;
    }
  }

  Future<Map?> orderList(int nextPage, [String? filter = null]) async {
    try {
      final queryParameters =
          nextPage == 0 ? null : {'page': nextPage.toString()};

      // final queryParameters = null;

      Uri uri = Uri.https(
          api.domain, api.projectName + api.orderList, queryParameters);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return orderList(nextPage, filter);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in orderList(GET): $e");
      return null;
    }
  }

  Future<Map?> orderDetail(int id) async {
    try {
      Uri uri = Uri.https(
          api.domain, api.projectName + api.orderDetail + id.toString());

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token");

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return orderDetail(id);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in orderDetail(GET): $e");
      return null;
    }
  }

  Future<Map?> rewards() async {
    try {
      Uri uri = Uri.https(api.domain, api.projectName + api.rewards);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return rewards();
        }
      }
      return data;
    } catch (e) {
      print("Errors found in rewards(GET): $e");
      return null;
    }
  }

  Future<Map?> voucher(String filter, int nextPage) async {
    try {
      final queryParameters = nextPage == 0
          ? {'filter': filter}
          : {'filter': filter, 'page': nextPage.toString()};

      Uri uri =
          Uri.https(api.domain, api.projectName + api.voucher, queryParameters);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token");

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return voucher(filter, nextPage);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in voucher(GET): $e");
      return null;
    }
  }

  Future<Map?> voucherDetail(int id) async {
    try {
      Uri uri = Uri.https(
          api.domain, api.projectName + api.voucherDetail + id.toString());

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token");

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in voucherDetail(GET): $e");
      return null;
    }
  }

  Future<Map?> me() async {
    // try {
    Uri uri = Uri.https(api.domain, api.projectName + api.me);

    Box box = Hive.box('box');
    final String accessToken = box.get("access_token");

    Response response = await networkService.get(uri, headers: {
      HttpHeaders.authorizationHeader: 'Bearer $accessToken',
      HttpHeaders.contentTypeHeader: 'application/json',
    });

    Map data = jsonDecode(response.body);
    if (data['code'] == 403) {
      int? refreshCode = await ApiPost().accessTokenRefresh();
      if (refreshCode == 200) {
        return me();
      }
    }
    return data;
    // } catch (e) {
    //   print("Errors found in me(GET): $e");
    //   return null;
    // }
  }

  Future<Map?> faq() async {
    try {
      Uri uri = Uri.https(api.domain, api.projectName + api.faq);

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in faq(GET): $e");
      return null;
    }
  }

  Future<Map?> faqSearch(String? query) async {
    try {
      final queryParameters = query == null ? null : {'keyword': query};
      Uri uri = Uri.https(
          api.domain, api.projectName + api.faqSearch, queryParameters);

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in faqSearch(GET): $e");
      return null;
    }
  }

  Future<Map?> feedbackCriteria() async {
    try {
      Uri uri = Uri.https(api.domain, api.projectName + api.feedbackCriteria);

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in feedbackCriteria(GET): $e");
      return null;
    }
  }

  Future<Map?> creditHistory(int nextPage) async {
    try {
      final queryParameters =
          nextPage == 0 ? null : {'page': nextPage.toString()};

      Uri uri = Uri.https(
          api.domain, api.projectName + api.creditHistory, queryParameters);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token");

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return creditHistory(nextPage);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in creditHistory(GET): $e");
      return null;
    }
  }

  Future<Map?> creditPackage() async {
    try {
      Uri uri = Uri.https(api.domain, api.projectName + api.creditPackage);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token");

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return creditPackage();
        }
      }
      return data;
    } catch (e) {
      print("Errors found in creditPackage(GET): $e");
      return null;
    }
  }

  Future<Map?> pointHistory(String filter, int nextPage) async {
    try {
      final queryParameters = nextPage == 0
          ? {'filter': filter}
          : {'filter': filter, 'page': nextPage.toString()};

      Uri uri = Uri.https(
          api.domain, api.projectName + api.pointHistory, queryParameters);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token");

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return pointHistory(filter, nextPage);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in pointHistory(GET): $e");
      return null;
    }
  }

  Future<Map?> qrObtain() async {
    try {
      Uri uri = Uri.https(
          api.domain, api.projectName + api.pointObtain);

      Box box = Hive.box('box');
     final String accessToken = box.get("access_token") ?? '';

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return qrObtain();
        }
      }
      return data;
    } catch (e) {
      print("Errors found in qrObtain(GET): $e");
      return null;
    }
  }

  Future<Map?> notificationList(int nextPage) async {
    try {
      final queryParameters =
          nextPage == 0 ? null : {'page': nextPage.toString()};

      Uri uri = Uri.https(
          api.domain, api.projectName + api.notificationList, queryParameters);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token");

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return notificationList(nextPage);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in notificationList(GET): $e");
      return null;
    }
  }

  Future<Map?> notificationDetail(int id) async {
    try {
      Uri uri = Uri.https(
          api.domain, api.projectName + api.notificationDetail + id.toString());

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token");

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return notificationDetail(id);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in notificationDetail(GET): $e");
      return null;
    }
  }

  Future<Map?> referral() async {
    try {
      Uri uri = Uri.https(api.domain, api.projectName + api.referral);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token");

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return referral();
        }
      }
      return data;
    } catch (e) {
      print("Errors found in referral(GET): $e");
      return null;
    }
  }

  Future<Map?> memberships() async {
    try {
      Uri uri = Uri.https(api.domain, api.projectName + api.memberships);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return memberships();
        }
      }
      return data;
    } catch (e) {
      print("Errors found in memberships(GET): $e");
      return null;
    }
  }

  Future<Map?> customerSupport() async {
    try {
      Uri uri = Uri.https(api.domain, api.projectName + api.customerSupport);

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in customerSupport(GET): $e");
      return null;
    }
  }

  Future<Map?> settings() async {
    try {
      Uri uri = Uri.https(api.domain, api.projectName + api.settings);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token");

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return settings();
        }
      }
      return data;
    } catch (e) {
      print("Errors found in settings(GET): $e");
      return null;
    }
  }

  Future<Map?> addressList() async {
    try {
      Uri uri = Uri.https(api.domain, api.projectName + api.addressList);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return addressList();
        }
      }
      return data;
    } catch (e) {
      print("Errors found in addressList(GET): $e");
      return null;
    }
  }

  Future<Map?> addressDetail(int id) async {
    try {
      Uri uri = Uri.https(
          api.domain, api.projectName + api.addressDetail + id.toString());

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token");

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return addressDetail(id);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in addressDetail(GET): $e");
      return null;
    }
  }

  Future<Map?> countryList() async {
    try {
      Uri uri = Uri.https(api.domain, api.projectName + api.countryList);

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in countryList(GET): $e");
      return null;
    }
  }

  Future<Map?> giftCardList(String? filter) async {
    try {
      final queryParameters = {'filter': filter};

      Uri uri = Uri.https(
          api.domain, api.projectName + api.giftCardList, queryParameters);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token");

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return giftCardList(filter);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in giftCardList(GET): $e");
      return null;
    }
  }

  Future<Map?> giftCardDesignList() async {
    try {
      Uri uri = Uri.https(api.domain, api.projectName + api.giftCardDesignList);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return giftCardDesignList();
        }
      }
      return data;
    } catch (e) {
      print("Errors found in giftCardDesignList(GET): $e");
      return null;
    }
  }

  Future<Map?> contactList() async {
    try {
      Uri uri = Uri.https(api.domain, api.projectName + api.contactList);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in contactList(GET): $e");
      return null;
    }
  }

  Future<Map?> contactListSearch(String? query) async {
    try {
      final queryParameters = query == null ? null : {'keyword': query};

      Uri uri = Uri.https(
          api.domain, api.projectName + api.contactListSearch, queryParameters);

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

      Map data = jsonDecode(response.body);
      return data;
    } catch (e) {
      print("Errors found in contactListSearch(GET): $e");
      return null;
    }
  }
}
