import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:amverton/Model/cart/cart.dart';
import 'package:amverton/Model/cart/cart_item.dart';
import 'package:amverton/Model/scanqr.dart';
import 'package:amverton/Model/cart/cart_page_variable.dart';
import 'package:amverton/Model/user.dart';
import 'package:amverton/Model/home.dart';
import 'package:amverton/Model/voucher.dart';

class HomeNotifier extends StateNotifier<Home> {
  HomeNotifier() : super(Home());

  void updateHome(Home newHome) {
    state = newHome;
  }
}

class ScanQRNotifier extends StateNotifier<Scanqr> {
  ScanQRNotifier() : super(Scanqr());

  void updateHome(Scanqr newScanqr) {
    state = newScanqr;
  }
}


class ScanqrNotifier extends StateNotifier<Home> {
  ScanqrNotifier() : super(Home());

  void updateScanqr(Home newScanqr) {
    state = newScanqr;
  }
}

class UserNotifier extends StateNotifier<User> {
  UserNotifier() : super(User());

  void update(User newUser) {
    state = newUser;
  }
}

class CartNotifier extends StateNotifier<Cart> {
  CartNotifier() : super(Cart());

  void update(Cart newData) {
    state = newData;
  }

  void updateItem(List<CartItem> items) {
    state.items = items;
  }
}

class CartPageVariableNotifier extends StateNotifier<CartPageVariable> {
  CartPageVariableNotifier() : super(CartPageVariable());

  void update(CartPageVariable newData) {
    state = newData;
  }
}

class VoucherRedeemNotifier extends StateNotifier<Voucher> {
  VoucherRedeemNotifier() : super(Voucher());

  void update(Voucher newData) {
    state = newData;
  }
}
