import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class AboutUsTemplate1 extends StatefulWidget {
  static String routeName = "/about_us/1";
  final dynamic args;

  const AboutUsTemplate1({Key? key, this.args}) : super(key: key);

  @override
  _AboutUsTemplate1State createState() => _AboutUsTemplate1State();
}

class _AboutUsTemplate1State extends State<AboutUsTemplate1> {
  late Future future;
  late Box boxGlobal;
  String theme = "L";

  @override
  void initState() {
    super.initState();
    theme = widget.args["theme"];
    future = openBox();
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('ACCOUNT+ABOUT_US+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return Scaffold(
              extendBodyBehindAppBar: true,
              appBar: AppBar(
                title: Text(
                  list.values
                          .firstWhereOrNull(
                              (element) => element.name == "TITLE")
                          ?.data ??
                      "",
                  style: TextStyle(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                ),
                iconTheme: IconThemeData(
                  color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                ),
              ),
              body: Container(
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  height: phoneHeight,
                  child: SingleChildScrollView(
                    padding: EdgeInsets.fromLTRB(
                      defaultPadding,
                      verticalPaddingLarge + defaultPadding,
                      defaultPadding,
                      defaultPadding,
                    ),
                    child: HtmlWidget(
                      '''
                      ${list.values.firstWhereOrNull((element) => element.name == "CONTENT")?.data ?? ""}
                      ''',
                      textStyle: TextStyle(
                        color:
                            theme == "L" ? ThemeColors.dark : ThemeColors.light,
                      ),
                      onErrorBuilder: (context, element, error) =>
                          Text('$element error: $error'),
                    ),
                  )),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }
}
