import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/user.dart';
import 'package:amverton/Notifier/notifiers.dart';
import 'package:amverton/Provider/providers.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Account/temp_delivery_policy_template.dart';
import 'package:amverton/View/Account/temp_privacy_policy_template.dart';
import 'package:amverton/View/Account/temp_refund_policy_template.dart';
import 'package:amverton/View/Custom%20Widgets/default_divider2.dart';
import 'package:amverton/View/Notification/notification_page.dart';
import 'package:amverton/View/Points/points_history_page.dart';
import 'package:amverton/View/Settings/settings_page.dart';
import 'package:amverton/View/Feedback/feedback_outlet.dart';
import 'package:amverton/main.dart';
import 'package:hive/hive.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;
import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

late UserNotifier userNotifier;

class AccountPage extends ConsumerStatefulWidget {
  const AccountPage({Key? key}) : super(key: key);

  @override
  AccountPageState createState() => AccountPageState();
}

class AccountPageState extends ConsumerState<AccountPage> {
  late Future future;
  late Box box;
  late Box boxGlobal;
  bool isLogin = false;

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    isLogin = box.get("is_login") ?? false;
   // if (isLogin) getMe();
  }
@override
Widget build(BuildContext context) {
  userNotifier = ref.read(userProvider.notifier);

  return FutureBuilder(
      future: future,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          return Scaffold(
            // backgroundColor: ThemeColors.light,
            // appBar: AppBar(
            //   title: Text(() {
            //     List bottomBarList = boxGlobal.get("bottomBar") ?? [];
            //     if (bottomBarList.isNotEmpty) {
            //       return bottomBarList
            //               .firstWhereOrNull(
            //                   (element) => element.name == "ACCOUNT")
            //               ?.title ??
            //           "";
            //     }
            //     return "";
            //   }()),
            // ),
            body: RefreshIndicator(
              color: ThemeColors.primaryDark,
              onRefresh: () {
                return Future.delayed(const Duration(seconds: 1), () {
                  setState(() {
                    future = openBox();
                    // if (isLogin) getMe();
                  });
                });
              },
              child: Stack(
                children: [
                  // Background image at the top portion
                  Container(
                    height: 240, // Adjust height as needed
                    width: double.infinity,
                    decoration: const BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage("assets/images/home_background_2.png"),
                        fit: BoxFit.fill,
                      ),
                    ),
                  ),

                  // Scrollable content overlaid on top
                  CustomScrollView(
                    slivers: [
                      SliverFillRemaining(
                        hasScrollBody: false,
                        child: Column(
                          children: [
                            SizedBox(
                              height: 50,
                            ),
                            Column(children: accountWidget),
                            voker(),
                            const Spacer(),
                            SizedBox(
                              height: spacingHeightMedium,
                            ),

                          ],
                        ),
                      )
                    ],
                  ),
                ],
              ),
            ),
          );
        }
        return Container();
      });
}

  // Widget voker() {
  //   //todo get from asset
  //   int style = 2;

  //   if (style == 1) {
  //     return Column(
  //       children: List.generate(accountAsset.length, (index) {
  //         return Container(
  //           padding: EdgeInsets.symmetric(horizontal: defaultPadding),
  //           child: ListTile(
  //             leading: ImageIcon(
  //               AssetImage(getIcon(accountAsset[index])),
  //               color: ThemeColors.primaryDark,
  //             ),
  //             title: Text(
  //               accountAsset[index].title ?? "",
  //               style: TextStyle(
  //                 fontSize: h3,
  //                 fontWeight: FontWeight.w600,
  //               ),
  //             ),
  //             trailing: ImageIcon(
  //               AssetImage(
  //                   "assets/icons/${boxGlobal.get("iconSet")}/Standard_Moreleft.png"),
  //               color: ThemeColors.primaryDark,
  //             ),
  //             onTap: getOnTap(accountAsset[index]),
  //           ),
  //         );
  //       }),
  //     );
  //   } else {
  //     return Container(
  //       padding: EdgeInsets.symmetric(horizontal: defaultPadding),
  //       child: Column(children: [
  //         ...List.generate(
  //           accountAsset.length,
  //           (index) {
  //             return Column(
  //               children: [
  //                 ListTile(
  //                   leading: ImageIcon(
  //                     AssetImage(getIcon(accountAsset[index])),
  //                     color: ThemeColors.primaryDark,
  //                   ),
  //                   title: Text(
  //                     accountAsset[index].title ?? "",
  //                     style: TextStyle(
  //                       fontSize: h3,
  //                       fontWeight: FontWeight.w600,
  //                     ),
  //                   ),
  //                   trailing: ImageIcon(
  //                     AssetImage(
  //                         "assets/icons/${boxGlobal.get("iconSet")}/Standard_Moreleft.png"),
  //                     color: ThemeColors.primaryDark,
  //                   ),
  //                   onTap: getOnTap(accountAsset[index]),
  //                 ),
  //                 Container(
  //                   height: 1,
  //                   color: ThemeColors.disabled,
  //                 ),
  //               ],
  //             );
  //           },
  //         ),
  //       ]),
  //     );
  //   }
  // }
 Widget voker() {
  return Container(
     decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Title
        Text(
          "Other Features",
          style: TextStyle(
            fontSize: h2,
            fontWeight: FontWeight.bold,
            color: ThemeColors.primaryDark,
          ),
        ),
        SizedBox(height: spacingHeightMedium),
        
        // Use Wrap instead of GridView for better compatibility with SliverFillRemaining
        Wrap(
          spacing: spacingHeightSmall,
          runSpacing: spacingHeightSmall,
          children: List.generate(accountAsset.length, (index) {
            return SizedBox(
              width: (MediaQuery.of(context).size.width - (defaultPadding * 2) - (spacingHeightSmall * 3)) / 4,
              child: GestureDetector(
                onTap: getOnTap(accountAsset[index]),
                child: Container(
                 
                  padding: EdgeInsets.symmetric(vertical: 4),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Icon Container
                      Container(
                        width: 40,
                        height: 40,
           
                        child: Center(
                          child: ImageIcon(
                            AssetImage(getIcon(accountAsset[index])),
                            color: getIconColor(accountAsset[index]),
                            size: h1,
                          ),
                        ),
                      ),
                      SizedBox(height: 4),
                      
                      // Label
                      Text(
                          getDisplayTitle(accountAsset[index]),
                          style: TextStyle(
                            fontSize: h3,
                            fontWeight: FontWeight.w600,
                            color: ThemeColors.primaryDark,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ),
            );
          }),
        ),
      ],
    ),
  );
}

// Helper method to get background color for each feature
Color getIconBackgroundColor(SubAsset subAsset) {
  switch (subAsset.sectionName) {
    case "POINT_HISTORY":
      return Color(0xFFFFE4B5); // Light orange/gold
    case "SETTINGS":
      return Color(0xFFE6E6FA); // Light purple/gray
    case "QUESTIONS":
      return Color(0xFFE0E0E0); // Light gray
    case "CUSTOMER_SUPPORT":
      return Color(0xFFE0E0E0); // Light gray
    default:
      return Color(0xFFF0F0F0); // Default light gray
  }
}

// Helper method to get icon color
Color getIconColor(SubAsset subAsset) {
  switch (subAsset.sectionName) {
    case "POINT_HISTORY":
      return Color(0xFFFF8C00); // Orange/gold
    case "MEMBERSHIP":
      return Color(0xFFFF8C00); // Orange/gold
    case "SETTINGS":
      return Color(0xFF6B6B6B); // Dark gray
    case "QUESTIONS":
      return Color(0xFF6B6B6B); // Dark gray
    case "CUSTOMER_SUPPORT":
      return Color(0xFF6B6B6B); // Dark gray
    default:
      return ThemeColors.primaryDark;
  }
}

// Helper method to get display-friendly titles
String getDisplayTitle(SubAsset subAsset) {
  switch (subAsset.sectionName) {
    case "POINT_HISTORY":
      return "Point History";
    case "SETTINGS":
      return "Settings";
    case "QUESTIONS":
      return "FAQ";
    case "CUSTOMER_SUPPORT":
      return "Live Chat";
    case "ORDER_HISTORY":
      return "Order History";
    case "VOUCHERS":
      return "Vouchers";
    case "FEEDBACK":
      return "Feedback";
    case "MEMBERSHIP":
      return "Membership";
    case "PRIVACY_POLICY":
      return "Privacy Policy";
    case "ABOUT_US":
      return "About Us";
    case "TERMS_AND_CONDITIONS":
      return "Terms & Conditions";
    case "REFERRAL":
      return "Referral";
    case "NOTIFICATION":
      return "Notifications";
    case "DELIVERY_REFUND_POLICY":
      return "Delivery Policy";
    default:
      return subAsset.title ?? subAsset.sectionName;
  }
}

  String getIcon(SubAsset subAsset) {
    String icon = "";
    if (subAsset.sectionName == "REFERRAL") {
      icon = "friends_invitations";
    } else if (subAsset.sectionName == "QUESTIONS") {
      icon = "help_center";
    } else if (subAsset.sectionName == "SETTINGS") {
      icon = "settings";
    } else if (subAsset.sectionName == "ORDER_HISTORY") {
      icon = "my_orders";
    } else if (subAsset.sectionName == "FEEDBACK") {
      icon = "feedback";
    } else if (subAsset.sectionName == "MEMBERSHIP") {
      icon = "membership";
    } else if (subAsset.sectionName == "POINT_HISTORY") {
      icon = "Game 03";
    } else if (subAsset.sectionName == "VOUCHERS") {
      icon = "rewards";
    } else if (subAsset.sectionName == "PRIVACY_POLICY") {
      icon = "shield-check";
    } else if (subAsset.sectionName == "CUSTOMER_SUPPORT") {
      icon = "Chat_callservice";
    } else if (subAsset.sectionName == "ABOUT_US") {
      icon = "About Us";
    } else if (subAsset.sectionName == "TERMS_AND_CONDITIONS") {
      icon = "terms-condition";
    } else if (subAsset.sectionName == "NOTIFICATION") {
      icon = "notification_Bell";
    } else if (subAsset.sectionName == "DELIVERY_REFUND_POLICY") {
      icon = "Promotion 02";
    }
    return "assets/icons/${boxGlobal.get("iconSet")}/$icon.png";
  }

  Function() getOnTap(SubAsset subAsset) {
    if (subAsset.sectionName == "REFERRAL") {
      return () {
        if (isLogin) {
          Navigator.pushNamed(context, "/referral/${subAsset.template}",
              arguments: {"theme": subAsset.theme});
        } else {
          Navigator.of(context, rootNavigator: true)
              .pushNamed("/login/${loginAsset.template}");
        }
      };
    } else if (subAsset.sectionName == "QUESTIONS") {
      return () {
        Navigator.pushNamed(context, "/faq/${subAsset.template}",
            arguments: {"theme": subAsset.theme});
      };
    } else if (subAsset.sectionName == "SETTINGS") {
      return () {
        if (isLogin) {
          Route route = MaterialPageRoute(
              builder: (BuildContext context) =>
                  SettingsPage(theme: subAsset.theme));
          Navigator.push(context, route).then(onRefresh);
        } else {
          Navigator.of(context, rootNavigator: true)
              .pushNamed("/login/${loginAsset.template}");
        }
      };
    } else if (subAsset.sectionName == "ORDER_HISTORY") {
      return () {
        if (isLogin) {
         // todo get from asset
         // Navigator.pushNamed(context, "/order_history/${subAsset.template}",
          Navigator.pushNamed(context, "/order_history/2",
              arguments: {"theme": subAsset.theme});
        } else {
         Navigator.of(context, rootNavigator: true)
             .pushNamed("/login/${loginAsset.template}");
        }
      };
    } else if (subAsset.sectionName == "VOUCHERS") {
      return () {
        if (isLogin) {
          //todo need to get from assets
          // Navigator.pushNamed(context, "/rewards_list/${subAsset.template}",
          //     arguments: {"theme": subAsset.theme});
          Navigator.pushNamed(context, "/rewards_list/3",
              arguments: {"theme": subAsset.theme});
        } else {
          Navigator.of(context, rootNavigator: true)
              .pushNamed("/login/${loginAsset.template}");
        }
      };
    } else if (subAsset.sectionName == "FEEDBACK") {
      return () {
        if (isLogin) {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (BuildContext context) => const FeedbackOutlet()));
        } else {
          Navigator.of(context, rootNavigator: true)
              .pushNamed("/login/${loginAsset.template}");
        }
      };
    } else if (subAsset.sectionName == "MEMBERSHIP") {
      return () {
        Navigator.pushNamed(context, "/membership/${subAsset.template}",
            arguments: {"theme": subAsset.theme});
      };
    } else if (subAsset.sectionName == "POINT_HISTORY") {
      return () {
        if (isLogin) {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (BuildContext context) => const PointHistoryPage()));
        } else {
          Navigator.of(context, rootNavigator: true)
              .pushNamed("/login/${loginAsset.template}");
        }
      };
    } else if (subAsset.sectionName == "PRIVACY_POLICY") {
      return () {
        Navigator.pushNamed(context, "/privacy_policy/${subAsset.template}",
            arguments: {"theme": subAsset.theme});
      };
    } else if (subAsset.sectionName == "CUSTOMER_SUPPORT") {
      return () {
        Navigator.pushNamed(context, "/support/${subAsset.template}",
            arguments: {"theme": subAsset.theme});
      };
    } else if (subAsset.sectionName == "ABOUT_US") {
      return () {
        Navigator.pushNamed(context, "/about_us/${subAsset.template}",
            arguments: {"theme": subAsset.theme});
      };
    } else if (subAsset.sectionName == "TERMS_AND_CONDITIONS") {
      return () {
        Navigator.pushNamed(context, "/tnc/${subAsset.template}",
            arguments: {"theme": subAsset.theme});
      };
    } else if (subAsset.sectionName == "NOTIFICATION") {
      return () {
        if (isLogin) {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (BuildContext context) => const NotificationPage()));
        } else {
          Navigator.of(context, rootNavigator: true)
              .pushNamed("/login/${loginAsset.template}");
        }
      };
    } else if (subAsset.sectionName == "DELIVERY_REFUND_POLICY") {
      return () {
        Navigator.pushNamed(
            context, "/delivery_refund_policy/${subAsset.template}",
            arguments: {"theme": subAsset.theme});
      };
    } else {
      return () {};
    }
  }

  FutureOr onRefresh(dynamic value) {
    //if (isLogin) getMe();
  }

  Future<Map?> getMe() async {
    Box box = await Hive.openBox("box");
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.me();

    if (data != null && data['data'] != null) {
      User user = User.fromJson(data['data']['me']);
      userNotifier.update(user);
      await box.put("user", user);

      String barcode = data['data']['barcode'];
      await box.put("barcode", barcode);

      int totalDaysCheckIn =
          data['data']['check_in']?['total_days_checked_in'] ?? 0;
      box.put("total_days_checked_in", totalDaysCheckIn);
    }
    return data;
  }
}
