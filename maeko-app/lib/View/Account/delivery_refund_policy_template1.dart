import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class DeliveryRefundPolicyTemplate1 extends StatefulWidget {
  static String routeName = "/delivery_refund_policy/1";
  final dynamic args;

  const DeliveryRefundPolicyTemplate1({Key? key, this.args}) : super(key: key);

  @override
  _DeliveryRefundPolicyTemplate1State createState() =>
      _DeliveryRefundPolicyTemplate1State();
}

class _DeliveryRefundPolicyTemplate1State
    extends State<DeliveryRefundPolicyTemplate1> {
  // late Future future;
  // late Box boxGlobal;
  Box boxGlobal = Hive.box("boxGlobal");
  String theme = "L";

  @override
  void initState() {
    super.initState();
    theme = widget.args["theme"];
    // future = openBox();
  }

  // openBox() async {
  //   boxGlobal = await Hive.openBox('boxGlobal');
  //   return await Hive.openBox<Asset>('ACCOUNT+PRIVACY_POLICY+1');
  // }

  @override
  Widget build(BuildContext context) {
    // return FutureBuilder(
    //     future: future,
    //     builder: (BuildContext context, AsyncSnapshot snapshot) {
    //       if (snapshot.connectionState == ConnectionState.done) {
    //         final Box list = snapshot.data;
    //         list.watch();
    //
    //         String? backgroundType = list.values
    //             .firstWhereOrNull(
    //                 (element) => element.name == "BACKGROUND_TYPE")
    //             ?.data ??
    //             "";
    //         Asset? background = list.values.firstWhereOrNull(
    //                 (element) => element.name == "BACKGROUND_$backgroundType");

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: Text(
          "Delivery/Refund Policy",
          style: TextStyle(
            color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
          ),
        ),
        iconTheme: IconThemeData(
          color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
        ),
      ),
      body: Container(
          // decoration: background == null
          //     ? null
          //     : BoxDecoration(
          //         image: backgroundType == "IMAGE"
          //             ? DecorationImage(
          //                 image:
          //                     CachedNetworkImageProvider(background.data ?? ""),
          //                 fit: BoxFit.cover,
          //               )
          //             : null,
          //         color: backgroundType == "COLOR"
          //             ? Color(int.parse("0xff${background.data ?? "FFFFFF"}"))
          //             : null,
          //       ),
          color: ThemeColors.light,
          height: phoneHeight,
          child: SingleChildScrollView(
            padding: EdgeInsets.fromLTRB(
              defaultPadding,
              verticalPaddingLarge + defaultPadding,
              defaultPadding,
              defaultPadding,
            ),
            child: HtmlWidget(
              '''
                      <p dir="ltr"><strong>Return/Refund Policy for SinBoonKee</strong></p> <p dir="ltr"><strong>Service Tax and Fees</strong></p> <ul> <li dir="ltr" aria-level="1"> <p dir="ltr" role="presentation">Prices for all products and delivery services are inclusive of a 6% Service Tax.</p> </li> <li dir="ltr" aria-level="1"> <p dir="ltr" role="presentation">Delivery orders are subject to a minimum order of RM 15.</p> </li> <li dir="ltr" aria-level="1"> <p dir="ltr" role="presentation">Delivery orders are subject to a Delivery and processing fee.</p> </li> </ul> <p dir="ltr"><strong>Delivery Terms</strong></p> <ul> <li dir="ltr" aria-level="1"> <p dir="ltr" role="presentation">We have a limited delivery area only.</p> </li> <li dir="ltr" aria-level="1"> <p dir="ltr" role="presentation">Prices listed on this menu are applicable for SinBoonKee Delivery only and are subject to change without prior notice.</p> </li> <li dir="ltr" aria-level="1"> <p dir="ltr" role="presentation">For large orders exceeding RM200, the estimated delivery time is within 2 hours.</p> </li> </ul> <p dir="ltr"><strong>Product and Bundle Policies</strong></p> <ul> <li dir="ltr" aria-level="1"> <p dir="ltr" role="presentation">All products in the promotional bundles and sets are fixed.</p> </li> <li dir="ltr" aria-level="1"> <p dir="ltr" role="presentation">Please note that deals for SinBoonKee Delivery may differ from Dine-In or Take Away options.</p> </li> </ul> <p dir="ltr"><strong>Delivery Availability</strong></p> <ul> <li dir="ltr" aria-level="1"> <p dir="ltr" role="presentation">To ensure rider safety, SinBoonKee Delivery may be temporarily unavailable at selected areas due to weather conditions or unforeseen circumstances.</p> </li> </ul> <p dir="ltr"><strong>Illustration and Payment</strong></p> <ul> <li dir="ltr" aria-level="1"> <p dir="ltr" role="presentation">Visuals shown are for illustration purposes only.</p> </li> <li dir="ltr" aria-level="1"> <p dir="ltr" role="presentation">Payment can be made by cash. Alternatively, we accept payments made with credit cards or debit cards (Visa, MasterCard) issued by banks in Malaysia.</p> </li> <li dir="ltr" aria-level="1"> <p dir="ltr" role="presentation">Payment by credit card or debit card shall be made at the point of order. Cash payment shall be made at the point of arrival of food.</p> </li> </ul> <p dir="ltr"><strong>Refund and Cancellation Policy</strong></p> <ul> <li dir="ltr" aria-level="1"> <p dir="ltr" role="presentation">There is strictly no refund, change, or cancellation once the order has been confirmed or payment has been made.</p> </li> </ul> <p dir="ltr"><strong>Language Version</strong></p> <ul> <li dir="ltr" aria-level="1"> <p dir="ltr" role="presentation">In the event of any inconsistency between the English and Malay language version of the terms and conditions, the English language version shall prevail.</p> </li> </ul> <p>&nbsp;</p>
                      ''',
              textStyle: TextStyle(
                color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
              ),
            ),
          )),
    );
    //   }
    //   return Scaffold(appBar: AppBar());
    // });
  }
}
