import 'package:flutter/material.dart';

class DeliveryPolicyPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Delivery Policy"),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "<PERSON><PERSON><PERSON>'s Delivery Policy",
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            Text(
              "1. Introduction",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "O'<PERSON>'s is committed to providing a reliable and efficient delivery service to our customers. This delivery policy outlines the terms and conditions for the delivery of food and beverages ordered from <PERSON><PERSON><PERSON>'s, ensuring a smooth and satisfactory experience.",
            ),
            SizedBox(height: 16),
            Text(
              "2. Delivery Areas",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "We deliver to the following areas:",
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                      "• Local Area: Within a 10 km radius of our restaurant."),
                  Text(
                      "• Extended Area: Within a 20 km radius for an additional fee."),
                ],
              ),
            ),
            SizedBox(height: 16),
            Text(
              "3. Delivery Options",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "We offer several delivery options to cater to the needs of our customers:",
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("• Standard Delivery: 30-45 minutes."),
                  Text("• Express Delivery: 20-30 minutes."),
                  Text(
                      "• Scheduled Delivery: Select a preferred delivery time within our operating hours."),
                ],
              ),
            ),
            SizedBox(height: 16),
            Text(
              "4. Delivery Charges",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "Delivery charges vary based on the delivery option selected:",
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("• Standard Delivery: RM 5.00"),
                  Text("• Express Delivery: RM 10.00"),
                  Text("• Scheduled Delivery: RM 7.00"),
                ],
              ),
            ),
            SizedBox(height: 16),
            Text(
              "Orders over RM 100 qualify for free standard delivery.",
            ),
            SizedBox(height: 16),
            Text(
              "5. Delivery Times",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text("• Standard Delivery: Monday to Sunday, 11 AM to 10 PM."),
            Text("• Express Delivery: Monday to Sunday, 11 AM to 10 PM."),
            Text("• Scheduled Delivery: Monday to Sunday, 11 AM to 10 PM."),
            SizedBox(height: 16),
            Text(
              "6. Order Processing Time",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "Orders are prepared and dispatched within the estimated delivery time from the moment the order is received. During peak hours, processing time may extend slightly.",
            ),
            SizedBox(height: 16),
            Text(
              "7. Delivery Confirmation",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "Once your order is dispatched, you will receive a confirmation message with tracking information. You can track your order using the provided link.",
            ),
            SizedBox(height: 16),
            Text(
              "8. Delivery Issues",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "If there are any issues with your delivery, such as delays or damages, please contact our customer service team immediately at [<EMAIL>](mailto:<EMAIL>) or call +6(03) 9222 3646.",
            ),
            SizedBox(height: 16),
            Text(
              "9. Missed Deliveries",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "If you are not available to receive your delivery, our delivery personnel will contact you to arrange a re-delivery. Additional charges may apply for re-delivery.",
            ),
            SizedBox(height: 16),
            Text(
              "10. Delivery Conditions",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "To ensure the quality and safety of our food, deliveries are made using insulated bags. We recommend consuming the delivered food within 2 hours of receipt.",
            ),
            SizedBox(height: 16),
            Text(
              "11. Changes to Delivery Policy",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "O'Brien's reserves the right to modify this delivery policy at any time. Any changes will be communicated via our website and will be effective immediately upon posting.",
            ),
            SizedBox(height: 16),
            Text(
              "12. Contact Information",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "For any questions or concerns regarding our delivery policy, please contact us at:",
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("O'Brien's Customer Service"),
                  Text("Email: <EMAIL>"),
                  Text("Phone: +6(03) 9222 3646"),
                  Text(
                      "Address: No.70-3, Jalan Metro Pudu, Fraser Business Park, Off Jalan Loke Yew, 55100 Kuala Lumpur"),
                ],
              ),
            ),
            SizedBox(height: 16),
            Text(
              "By placing an order with O'Brien's, you agree to the terms outlined in this delivery policy. Thank you for choosing O'Brien's for your dining needs.",
            ),
          ],
        ),
      ),
    );
  }
}
