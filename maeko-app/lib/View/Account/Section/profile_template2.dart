import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/user.dart';
import 'package:amverton/Provider/providers.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Account/account_page.dart';
import 'package:amverton/View/Notification/notification_page.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ProfileTemplate2 extends ConsumerStatefulWidget {
  final String theme;

  const ProfileTemplate2({Key? key, required this.theme}) : super(key: key);

  @override
  _ProfileTemplate2State createState() => _ProfileTemplate2State();
}

class _ProfileTemplate2State extends ConsumerState<ProfileTemplate2> {
  late Future future;
  late Box box;
  late Box boxGlobal;

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    // todo temp for obriens
    return await Hive.openBox<Asset>('ACCOUNT+PROFILE+1');
  }

  @override
  Widget build(BuildContext context) {
    User user = ref.watch(userProvider);

    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            bool isLogin = box.get("is_login") ?? false;

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            //profile image
            String profileImage = "assets/images/no_gender_1.png";
            if (isLogin) {
              switch (user.gender) {
                case "M":
                  profileImage = "assets/images/male.png";
                  break;
                case "F":
                  profileImage = "assets/images/female.png";
                  break;
                default:
                  profileImage = "assets/images/no_gender_2.png";
              }
            }

            return Container(
              width: phoneWidth,
              height: phoneHeight / 11,
              decoration: background == null
                  ? null
                  : BoxDecoration(
                      image: backgroundType == "IMAGE"
                          ? DecorationImage(
                              image: CachedNetworkImageProvider(
                                  background.data ?? ""),
                              fit: BoxFit.cover,
                            )
                          : null,
                      color: backgroundType == "COLOR"
                          ? Color(
                              int.parse("0xff${background.data ?? "FFFFFF"}"))
                          : null,
                    ),
              padding: EdgeInsets.symmetric(horizontal: defaultPadding),
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  if (!isLogin) {
                    Navigator.of(context, rootNavigator: true)
                        .pushNamed("/login/${loginAsset.template}");
                  } else {
                    Navigator.pushNamed(
                        context, "/edit_profile/${editProfileAsset.template}",
                        arguments: {
                          "theme": accountAsset
                                  .firstWhereOrNull((element) =>
                                      element.sectionName == "EDIT_PROFILE")
                                  ?.theme ??
                              "L",
                        }).then((_) async {
                      //todo temporary refresh home also, wait account template get from api then remove it
                      homePageKey.currentState?.onRefresh();
                      accountPageKey.currentState?.onRefresh(null);
                    });
                  }
                },
                child: Row(
                  children: [
                    // todo temp for obriens (ori: CachedNetworkImage)
                    CircleAvatar(
                      backgroundColor: ThemeColors.disabled,
                      backgroundImage: AssetImage(
                        profileImage,
                      ),
                      radius: phoneWidth / 16,
                    ),
                    SizedBox(width: spacingHeightMedium),
                    Expanded(
                      child: Container(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              isLogin ? user!.fullName : "Log In or Register Now",
                              style: TextStyle(
                                fontSize: h3,
                                color: widget.theme == "L"
                                    ? ThemeColors.dark
                                    : ThemeColors.light,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (isLogin)
                              Row(
                                children: [
                                  Text(
                                    isLogin ? user!.phone : "",
                                    style: TextStyle(
                                      fontSize: h3,
                                      color: ThemeColors.gray,
                                    ),
                                  ),
                                  SizedBox(
                                    width: 2,
                                  ),
                                  Icon(
                                    Icons.check_circle_rounded,
                                    color: Colors.green,
                                    size: 20,
                                  ),
                                ],
                              ),
                          ],
                        ),
                      ),
                    ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: IconButton(
                        onPressed: () {
                          if (isLogin) {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (BuildContext context) =>
                                        const NotificationPage()));
                          } else {
                            Navigator.of(context, rootNavigator: true)
                                .pushNamed("/login/${loginAsset.template}");
                          }
                        },
                        icon: ImageIcon(
                          AssetImage(
                              "assets/icons/${boxGlobal.get("iconSet")}/notification_bell ring.png"),
                          color: widget.theme == "L"
                              ? ThemeColors.dark
                              : ThemeColors.light,
                        ),
                      ),
                    )
                  ],
                ),
              ),
            );
          }
          return Container();
        });
  }
}
