import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/user.dart';
import 'package:amverton/Provider/providers.dart';
import 'package:amverton/View/Account/account_page.dart';
import 'package:amverton/View/Credit/credit_history_page.dart';
import 'package:amverton/View/Credit/credit_topup.dart';
import 'package:amverton/View/Points/points_history_page.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class BalanceTemplate2 extends ConsumerStatefulWidget {
  final String theme;

  const BalanceTemplate2({Key? key, required this.theme}) : super(key: key);

  @override
  _BalanceTemplate2State createState() => _BalanceTemplate2State();
}

class _BalanceTemplate2State extends ConsumerState<BalanceTemplate2> {
  late Future future;
  late Box box;
  late Box boxGlobal;

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('ACCOUNT+BALANCE+2');
  }

  @override
  Widget build(BuildContext context) {
    User user = ref.watch(userProvider);

    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            bool isLogin = box.get("is_login") ?? false;

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            String? innerBackgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "INNER_BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? innerBackground = list.values.firstWhereOrNull((element) =>
                element.name == "INNER_BACKGROUND_$innerBackgroundType");

            String? balanceLabel = list.values
                .firstWhereOrNull((element) => element.name == "CREDIT_LABEL")
                ?.data;
            String? pointLabel = list.values
                .firstWhereOrNull((element) => element.name == "POINT_LABEL")
                ?.data;

            if (list.isNotEmpty) {
              return Container(
                decoration: background == null
                    ? null
                    : BoxDecoration(
                        image: backgroundType == "IMAGE"
                            ? DecorationImage(
                                image: CachedNetworkImageProvider(
                                    background.data ?? ""),
                                fit: BoxFit.cover,
                              )
                            : null,
                        color: backgroundType == "COLOR"
                            ? Color(
                                int.parse("0xff${background.data ?? "FFFFFF"}"))
                            : null,
                      ),
                child: Container(
                  width: phoneWidth,
                  margin: EdgeInsets.all(defaultPadding),
                  padding: EdgeInsets.symmetric(
                      horizontal: defaultInnerPadding,
                      vertical: defaultPadding),
                  decoration: innerBackground == null
                      ? BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: ThemeColors.disabled,
                              offset: const Offset(0.0, 5.0),
                              blurRadius: 5.0,
                              spreadRadius: 0.5,
                            )
                          ],
                        )
                      : BoxDecoration(
                          image: innerBackgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      innerBackground.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: innerBackgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${innerBackground.data ?? "FFFFFF"}"))
                              : null,
                          borderRadius: BorderRadius.circular(20),
                        ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isLogin ? user!.fullName : "Guest",
                        style: TextStyle(
                          color: widget.theme == "L"
                              ? ThemeColors.dark
                              : ThemeColors.light,
                          fontSize: h2,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: spacingHeightMedium),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (balanceLabel != null)
                            Expanded(
                                child: Container(
                                    padding: EdgeInsets.only(
                                        right: defaultInnerPadding),
                                    child: Stack(
                                      children: [
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              balanceLabel,
                                              style: TextStyle(
                                                  color: widget.theme == "L"
                                                      ? ThemeColors.gray
                                                      : ThemeColors.disabled,
                                                  fontSize: h3),
                                            ),
                                            SizedBox(
                                                height: defaultInnerPadding),
                                            Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                if (list.values
                                                        .firstWhereOrNull(
                                                            (element) =>
                                                                element.name ==
                                                                "CREDIT_ICON")
                                                        ?.data !=
                                                    null)
                                                  CachedNetworkImage(
                                                    imageUrl: list.values
                                                            .firstWhereOrNull(
                                                                (element) =>
                                                                    element
                                                                        .name ==
                                                                    "CREDIT_ICON")
                                                            ?.data ??
                                                        "",
                                                    width: 28,
                                                    height: 28,
                                                  ),
                                                SizedBox(
                                                    width: spacingHeightSmall),
                                                GestureDetector(
                                                  onTap: () {
                                                    if (isLogin)
                                                      Navigator.push(
                                                          context,
                                                          MaterialPageRoute(
                                                              builder: (BuildContext
                                                                      context) =>
                                                                  CreditTopUp())).then(
                                                          (value) {
                                                        setState(() {
                                                          future = openBox();
                                                        });
                                                      });
                                                  },
                                                  child: Text(
                                                    () {
                                                      String credit = "0.00";
                                                      if (isLogin)
                                                        credit = user!.point;
                                                      return "${list.values.firstWhereOrNull((element) => element.name == "CREDIT_UNIT")?.data ?? ""} $credit";
                                                    }(),
                                                    style: TextStyle(
                                                      color: widget.theme == "L"
                                                          ? ThemeColors.dark
                                                          : ThemeColors.light,
                                                      fontSize: h2,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                        if (isLogin)
                                          Positioned(
                                              right: 0,
                                              child: GestureDetector(
                                                onTap: () {
                                                  Navigator.push(
                                                      context,
                                                      MaterialPageRoute(
                                                        builder: (BuildContext
                                                                context) =>
                                                            CreditHistoryPage(),
                                                      ));
                                                },
                                                child: ImageIcon(
                                                    AssetImage(
                                                        "assets/icons/${boxGlobal.get("iconSet")}/History Icon.png"),
                                                    color: ThemeColors
                                                        .primaryDark),
                                              ))
                                      ],
                                    ))),
                          if (pointLabel != null)
                            Expanded(
                                child: Container(
                                    padding: EdgeInsets.only(
                                        left: spacingHeightMedium),
                                    decoration: BoxDecoration(
                                        border: balanceLabel == null
                                            ? null
                                            : Border(
                                                left: BorderSide(
                                                  color: widget.theme == "L"
                                                      ? ThemeColors.dark
                                                      : ThemeColors.light,
                                                ),
                                              )),
                                    child: Stack(
                                      children: [
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              pointLabel,
                                              style: TextStyle(
                                                  color: widget.theme == "L"
                                                      ? ThemeColors.gray
                                                      : ThemeColors.disabled,
                                                  fontSize: h3),
                                            ),
                                            SizedBox(
                                                height: defaultInnerPadding),
                                            Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                if (list.values
                                                        .firstWhereOrNull(
                                                            (element) =>
                                                                element.name ==
                                                                "POINT_ICON")
                                                        ?.data !=
                                                    null)
                                                  CachedNetworkImage(
                                                    imageUrl: list.values
                                                            .firstWhereOrNull(
                                                                (element) =>
                                                                    element
                                                                        .name ==
                                                                    "POINT_ICON")
                                                            ?.data ??
                                                        "",
                                                    width: 28,
                                                    height: 28,
                                                  ),
                                                SizedBox(
                                                    width: spacingHeightSmall),
                                                Expanded(
                                                  child: Text(
                                                    () {
                                                      String points = "0.00";
                                                      if (isLogin)
                                                        points = user!.point;

                                                      return "$points ${list.values.firstWhereOrNull((element) => element.name == "POINT_UNIT")?.data ?? ""}";
                                                    }(),
                                                    style: TextStyle(
                                                      color: widget.theme == "L"
                                                          ? ThemeColors.dark
                                                          : ThemeColors.light,
                                                      fontSize: h2,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                        if (isLogin)
                                          Positioned(
                                              right: 0,
                                              child: GestureDetector(
                                                onTap: () {
                                                  Navigator.push(
                                                      context,
                                                      MaterialPageRoute(
                                                          builder: (BuildContext
                                                                  context) =>
                                                              const PointHistoryPage()));
                                                },
                                                child: ImageIcon(
                                                    AssetImage(
                                                        "assets/icons/${boxGlobal.get("iconSet")}/History Icon.png"),
                                                    color: ThemeColors
                                                        .primaryDark),
                                              ))
                                      ],
                                    ))),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            }
          }
          return Container();
        });
  }
}
