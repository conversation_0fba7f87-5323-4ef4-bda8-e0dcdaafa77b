// import 'dart:convert';

// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:decimal/decimal.dart';
// import 'package:flutter/material.dart';
// import 'package:amverton/Constant/theme_size.dart';
// import 'package:amverton/Model/bottom_bar.dart';
// import 'package:amverton/Model/home.dart';
// import 'package:amverton/Model/pages.dart';
// import 'package:amverton/Model/user.dart';
// import 'package:amverton/Provider/providers.dart';
// import 'package:amverton/Repository/modify_assets.dart';
// import 'package:amverton/View/Credit/credit_history_page.dart';
// import 'package:amverton/View/Credit/credit_topup.dart';
// import 'package:amverton/View/Points/points_history_page.dart';
// import 'package:amverton/main.dart';
// import 'package:hive/hive.dart';
// import 'package:collection/collection.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';

// class BalanceTemplate4 extends ConsumerStatefulWidget {
//   final String theme;

//   const BalanceTemplate4({Key? key, required this.theme}) : super(key: key);

//   @override
//   _BalanceTemplate4State createState() => _BalanceTemplate4State();
// }

// class _BalanceTemplate4State extends ConsumerState<BalanceTemplate4> {
//   late Future future;
//   late Box box;
//   late Box boxGlobal;
//   late bool isLogin;

//   @override
//   void initState() {
//     super.initState();
//     future = openBox();
//   }

//   openBox() async {
//     box = await Hive.openBox('box');
//     boxGlobal = await Hive.openBox('boxGlobal');
//     isLogin = box.get("is_login") ?? false;
//     // todo temp for obriens
//     return await Hive.openBox<Asset>('ACCOUNT+BALANCE+1');
//     // return await Hive.openBox<Asset>('ACCOUNT+BALANCE+3');
//   }

//   @override
//   Widget build(BuildContext context) {
//     Home home = ref.watch(homeProvider);

//     return FutureBuilder(
//         future: future,
//         builder: (BuildContext context, AsyncSnapshot snapshot) {
//           if (snapshot.connectionState == ConnectionState.done) {
//             final Box list = snapshot.data;
//             list.watch();

//             String? backgroundType = list.values
//                     .firstWhereOrNull(
//                         (element) => element.name == "BACKGROUND_TYPE")
//                     ?.data ??
//                 "";
//             Asset? background = list.values.firstWhereOrNull(
//                 (element) => element.name == "BACKGROUND_$backgroundType");

//             String? innerBackgroundType = list.values
//                     .firstWhereOrNull(
//                         (element) => element.name == "INNER_BACKGROUND_TYPE")
//                     ?.data ??
//                 "";
//             Asset? innerBackground = list.values.firstWhereOrNull((element) =>
//                 element.name == "INNER_BACKGROUND_$innerBackgroundType");

//             String? balanceLabel = list.values
//                 .firstWhereOrNull((element) => element.name == "CREDIT_LABEL")
//                 ?.data;
//             String? pointLabel = list.values
//                 .firstWhereOrNull((element) => element.name == "POINT_LABEL")
//                 ?.data;

//             String? pointUnit = list.values
//                 .firstWhereOrNull((element) => element.name == "POINT_UNIT")
//                 ?.data;

//             if (list.isNotEmpty) {
//               if (isLogin) {
//                 return Container(
//                   margin: EdgeInsets.all(defaultPadding),
//                   padding: EdgeInsets.all(defaultPadding),
//                   decoration: BoxDecoration(
//                     color: Colors.white,
//                     borderRadius: BorderRadius.circular(16.0),
//                     boxShadow: [
//                       BoxShadow(
//                         color: Colors.grey.withOpacity(0.5),
//                         spreadRadius: 2,
//                         blurRadius: 5,
//                         offset: Offset(0, 3),
//                       ),
//                     ],
//                   ),
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.start,
//                     children: [
//                       Row(
//                         children: [
//                           Container(
//                             height: 20,
//                             width: 20,
//                             child: Image.asset(
//                                 "assets/icons/home/<USER>"),
//                           ),
//                           SizedBox(width: 8),
//                           Text(
//                             'My ${pointLabel}',
//                             style: TextStyle(
//                               fontSize: h2,
//                             ),
//                           ),
//                           Spacer(),
//                           Container(
//                             padding: EdgeInsets.symmetric(
//                                 horizontal: 12, vertical: 8),
//                             decoration: BoxDecoration(
//                               color: Colors.amber,
//                               borderRadius: BorderRadius.circular(20),
//                             ),
//                             child: Row(
//                               children: [
//                                 Icon(Icons.star_border, color: Colors.black),
//                                 SizedBox(
//                                   width: 8,
//                                 ),
//                                 Text(
//                                   home.me.tier ?? "",
//                                   style: TextStyle(
//                                       fontSize: 16,
//                                       fontWeight: FontWeight.bold),
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ],
//                       ),
//                       Container(
//                         margin: EdgeInsets.only(top: 8, bottom: defaultPadding),
//                         child: Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           crossAxisAlignment: CrossAxisAlignment.center,
//                           children: [
//                             RichText(
//                               text: TextSpan(
//                                 children: [
//                                   TextSpan(
//                                     text: home.me.point == ''
//                                         ? "0"
//                                         : Decimal.parse(home.me.point)
//                                             .toStringAsFixed(0),
//                                     style: TextStyle(
//                                       fontSize: 30,
//                                       color: Colors.black,
//                                     ),
//                                   ),
//                                   TextSpan(
//                                     text: ' ${pointUnit}',
//                                     style: TextStyle(
//                                       fontSize: h3,
//                                       color: Colors.black,
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                             Text(
//                               home.me.pointExpiring != ""
//                                   ? "${home.me.pointExpiring} ${pointUnit} expiring by ${home.me.pointExpiringDate}"
//                                   : "",
//                               style: TextStyle(
//                                 fontSize: h5,
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                       Container(
//                         margin: EdgeInsets.only(bottom: 8, left: 30, right: 30),
//                         width: phoneWidth,
//                         child: home.me.barcode != "" && home.me.barcode != null
//                             ? Image.memory(
//                                 base64Decode(
//                                   home!.me.barcode!,
//                                 ),
//                                 height: phoneHeight * 0.08,
//                                 fit: BoxFit.fill,
//                               )
//                             : Container(),
//                       ),
//                       Container(
//                         margin: EdgeInsets.symmetric(horizontal: 30),
//                         child: Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             Text(
//                               home.me.name ?? "",
//                               style: TextStyle(fontSize: 16),
//                             ),
//                             Text(
//                               home.me.code ?? "",
//                               style:
//                                   TextStyle(fontSize: 16, color: Colors.grey),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ],
//                   ),
//                 );
//               }

//               return GestureDetector(
//                 onTap: () {
//                   Navigator.of(context, rootNavigator: true)
//                       .pushNamed("/login/${loginAsset.template}");
//                 },
//                 child: Container(
//                   margin: EdgeInsets.all(defaultPadding),
//                   padding: EdgeInsets.all(defaultPadding),
//                   decoration: BoxDecoration(
//                     color: Colors.white,
//                     borderRadius: BorderRadius.circular(16.0),
//                     boxShadow: [
//                       BoxShadow(
//                         color: Colors.grey.withOpacity(0.5),
//                         spreadRadius: 2,
//                         blurRadius: 5,
//                         offset: Offset(0, 3),
//                       ),
//                     ],
//                   ),
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.start,
//                     children: [
//                       Row(
//                         children: [
//                           Container(
//                             height: 20,
//                             width: 20,
//                             child: Image.asset(
//                                 "assets/icons/home/<USER>"),
//                           ),
//                           SizedBox(width: 8),
//                           Text(
//                             'My ${pointLabel}',
//                             style: TextStyle(
//                                 fontSize: h1, fontWeight: FontWeight.bold),
//                           ),
//                           Spacer(),
//                           Container(
//                             padding: EdgeInsets.symmetric(
//                                 horizontal: 12, vertical: 8),
//                             decoration: BoxDecoration(
//                               color: Colors.amber,
//                               borderRadius: BorderRadius.circular(20),
//                             ),
//                             child: Row(
//                               children: [
//                                 Icon(Icons.star_border, color: Colors.black),
//                                 SizedBox(
//                                   width: 8,
//                                 ),
//                                 Text(
//                                   'Platinum',
//                                   style: TextStyle(
//                                       fontSize: 16,
//                                       fontWeight: FontWeight.bold),
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ],
//                       ),
//                       Container(
//                         margin: EdgeInsets.only(top: 8, bottom: defaultPadding),
//                         child: Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           crossAxisAlignment: CrossAxisAlignment.center,
//                           children: [
//                             RichText(
//                               text: TextSpan(
//                                 children: [
//                                   TextSpan(
//                                     text: home.me.point == ''
//                                         ? "0"
//                                         : Decimal.parse(home.me.point)
//                                             .toStringAsFixed(0),
//                                     style: TextStyle(
//                                       fontSize: 30,
//                                       color: Colors.black,
//                                     ),
//                                   ),
//                                   TextSpan(
//                                     text: ' ${pointUnit}',
//                                     style: TextStyle(
//                                       fontSize: h3,
//                                       color: Colors.black,
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               );
//             }
//           }
//           return Container();
//         });
//   }
// }
import 'dart:convert';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/user.dart';
import 'package:amverton/Provider/providers.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Account/account_page.dart';
import 'package:amverton/View/Credit/credit_history_page.dart';
import 'package:amverton/View/Credit/credit_topup.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Points/points_history_page.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class BalanceTemplate4 extends ConsumerStatefulWidget {
  final String theme;

  const BalanceTemplate4({Key? key, required this.theme}) : super(key: key);

  @override
  _BalanceTemplate4State createState() => _BalanceTemplate4State();
}

class _BalanceTemplate4State extends ConsumerState<BalanceTemplate4> {
  late Future future;
  late Box box;
  late Box boxGlobal;
  bool enlarge = false;

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('ACCOUNT+BALANCE+1');
  }

  @override
  Widget build(BuildContext context) {
    User user = ref.watch(userProvider);

    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String barcode = "";
            bool isLogin = box.get("is_login") ?? false;
            if (isLogin) {
              barcode = box.get("barcode");
            }

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            String? balanceLabel = list.values
                .firstWhereOrNull((element) => element.name == "CREDIT_LABEL")
                ?.data;
            String? pointLabel = list.values
                .firstWhereOrNull((element) => element.name == "POINT_LABEL")
                ?.data;

            if (list.isNotEmpty) {
              return Container(
                margin: EdgeInsets.all(defaultPadding),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: Container(
                    width: phoneWidth,
                    height: 200, // Fixed height for card-like appearance
                    decoration: background == null
                        ? BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Colors.grey[300]!,
                                Colors.grey[400]!,
                              ],
                            ),
                          )
                        : BoxDecoration(
                            image: backgroundType == "IMAGE"
                                ? DecorationImage(
                                    image: CachedNetworkImageProvider(
                                        background.data ?? ""),
                                    fit: BoxFit.cover,
                                  )
                                : null,
                            color: backgroundType == "COLOR"
                                ? Color(int.parse(
                                    "0xff${background.data ?? "FFFFFF"}"))
                                : null,
                          ),
                    child: Stack(
                      children: [
                        // Background overlay removed since background image contains all branding
                        // Container(
                        //   decoration: BoxDecoration(
                        //     gradient: LinearGradient(
                        //       begin: Alignment.topCenter,
                        //       end: Alignment.bottomCenter,
                        //       colors: [
                        //         Colors.black.withOpacity(0.1),
                        //         Colors.black.withOpacity(0.3),
                        //       ],
                        //     ),
                        //   ),
                        // ),
                        
                        // Card content
                        Padding(
                          padding: EdgeInsets.all(defaultPadding),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Empty space for the background image content (logo, hotel name, advantage text)
                              SizedBox(height: 60), // Space for background image content
                              
                              Spacer(),
                              
                              // Member name and points - right aligned
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  // Reload button on the left
                                  if (isLogin)
                                    GestureDetector(
                                      onTap: () {
                                        Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                                builder: (BuildContext context) =>
                                                    CreditTopUp())).then((value) {
                                          setState(() {
                                            future = openBox();
                                          });
                                        });
                                      },
                                      child: Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 12,
                                          vertical: 6,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.white.withOpacity(0.2),
                                          borderRadius: BorderRadius.circular(15),
                                          border: Border.all(
                                            color: Colors.black.withOpacity(0.3),
                                          ),
                                        ),
                                        child: Text(
                                          "Reload",
                                          style: TextStyle(
                                            color: Colors.black,
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    )
                                  else
                                    SizedBox(), // Placeholder when not logged in
                                  
                                  // Name and points on the right
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      // Member name
                                      Text(
                                        isLogin ? user!.fullName.toUpperCase() : "GUEST",
                                        style: TextStyle(
                                          color: Colors.black,
                                          fontSize: 24,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      
                                      SizedBox(height: 8),
                                      
                                      // Points
                                      if (pointLabel != null)
                                        Text(
                                          () {
                                            String points = "0";
                                            if (isLogin) points = user!.point.split('.')[0]; // Remove decimal for points
                                            return "$points ${list.values.firstWhereOrNull((element) => element.name == "POINT_UNIT")?.data ?? "Points"}";
                                          }(),
                                          style: TextStyle(
                                            color: Colors.black,
                                            fontSize: 20,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                    ],
                                  ),
                                ],
                              ),
                              
                              SizedBox(height: 8),
                              
                              // Expiry date (you might want to get this from user data)
                              Text(
                                "Expires on 19 April 2027", // Replace with actual expiry date
                                style: TextStyle(
                                  color: Colors.black.withOpacity(0.7),
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }
          }
          return Container();
        });
  }
}