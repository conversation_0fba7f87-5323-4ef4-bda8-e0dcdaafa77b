import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Notification/notification_page.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class ProfileTemplate1 extends StatefulWidget {
  final String theme;

  const ProfileTemplate1({Key? key, required this.theme}) : super(key: key);

  @override
  _ProfileTemplate1State createState() => _ProfileTemplate1State();
}

class _ProfileTemplate1State extends State<ProfileTemplate1> {
  late Future future;
  late Box box;
  late Box boxGlobal;

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('ACCOUNT+PROFILE+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            bool isLogin = box.get("is_login") ?? false;

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return Container(
              width: phoneWidth,
              height: phoneHeight / 11.5,
              decoration: background == null
                  ? null
                  : BoxDecoration(
                      image: backgroundType == "IMAGE"
                          ? DecorationImage(
                              image: CachedNetworkImageProvider(
                                  background.data ?? ""),
                              fit: BoxFit.cover,
                            )
                          : null,
                      color: backgroundType == "COLOR"
                          ? Color(
                              int.parse("0xff${background.data ?? "FFFFFF"}"))
                          : null,
                    ),
              padding: EdgeInsets.all(defaultPadding),
              child: Row(
                children: [
                  const Spacer(),
                  SizedBox(width: phoneWidth / 10),
                  Text(
                    "Profile",
                    style: TextStyle(
                      color: widget.theme == "L"
                          ? ThemeColors.dark
                          : ThemeColors.light,
                      fontSize: h1,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const Spacer(),
                  Align(
                    alignment: Alignment.centerRight,
                    child: IconButton(
                      onPressed: () {
                        if (isLogin) {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (BuildContext context) =>
                                      const NotificationPage()));
                        } else {
                          Navigator.of(context, rootNavigator: true)
                              .pushNamed("/login/${loginAsset.template}");
                        }
                      },
                      icon: ImageIcon(
                        AssetImage(
                            "assets/icons/${boxGlobal.get("iconSet")}/notification_Bell.png"),
                        color: widget.theme == "L"
                            ? ThemeColors.dark
                            : ThemeColors.light,
                      ),
                    ),
                  )
                ],
              ),
            );
          }
          return Container();
        });
  }
}
