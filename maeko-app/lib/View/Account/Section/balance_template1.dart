import 'dart:convert';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/user.dart';
import 'package:amverton/Provider/providers.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Account/account_page.dart';
import 'package:amverton/View/Credit/credit_history_page.dart';
import 'package:amverton/View/Credit/credit_topup.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Points/points_history_page.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class BalanceTemplate1 extends ConsumerStatefulWidget {
  final String theme;

  const BalanceTemplate1({Key? key, required this.theme}) : super(key: key);

  @override
  _BalanceTemplate1State createState() => _BalanceTemplate1State();
}

class _BalanceTemplate1State extends ConsumerState<BalanceTemplate1> {
  late Future future;
  late Box box;
  late Box boxGlobal;
  bool enlarge = false;

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('ACCOUNT+BALANCE+1');
  }

  @override
  Widget build(BuildContext context) {
    User user = ref.watch(userProvider);

    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String barcode = "";
            bool isLogin = box.get("is_login") ?? false;
            if (isLogin) {
              barcode = box.get("barcode");
            }

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            String? innerBackgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "INNER_BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? innerBackground = list.values.firstWhereOrNull((element) =>
                element.name == "INNER_BACKGROUND_$innerBackgroundType");

            String? balanceLabel = list.values
                .firstWhereOrNull((element) => element.name == "CREDIT_LABEL")
                ?.data;
            String? pointLabel = list.values
                .firstWhereOrNull((element) => element.name == "POINT_LABEL")
                ?.data;

            if (list.isNotEmpty) {
              return Container(
                decoration: background == null
                    ? null
                    : BoxDecoration(
                        image: backgroundType == "IMAGE"
                            ? DecorationImage(
                                image: CachedNetworkImageProvider(
                                    background.data ?? ""),
                                fit: BoxFit.cover,
                              )
                            : null,
                        color: backgroundType == "COLOR"
                            ? Color(
                                int.parse("0xff${background.data ?? "FFFFFF"}"))
                            : null,
                      ),
                child: Container(
                  width: phoneWidth,
                  margin: EdgeInsets.all(defaultPadding),
                  padding: EdgeInsets.symmetric(
                      horizontal: defaultInnerPadding,
                      vertical: defaultPadding),
                  decoration: innerBackground == null
                      ? BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: widget.theme == "L"
                                ? ThemeColors.disabled
                                : ThemeColors.gray,
                          ),
                        )
                      : BoxDecoration(
                          image: innerBackgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      innerBackground.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: innerBackgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${innerBackground.data ?? "FFFFFF"}"))
                              : null,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: widget.theme == "L"
                                ? ThemeColors.disabled
                                : ThemeColors.gray,
                          ),
                        ),
                  child: Column(
                    children: [
                      SizedBox(height: spacingHeightSmall),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              isLogin ? user!.fullName : "Guest",
                              style: TextStyle(
                                color: widget.theme == "L"
                                    ? ThemeColors.dark
                                    : ThemeColors.light,
                                fontSize: h1,
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          SizedBox(width: spacingWidth),
                          if (isLogin)
                            GestureDetector(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (BuildContext context) =>
                                            CreditTopUp())).then((value) {
                                  setState(() {
                                    future = openBox();
                                  });
                                });
                              },
                              child: Text(
                                "Reload",
                                style: TextStyle(
                                  color: ThemeColors.primaryDark,
                                  fontSize: h3,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            )
                        ],
                      ),
                      SizedBox(height: spacingHeightMedium),
                      Row(
                        children: [
                          if (balanceLabel != null)
                            Expanded(
                                child: Container(
                              padding:
                                  EdgeInsets.only(right: defaultInnerPadding),
                              decoration: BoxDecoration(
                                  border: pointLabel == null
                                      ? null
                                      : Border(
                                          right: BorderSide(
                                            color: widget.theme == "L"
                                                ? ThemeColors.dark
                                                : ThemeColors.light,
                                          ),
                                        )),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          balanceLabel,
                                          style: TextStyle(
                                              color: widget.theme == "L"
                                                  ? ThemeColors.dark
                                                  : ThemeColors.light,
                                              fontSize: h3),
                                        ),
                                        SizedBox(height: spacingHeightSmall),
                                        Text(
                                          () {
                                            String credit = "0.00";
                                            if (isLogin) credit = user!.point;

                                            return "${list.values.firstWhereOrNull((element) => element.name == "CREDIT_UNIT")?.data ?? ""} $credit";
                                          }(),
                                          style: TextStyle(
                                            color: widget.theme == "L"
                                                ? ThemeColors.dark
                                                : ThemeColors.light,
                                            fontSize: h2,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(width: spacingWidth),
                                  if (isLogin)
                                    GestureDetector(
                                      onTap: () {
                                        Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                                builder: (BuildContext
                                                        context) =>
                                                    const CreditHistoryPage()));
                                      },
                                      child: ImageIcon(
                                        AssetImage(
                                            "assets/icons/${boxGlobal.get("iconSet")}/History Icon.png"),
                                        size: 22,
                                        color: ThemeColors.primaryDark,
                                      ),
                                    ),
                                ],
                              ),
                            )),
                          if (pointLabel != null)
                            Expanded(
                              child: Container(
                                padding:
                                    EdgeInsets.only(left: defaultInnerPadding),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            pointLabel,
                                            style: TextStyle(
                                                color: widget.theme == "L"
                                                    ? ThemeColors.dark
                                                    : ThemeColors.light,
                                                fontSize: h3),
                                          ),
                                          SizedBox(height: spacingHeightSmall),
                                          Text(
                                            () {
                                              String points = "0.00";
                                              if (isLogin) points = user!.point;

                                              return "$points ${list.values.firstWhereOrNull((element) => element.name == "POINT_UNIT")?.data ?? ""}";
                                            }(),
                                            style: TextStyle(
                                              color: widget.theme == "L"
                                                  ? ThemeColors.dark
                                                  : ThemeColors.light,
                                              fontSize: h2,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(width: spacingWidth),
                                    if (isLogin &&
                                        accountAsset.firstWhereOrNull(
                                                (element) =>
                                                    element.sectionName ==
                                                    "POINT_HISTORY") !=
                                            null)
                                      GestureDetector(
                                        onTap: () {
                                          Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                  builder: (BuildContext
                                                          context) =>
                                                      const PointHistoryPage()));
                                        },
                                        child: ImageIcon(
                                            AssetImage(
                                                "assets/icons/${boxGlobal.get("iconSet")}/History Icon.png"),
                                            color: ThemeColors.primaryDark),
                                      )
                                  ],
                                ),
                              ),
                            ),
                        ],
                      ),
                      if (!isLogin)
                        Container(
                          padding: EdgeInsets.only(top: defaultPadding),
                          width: phoneWidth,
                          child: DefaultButton(
                            text: "Login / Register now",
                            buttonColor: ThemeColors.primaryDark,
                            onPressed: () {
                              Navigator.of(context, rootNavigator: true)
                                  .pushNamed("/login/${loginAsset.template}");
                            },
                          ),
                        ),
                      if (isLogin)
                        Column(
                          children: [
                            SizedBox(height: spacingHeightMedium),
                            if (enlarge)
                              Container(
                                width: phoneWidth,
                                margin: EdgeInsets.only(
                                    bottom: defaultInnerPadding),
                                padding: EdgeInsets.symmetric(
                                    horizontal: defaultInnerPadding,
                                    vertical: defaultPadding),
                                decoration: BoxDecoration(
                                  color: ThemeColors.light,
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: widget.theme == "L"
                                        ? ThemeColors.disabled
                                        : ThemeColors.gray,
                                  ),
                                ),
                                child: Image.memory(base64Decode(barcode)),
                              ),
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  enlarge = !enlarge;
                                });
                              },
                              child: Text(
                                enlarge ? "Hide Barcode" : "Show Barcode",
                                style: TextStyle(
                                  color: widget.theme == "L"
                                      ? ThemeColors.dark
                                      : ThemeColors.light,
                                  fontSize: h3,
                                ),
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
              );
            }
          }
          return Container();
        });
  }
}
