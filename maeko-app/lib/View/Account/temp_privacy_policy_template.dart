import 'package:flutter/material.dart';

class PrivacyPolicyPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Privacy Policy"),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "<PERSON><PERSON><PERSON>'s Privacy Policy",
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Sized<PERSON>ox(height: 16),
            Text(
              "1. Introduction",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "<PERSON><PERSON><PERSON>'s is committed to protecting your privacy. This privacy policy outlines how we collect, use, and protect your personal information when you visit our website or place an order with us.",
            ),
            SizedBox(height: 16),
            Text(
              "2. Information We Collect",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "We collect the following information from our customers:",
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                      "• Personal Information: Name, email address, phone number, and delivery address."),
                  Text(
                      "• Payment Information: Credit card details and billing address."),
                  Text("• Order Information: Details of the orders you place."),
                  Text(
                      "• Usage Information: Information about how you use our website, such as the pages you visit and the links you click."),
                ],
              ),
            ),
            SizedBox(height: 16),
            Text(
              "3. How We Use Your Information",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "We use your information for the following purposes:",
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("• To process and deliver your orders."),
                  Text(
                      "• To communicate with you about your orders and our services."),
                  Text("• To improve our website and services."),
                  Text(
                      "• To send you promotional offers and updates, if you have opted in to receive them."),
                ],
              ),
            ),
            SizedBox(height: 16),
            Text(
              "4. Sharing Your Information",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "We do not share your personal information with third parties, except in the following circumstances:",
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                      "• With service providers who help us operate our business, such as delivery partners and payment processors."),
                  Text("• When required by law or to protect our rights."),
                  Text(
                      "• With your consent, or if you have opted in to receive third-party offers."),
                ],
              ),
            ),
            SizedBox(height: 16),
            Text(
              "5. Security of Your Information",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "We take the security of your personal information seriously and implement appropriate technical and organizational measures to protect it from unauthorized access, loss, or misuse.",
            ),
            SizedBox(height: 16),
            Text(
              "6. Your Rights",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "You have the following rights regarding your personal information:",
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                      "• The right to access the personal information we hold about you."),
                  Text(
                      "• The right to request the correction of inaccurate personal information."),
                  Text(
                      "• The right to request the deletion of your personal information."),
                  Text(
                      "• The right to object to the processing of your personal information."),
                  Text(
                      "• The right to withdraw your consent at any time, where we have obtained your consent to process your personal information."),
                ],
              ),
            ),
            SizedBox(height: 16),
            Text(
              "7. Changes to This Privacy Policy",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "We may update this privacy policy from time to time. We will notify you of any changes by posting the new privacy policy on our website. You are advised to review this privacy policy periodically for any changes.",
            ),
            SizedBox(height: 16),
            Text(
              "8. Contact Us",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "If you have any questions or concerns about our privacy policy, please contact us at:",
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("O'Brien's Customer Service"),
                  Text("Email: <EMAIL>"),
                  Text("Phone: +6(03) 9222 3646"),
                  Text(
                      "Address: No.70-3, Jalan Metro Pudu, Fraser Business Park, Off Jalan Loke Yew, 55100 Kuala Lumpur"),
                ],
              ),
            ),
            SizedBox(height: 16),
            Text(
              "By using our services, you agree to the terms outlined in this privacy policy. Thank you for choosing O'Brien's for your dining needs.",
            ),
          ],
        ),
      ),
    );
  }
}
