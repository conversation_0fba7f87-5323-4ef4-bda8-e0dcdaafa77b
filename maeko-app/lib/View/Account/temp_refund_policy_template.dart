import 'package:flutter/material.dart';

class RefundPolicyPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Refund Policy",
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "<PERSON><PERSON><PERSON>'s Refund Policy",
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            Text(
              "1. Introduction",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "At O'Brien's, customer satisfaction is our top priority. We strive to ensure that every meal you order meets your expectations. This refund policy outlines the conditions under which refunds will be granted for food and beverage orders.",
            ),
            SizedBox(height: 16),
            Text(
              "2. Eligibility for Refunds",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "Refunds will be considered under the following circumstances:",
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                      "• Incorrect Order: If you receive an order that is incorrect (e.g., wrong items, missing items)."),
                  Text(
                      "• Quality Issues: If the food delivered does not meet our quality standards (e.g., spoiled or undercooked)."),
                  Text(
                      "• Late Delivery: If the delivery exceeds the promised time frame significantly without prior notice."),
                ],
              ),
            ),
            SizedBox(height: 16),
            Text(
              "3. Non-Refundable Situations",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "Refunds will not be granted in the following situations:",
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                      "• Change of Mind: If you change your mind after placing the order."),
                  Text(
                      "• Incorrect Address: If the delivery address provided was incorrect."),
                  Text(
                      "• Unavailable for Delivery: If you were unavailable to receive the delivery at the provided address and time."),
                ],
              ),
            ),
            SizedBox(height: 16),
            Text(
              "4. Refund Process",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "To request a refund, please follow these steps:",
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                      "1. Contact Us: Contact our customer service team within 24 hours of receiving your order. You can reach <NAME_EMAIL> or call +6(03) 9222 3646."),
                  Text(
                      "2. Provide Details: Provide your order number, details of the issue, and any relevant photos if applicable."),
                  Text(
                      "3. Review: Our team will review your request and respond within 2 business days."),
                ],
              ),
            ),
            SizedBox(height: 16),
            Text(
              "5. Refund Resolution",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                      "• Approved Refunds: If your refund request is approved, we will process the refund to the original payment method within 5-7 business days."),
                  Text(
                      "• Partial Refunds: In cases where part of the order is eligible for a refund, we will issue a partial refund accordingly."),
                  Text(
                      "• Replacement Orders: As an alternative to a refund, we may offer to resend the correct items or provide a discount on your next order."),
                ],
              ),
            ),
            SizedBox(height: 16),
            Text(
              "6. Contact Information",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              "For any questions or concerns regarding our refund policy, please contact us at:",
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("O'Brien's Customer Service"),
                  Text("Email: <EMAIL>"),
                  Text("Phone: +6(03) 9222 3646"),
                  Text(
                      "Address: No.70-3, Jalan Metro Pudu, Fraser Business Park, Off Jalan Loke Yew, 55100 Kuala Lumpur"),
                ],
              ),
            ),
            SizedBox(height: 16),
            Text(
              "By placing an order with O'Brien's, you agree to the terms outlined in this refund policy. Thank you for choosing O'Brien's for your dining needs.",
            ),
          ],
        ),
      ),
    );
  }
}
