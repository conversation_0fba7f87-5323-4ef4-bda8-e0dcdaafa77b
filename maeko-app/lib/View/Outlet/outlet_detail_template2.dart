import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/bottom_bar.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/OrderMenu/order_menu_page.dart';
import 'package:amverton/main.dart';
import 'package:hive/hive.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:collection/collection.dart';

class OutletDetailTemplate2 extends StatefulWidget {
  final int outletId;

  const OutletDetailTemplate2({Key? key, required this.outletId})
      : super(key: key);

  @override
  _OutletDetailTemplate2State createState() => _OutletDetailTemplate2State();
}

class _OutletDetailTemplate2State extends State<OutletDetailTemplate2> {
  Box box = Hive.box("box");
  Box boxGlobal = Hive.box("boxGlobal");
  late Future boxFuture;
  late Future future;
  Outlet? outlet;
  List<String> buttonText = ["Navigation", "Pick up", "Satisfaction", "Share"];
  List<String> buttonImage = [
    "NAVIGATION_ICON",
    "PICK_UP_ICON",
    "SATISFACTION_ICON",
    "SHARE_ICON",
  ];

  List<Function()> buttonFunction = [];

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();
    future = getOutletDetail();
    buttonFunction = [
      () => navigation(),
      () => pickUp(),
      () => satisfaction(),
      () => share(),
    ];
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('OUTLETS+MAIN+2');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Outlet")),
      body: FutureBuilder(
          future: boxFuture,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            if (snapshot.connectionState == ConnectionState.done) {
              final Box list = snapshot.data;
              list.watch();

              String? backgroundType = list.values
                      .firstWhereOrNull(
                          (element) => element.name == "BACKGROUND_TYPE")
                      ?.data ??
                  "";
              Asset? background = list.values.firstWhereOrNull(
                  (element) => element.name == "BACKGROUND_$backgroundType");

              if (list.isNotEmpty) {
                return FutureBuilder(
                  future: future,
                  builder: (BuildContext context, AsyncSnapshot snapshot) {
                    if (snapshot.hasData) {
                      outlet = snapshot.data['data']['outlet'] == null
                          ? null
                          : Outlet.fromJson(snapshot.data['data']['outlet']);

                      if (outlet != null) {
                        return SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                height: phoneHeight / 2,
                                child: Stack(
                                  children: [
                                    ClipRRect(
                                      borderRadius: const BorderRadius.only(
                                        bottomLeft: Radius.circular(20),
                                        bottomRight: Radius.circular(20),
                                      ),
                                      child: CachedNetworkImage(
                                        imageUrl: outlet!.image ?? "",
                                        width: phoneWidth,
                                        height: phoneHeight / 4,
                                        fit: BoxFit.fitWidth,
                                        placeholder: (context, url) {
                                          return Center(
                                            child: CircularProgressIndicator(
                                              color: ThemeColors.primaryDark,
                                            ),
                                          );
                                        },
                                        errorWidget: (context, url, error) {
                                          return Icon(
                                            Icons.error_outline_outlined,
                                            color: ThemeColors.primaryDark,
                                          );
                                        },
                                      ),
                                    ),
                                    Positioned(
                                      top: phoneHeight / 4.75,
                                      left: defaultPadding,
                                      right: defaultPadding,
                                      child: Container(
                                        padding: EdgeInsets.all(defaultPadding),
                                        decoration: BoxDecoration(
                                          color: ThemeColors.light,
                                          borderRadius: const BorderRadius.all(
                                              Radius.circular(20)),
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            AutoSizeText(
                                              outlet!.name,
                                              style: TextStyle(
                                                fontSize: h3,
                                                color: ThemeColors.dark,
                                                fontWeight: FontWeight.bold,
                                              ),
                                              maxLines: 3,
                                            ),
                                            SizedBox(
                                                height: spacingHeightSmall),
                                            AutoSizeText(
                                              outlet!.address,
                                              style: TextStyle(
                                                  fontSize: h4,
                                                  color: ThemeColors.gray),
                                              maxLines: 4,
                                            ),
                                            SizedBox(
                                                height: spacingHeightLarge),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceEvenly,
                                              children: List.generate(
                                                  buttonText.length, (index) {
                                                return GestureDetector(
                                                  onTap: buttonFunction[index],
                                                  child: Container(
                                                    width: phoneWidth / 5,
                                                    child: Column(
                                                      children: [
                                                        CachedNetworkImage(
                                                          imageUrl: list.values
                                                                  .firstWhereOrNull((element) =>
                                                                      element
                                                                          .name ==
                                                                      buttonImage[
                                                                          index])
                                                                  ?.data ??
                                                              "",
                                                          height:
                                                              phoneHeight / 35,
                                                          width:
                                                              phoneHeight / 35,
                                                          fit: BoxFit.cover,
                                                          placeholder:
                                                              (context, url) {
                                                            return Center(
                                                              child:
                                                                  CircularProgressIndicator(
                                                                color: ThemeColors
                                                                    .primaryDark,
                                                              ),
                                                            );
                                                          },
                                                          errorWidget: (context,
                                                              url, error) {
                                                            return Icon(
                                                              Icons
                                                                  .error_outline_outlined,
                                                              color: ThemeColors
                                                                  .primaryDark,
                                                            );
                                                          },
                                                        ),
                                                        SizedBox(
                                                            height:
                                                                spacingHeightSmall),
                                                        Text(
                                                          buttonText[index],
                                                          style: TextStyle(
                                                              fontSize: h4),
                                                        )
                                                      ],
                                                    ),
                                                  ),
                                                );
                                              }),
                                            ),
                                            SizedBox(
                                                height: spacingHeightMedium),
                                            if (outlet!.phone != null)
                                              Row(
                                                children: [
                                                  Expanded(
                                                    child: AutoSizeText(
                                                      outlet!.phone!,
                                                      style: TextStyle(
                                                          fontSize: h3,
                                                          color:
                                                              ThemeColors.dark),
                                                      maxLines: 3,
                                                    ),
                                                  ),
                                                  IconButton(
                                                    onPressed: () {
                                                      phoneCall(outlet!.phone!);
                                                    },
                                                    icon: CachedNetworkImage(
                                                      imageUrl: list.values
                                                              .firstWhereOrNull(
                                                                  (element) =>
                                                                      element
                                                                          .name ==
                                                                      "PHONE_ICON")
                                                              ?.data ??
                                                          "",
                                                      height: phoneHeight / 35,
                                                      width: phoneHeight / 35,
                                                      fit: BoxFit.cover,
                                                      placeholder:
                                                          (context, url) {
                                                        return Center(
                                                          child:
                                                              CircularProgressIndicator(
                                                            color: ThemeColors
                                                                .primaryDark,
                                                          ),
                                                        );
                                                      },
                                                      errorWidget: (context,
                                                          url, error) {
                                                        return Icon(
                                                          Icons
                                                              .error_outline_outlined,
                                                          color: ThemeColors
                                                              .primaryDark,
                                                        );
                                                      },
                                                    ),
                                                  ),
                                                ],
                                              ),
                                          ],
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                    left: defaultPadding,
                                    right: defaultPadding,
                                    top: defaultPadding,
                                    bottom: defaultInnerPadding),
                                child: Text(
                                  list.values
                                          .firstWhereOrNull((element) =>
                                              element.name ==
                                              "BUSINESS_HOUR_TITLE")
                                          ?.data ??
                                      "",
                                  style: TextStyle(
                                      color: ThemeColors.dark,
                                      fontSize: h2,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                              if (outlet!.operationHours != null)
                                Column(
                                  children: List.generate(
                                      outlet!.operationHours!.length,
                                      (operationHoursIndex) {
                                    return Padding(
                                      padding: EdgeInsets.symmetric(
                                          vertical: spacingHeightSmall,
                                          horizontal: defaultPadding),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            outlet!
                                                .operationHours![
                                                    operationHoursIndex]
                                                .name,
                                            style: TextStyle(
                                              color: ThemeColors.dark,
                                              fontSize: h3,
                                            ),
                                          ),
                                          if (outlet!
                                                  .operationHours![
                                                      operationHoursIndex]
                                                  .hours !=
                                              null)
                                            Column(
                                              children: List.generate(
                                                outlet!
                                                    .operationHours![
                                                        operationHoursIndex]
                                                    .hours!
                                                    .length,
                                                (hoursIndex) => Text(
                                                  "${outlet!.operationHours![operationHoursIndex].hours![hoursIndex].startAt} - ${outlet!.operationHours![operationHoursIndex].hours![hoursIndex].endAt}",
                                                  style: TextStyle(
                                                    color: ThemeColors.dark,
                                                    fontSize: h3,
                                                  ),
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                    );
                                  }),
                                ),
                            ],
                          ),
                        );
                      }
                    }
                    return Container();
                  },
                );
              }
            }
            return Container();
          }),
    );
  }

  void navigation() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                onTap: () {
                  openMap(
                      "waze://?q=${outlet!.name}&ll=${outlet!.latitude},${outlet!.longitude}",
                      "https://waze.com/ul?q=${outlet!.name}&ll=${outlet!.latitude},${outlet!.longitude}&navigate=yes");
                },
                leading: Image.asset(
                  "assets/icons/waze.png",
                  width: phoneWidth * 0.07,
                ),
                title: Text('Waze'),
              ),
              ListTile(
                onTap: () {
                  openMap(
                      "google.navigation:q=${outlet!.latitude},${outlet!.longitude}",
                      "https://www.google.com/maps/search/?api=1&query=${outlet!.latitude},${outlet!.longitude}");
                },
                leading: Image.asset(
                  "assets/icons/google-maps.png",
                  width: phoneWidth * 0.07,
                ),
                title: Text('Google Maps'),
              ),
            ],
          ),
        );
      },
    );
  }

  void pickUp() async {
    Box boxMenu = await Hive.openBox('boxMenu');
    await boxMenu.put("selected_outlet", outlet);
    await boxMenu.put("selected_deliver_method", 1);

    List<BottomBar> bottomBarList = boxGlobal.get("bottomBar") ?? [];
    if (bottomBarList.isNotEmpty) {
      int orderIndex =
          bottomBarList.indexWhere((element) => element.name == "ORDER");
      if (orderIndex != -1) {
        defaultBar[orderIndex] = OrderMenuPage(
          outlet: outlet!,
          key: orderMenuPageKey,
        );
        final dynamic navigationBar = barGlobalKey.currentWidget;
        navigationBar.onTap(orderIndex);
      }
    }
  }

  void satisfaction() {
    bool isLogin = box.get("is_login") ?? false;
    if (isLogin) {
      Navigator.pushNamed(
        context,
        "/feedback/${accountAsset.firstWhereOrNull((element) => element.sectionName == "FEEDBACK")?.template ?? ""}",
        arguments: {
          "outletId": outlet!.id,
          "outletName": outlet!.name,
        },
      );
    } else {
      Navigator.of(context, rootNavigator: true)
          .pushNamed("/login/${loginAsset.template}");
    }
  }

  void share() async {
    final box = context.findRenderObject() as RenderBox?;
    await Share.share(
        "${outlet?.name ?? ""}\n${outlet?.address ?? ""}\n${outlet?.phone ?? ""}${outlet?.latitude == null && outlet?.longitude == null ? "" : "\nhttps://maps.google.com/?q=${outlet?.latitude},${outlet?.longitude}"}",
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size);
  }

  Future<Map?> getOutletDetail() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.outletDetail(widget.outletId);
    return data;
  }

  openMap(String url, String fallBackUrl) async {
    try {
      bool launched = await launchUrl(Uri.parse(url));
      if (!launched) {
        await launchUrl(Uri.parse(fallBackUrl));
      }
    } catch (e) {
      defaultErrorDialog(context);
    }
  }

  void phoneCall(String phone) async {
    try {
      final Uri launchUri = Uri(
        scheme: 'tel',
        path: phone,
      );
      await launchUrl(launchUri);
    } catch (e) {
      defaultErrorDialog(context);
    }
  }
}
