import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/location_service.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/loading2.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hive/hive.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class OutletPage extends StatefulWidget {
  const OutletPage({Key? key}) : super(key: key);

  @override
  _OutletPageState createState() => _OutletPageState();
}

Position? currentPosition;
List<Outlet> outletList = [];
LatLng? defaultLatLng;

class _OutletPageState extends State<OutletPage> {
  Box boxGlobal = Hive.box("boxGlobal");
  late Future future;

  @override
  void initState() {
    super.initState();
    future = getOutlet();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        behavior: HitTestBehavior.translucent,
        child: Stack(
          children: [
            Scaffold(
              body: RefreshIndicator(
                  color: ThemeColors.primaryDark,
                  onRefresh: () {
                    return Future.delayed(const Duration(seconds: 1), () {
                      setState(() {
                        future = getOutlet();
                      });
                    });
                  },
                  child: SizedBox(
                    height: phoneHeight,
                    child: FutureBuilder(
                        future: future,
                        builder:
                            (BuildContext context, AsyncSnapshot snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return Loading2();
                          } else if (snapshot.connectionState ==
                              ConnectionState.done) {
                            if (snapshot.hasData) {
                              return outletMainWidget ?? Container();
                            }
                          }
                          return Container();
                        }),
                  )),
            ),
          ],
        ));
  }

  Future<Map?> getOutlet() async {
    ApiGet apiGet = ApiGet();

    // check location permission
    if (Platform.isIOS) {
      currentPosition = await LocationService.getCurrentPosition();
    } else {
      Position? permissionCheck =
          await LocationService.checkAndroidLocationPermission();
      if (permissionCheck != null)
        currentPosition = await LocationService.getCurrentPosition();
    }

    Map? data = await apiGet.outletList(
        currentPosition?.latitude, currentPosition?.longitude);

    if (data != null && data['data'] != null) {
      List<Outlet> list = (data['data']['outlets'] as List)
          .map((data) => Outlet.fromJson(data))
          .toList();
      outletList = list;

      if (defaultLatLng == null) {
        defaultLatLng = LatLng(double.parse(outletList.first.latitude!),
            double.parse(outletList.first.longitude!));
      }
    }
    return data;
  }
}
