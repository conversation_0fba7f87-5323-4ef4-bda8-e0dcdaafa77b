import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Custom%20Widgets/default_search_bar.dart';
import 'package:amverton/View/Outlet/outlet_detail_template2.dart';
import 'package:amverton/View/Outlet/outlet_page.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:http/http.dart' as http;

class MapTemplate2 extends StatefulWidget {
  final String theme;

  const MapTemplate2({Key? key, required this.theme}) : super(key: key);

  @override
  _MapTemplate2State createState() => _MapTemplate2State();
}

class _MapTemplate2State extends State<MapTemplate2> {
  late Future future;
  late Box boxGlobal;
  bool onMap = true;
  Completer<GoogleMapController> _controller = Completer<GoogleMapController>();

  final Set<Marker> markers = {};
  String markerUrl = "";
  BitmapDescriptor? markerIcon;
  List<GlobalKey> keys = [];

  late Future searchFuture;
  bool onSearch = false;
  List<Outlet> searchList = [];
  Set<Marker> searchMarkers = {};
  TextEditingController searchController = TextEditingController();
  List<GlobalKey> searchKeys = [];

  @override
  void initState() {
    super.initState();
    future = openBox();
    searchFuture = getOutletSearch(null);
  }

  setupMap() async {
    try {
      var request = await http.get(Uri.parse(markerUrl));
      var markerByte = await request.bodyBytes;
      markerIcon =
          await BitmapDescriptor.fromBytes(markerByte.buffer.asUint8List());
    } catch (e) {
      print(e.toString());
    }

    keys = List.generate(outletList.length, (index) => GlobalKey());
    for (int i = 0; i < outletList.length; i++) {
      if (outletList[i].latitude != null && outletList[i].longitude != null) {
        if (outletList[i].isMain == 1) {
          defaultLatLng = LatLng(double.parse(outletList[i].latitude!),
              double.parse(outletList[i].longitude!));
        }
        markers.add(Marker(
            markerId: MarkerId(
                "${outletList[i].latitude}, ${outletList[i].longitude}"),
            position: LatLng(double.parse(outletList[i].latitude!),
                double.parse(outletList[i].longitude!)),
            icon: markerIcon ?? BitmapDescriptor.defaultMarker,
            onTap: () {
              Scrollable.ensureVisible(keys[i].currentContext!);
            }));
      }
    }
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    Box boxAsset = await Hive.openBox<Asset>('OUTLETS+MAIN+2');
    markerUrl = boxAsset.values
            .firstWhereOrNull((element) => element.name == "MAP_MARKER_ICON")
            ?.data ??
        "";
    await setupMap();

    return boxAsset;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return Column(
              children: [
                header(backgroundType, background),
                Expanded(
                  child: Stack(
                    children: [
                      GoogleMap(
                        onMapCreated: (GoogleMapController controller) {
                          _controller.complete(controller);
                        },
                        initialCameraPosition: CameraPosition(
                          target: defaultLatLng!,
                          zoom: 12,
                        ),
                        markers: onSearch ? searchMarkers : markers,
                        myLocationEnabled: true,
                        myLocationButtonEnabled: false,
                      ),
                      Visibility(
                        visible: !onSearch,
                        child: Positioned(
                          bottom: defaultPadding,
                          child: Container(
                            width: phoneWidth,
                            child: SingleChildScrollView(
                              physics: AlwaysScrollableScrollPhysics(),
                              scrollDirection: Axis.horizontal,
                              padding: EdgeInsets.only(left: defaultPadding),
                              child: Row(
                                children:
                                    List.generate(outletList.length, (index) {
                                  return itemView(index, outletList[index]);
                                }),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Visibility(
                        visible: onSearch,
                        child: Positioned(
                          bottom: defaultPadding,
                          child: Container(
                            width: phoneWidth,
                            child: FutureBuilder(
                                future: searchFuture,
                                builder: (BuildContext context,
                                    AsyncSnapshot snapshot) {
                                  if (snapshot.connectionState ==
                                      ConnectionState.waiting) {
                                    return Center(
                                        child: CircularProgressIndicator(
                                      color: ThemeColors.primaryDark,
                                    ));
                                  } else if (snapshot.connectionState ==
                                      ConnectionState.done) {
                                    return SingleChildScrollView(
                                      physics: AlwaysScrollableScrollPhysics(),
                                      scrollDirection: Axis.horizontal,
                                      padding:
                                          EdgeInsets.only(left: defaultPadding),
                                      child: Row(
                                        children: List.generate(
                                            searchList.length, (index) {
                                          return itemView(
                                              index, searchList[index]);
                                        }),
                                      ),
                                    );
                                  } else {
                                    return Container();
                                  }
                                }),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          }
          return Container();
        });
  }

  Widget header(String? backgroundType, Asset? background) {
    return Container(
      color: backgroundType == "COLOR"
          ? Color(int.parse("0xff${background?.data ?? "FFFFFF"}"))
          : widget.theme == "L"
              ? ThemeColors.light
              : ThemeColors.dark,
      padding: EdgeInsets.only(
        top: topPaddingWithoutBar,
        left: defaultPadding,
        right: defaultPadding,
      ),
      child: DefaultSearchBar(
        controller: searchController,
        hintText: "Search",
        borderRadius: 15,
        onChanged: (value) {
          if (value.isEmpty && onSearch) {
            setState(() {
              onSearch = false;
            });
          } else if (value.isNotEmpty) {
            setState(() {
              onSearch = true;
              searchMarkers.clear();
              searchFuture = getOutletSearch(value);
            });
          }
        },
      ),
    );
  }

  Widget itemView(int index, Outlet outlet) {
    return GestureDetector(
      key: onSearch ? searchKeys[index] : keys[index],
      onTap: () async {
        if (outlet.latitude != null && outlet.longitude != null) {
          final GoogleMapController controller = await _controller.future;
          controller
              .animateCamera(CameraUpdate.newCameraPosition(CameraPosition(
            target: LatLng(double.parse(outlet.latitude!),
                double.parse(outlet.longitude!)),
            zoom: 15,
          )));
        }
      },
      child: Container(
        margin: EdgeInsets.only(right: defaultInnerPadding),
        padding: EdgeInsets.all(defaultInnerPadding),
        width: phoneWidth / 1.25,
        decoration: BoxDecoration(
          color: ThemeColors.light,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(20)),
              child: CachedNetworkImage(
                imageUrl: outlet.image ?? "",
                height: phoneWidth / 5,
                width: phoneWidth / 5,
                fit: BoxFit.cover,
                placeholder: (context, url) {
                  return Center(
                    child: CircularProgressIndicator(
                      color: ThemeColors.primaryDark,
                    ),
                  );
                },
                errorWidget: (context, url, error) {
                  return Icon(
                    Icons.error_outline_outlined,
                    color: ThemeColors.primaryDark,
                  );
                },
              ),
            ),
            SizedBox(width: spacingHeightSmall),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    outlet.name,
                    style: TextStyle(
                      color: widget.theme == "L"
                          ? ThemeColors.dark
                          : ThemeColors.light,
                      fontSize: h3,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: spacingHeightSmall),
                  Text(
                    outlet.phone ?? "",
                    style: TextStyle(
                        color: widget.theme == "L"
                            ? ThemeColors.dark
                            : ThemeColors.light,
                        fontSize: h4),
                  ),
                ],
              ),
            ),
            SizedBox(width: defaultPadding),
            GestureDetector(
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (BuildContext context) =>
                          OutletDetailTemplate2(outletId: outlet.id),
                    ));
              },
              child: ImageIcon(
                AssetImage(
                    "assets/icons/${boxGlobal.get("iconSet")}/Standard_Moreleft.png"),
                color: ThemeColors.dark,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<Map?> getOutletSearch(String? searchValue) async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.outletListSearch(searchValue);

    if (data != null && data['data'] != null) {
      searchList = (data['data']['outlets'] as List)
          .map((data) => Outlet.fromJson(data))
          .toList();

      List<Marker> markers = [];
      searchKeys =
          await List.generate(searchList.length, (index) => GlobalKey());

      for (int i = 0; i < searchList.length; i++) {
        if (searchList[i].latitude != null && searchList[i].longitude != null) {
          markers.add(Marker(
            markerId: MarkerId(
                "${searchList[i].latitude}, ${searchList[i].longitude}"),
            position: LatLng(double.parse(searchList[i].latitude!),
                double.parse(searchList[i].longitude!)),
            icon: markerIcon ?? BitmapDescriptor.defaultMarker,
            onTap: () {
              Scrollable.ensureVisible(searchKeys[i].currentContext!);
            },
          ));
        }
      }
      setState(() {
        searchMarkers.addAll(markers);
      });
    }

    return data;
  }
}
