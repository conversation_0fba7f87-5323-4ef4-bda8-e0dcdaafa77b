import 'dart:async';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_search_bar.dart';
import 'package:amverton/View/Outlet/outlet_page.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:http/http.dart' as http;

class MapTemplate1 extends StatefulWidget {
  final String theme;

  const MapTemplate1({Key? key, required this.theme}) : super(key: key);

  @override
  _MapTemplate1State createState() => _MapTemplate1State();
}

class _MapTemplate1State extends State<MapTemplate1> {
  late Future future;
  late Box boxGlobal;
  bool onMap = true;
  Completer<GoogleMapController> _controller = Completer<GoogleMapController>();

  final Set<Marker> markers = {};
  String markerUrl = "";
  BitmapDescriptor? markerIcon;
  PanelController panelController = PanelController();
  bool onDetail = false;
  late Outlet outletDetail;

  late Future searchFuture;
  bool onSearch = false;
  List<Outlet> searchList = [];
  Set<Marker> searchMarkers = {};
  TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    future = openBox();
    searchFuture = getOutletSearch(null);
  }

  setupMap() async {
    try {
      var request = await http.get(Uri.parse(markerUrl));
      var markerByte = await request.bodyBytes;
      markerIcon =
          await BitmapDescriptor.fromBytes(markerByte.buffer.asUint8List());
    } catch (e) {
      print(e.toString());
    }

    for (Outlet each in outletList) {
      if (each.latitude != null && each.longitude != null) {
        markers.add(Marker(
            markerId: MarkerId("${each.latitude}, ${each.longitude}"),
            position: LatLng(
                double.parse(each.latitude!), double.parse(each.longitude!)),
            icon: markerIcon ?? BitmapDescriptor.defaultMarker,
            onTap: () {
              panelController.open();
              setState(() {
                onDetail = true;
                outletDetail = each;
              });
            }));
      }
    }
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    Box boxAsset = await Hive.openBox<Asset>('OUTLETS+MAIN+1');
    markerUrl = boxAsset.values
            .firstWhereOrNull((element) => element.name == "MAP_MARKER_ICON")
            ?.data ??
        "";
    await setupMap();

    return boxAsset;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return Column(
              children: [
                header(backgroundType, background),
                Expanded(
                  child: Stack(
                    children: [
                      SlidingUpPanel(
                        color: backgroundType == "COLOR"
                            ? Color(int.parse(
                                "0xff${background?.data ?? "FFFFFF"}"))
                            : widget.theme == "L"
                                ? ThemeColors.light
                                : ThemeColors.dark,
                        controller: panelController,
                        minHeight: phoneHeight / 5,
                        maxHeight: phoneHeight / 1.8,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20),
                        ),
                        parallaxEnabled: true,
                        parallaxOffset: 0.5,
                        body: Column(
                          children: [
                            Expanded(
                              flex: 4,
                              child: GoogleMap(
                                onMapCreated: (GoogleMapController controller) {
                                  _controller.complete(controller);
                                },
                                initialCameraPosition: CameraPosition(
                                  target: defaultLatLng!,
                                  zoom: 12,
                                ),
                                markers: onSearch ? searchMarkers : markers,
                                myLocationEnabled: true,
                              ),
                            ),
                            Expanded(flex: 3, child: Container())
                          ],
                        ),
                        panelBuilder: (controller) {
                          if (onDetail) {
                            return detailView(controller);
                          } else {
                            if (!onSearch) {
                              return dataView(controller, list);
                            } else {
                              return searchView(controller);
                            }
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ],
            );
          }
          return Container();
        });
  }

  Widget header(String? backgroundType, Asset? background) {
    return Container(
      color: backgroundType == "COLOR"
          ? Color(int.parse("0xff${background?.data ?? "FFFFFF"}"))
          : widget.theme == "L"
              ? ThemeColors.light
              : ThemeColors.dark,
      padding: EdgeInsets.only(
        top: topPaddingWithoutBar,
        left: defaultPadding,
        right: defaultPadding,
      ),
      child: DefaultSearchBar(
        controller: searchController,
        hintText: "Search",
        onChanged: (value) {
          if (value.isEmpty && onSearch) {
            setState(() {
              onSearch = false;
            });
          } else if (value.isNotEmpty) {
            setState(() {
              onSearch = true;
              onDetail = false;
              searchMarkers.clear();
              searchFuture = getOutletSearch(value);
            });
          }
        },
      ),
    );
  }

  Widget dataView(ScrollController controller, Box list) {
    return SingleChildScrollView(
      padding: EdgeInsets.only(top: defaultPadding),
      controller: controller,
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(bottom: defaultPadding),
            height: 7,
            width: 35,
            decoration: BoxDecoration(
                color: ThemeColors.disabled,
                borderRadius: const BorderRadius.all(Radius.circular(20))),
          ),
          Text(
            list.values
                    .firstWhereOrNull((element) => element.name == "TITLE")
                    ?.data ??
                "",
            style: TextStyle(
                color:
                    widget.theme == "L" ? ThemeColors.dark : ThemeColors.light,
                fontSize: h1,
                fontWeight: FontWeight.bold),
          ),
          Column(
            children: List.generate(outletList.length, (index) {
              return ListTile(
                onTap: () async {
                  if (outletList[index].latitude != null &&
                      outletList[index].longitude != null) {
                    final GoogleMapController controller =
                        await _controller.future;
                    controller.animateCamera(
                        CameraUpdate.newCameraPosition(CameraPosition(
                      target: LatLng(double.parse(outletList[index].latitude!),
                          double.parse(outletList[index].longitude!)),
                      zoom: 15,
                    )));
                  }
                  panelController.open();
                  setState(() {
                    onDetail = true;
                    outletDetail = outletList[index];
                  });
                },
                title: Text(
                  outletList[index].name,
                  style: TextStyle(
                    color: widget.theme == "L"
                        ? ThemeColors.dark
                        : ThemeColors.light,
                    fontSize: h2,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                subtitle: Text(
                  () {
                    String distance = "";

                    // api will return distance even if no user's current position pass in api
                    if (outletList[index].distance != null &&
                        currentPosition != null) {
                      distance = outletList[index].distance!.toStringAsFixed(2);
                      return distance + "km Away from you";
                    } else {
                      return "-";
                    }
                  }(),
                  style: TextStyle(
                      color: widget.theme == "L"
                          ? ThemeColors.dark
                          : ThemeColors.light,
                      fontSize: h4),
                ),
                trailing: ImageIcon(
                  AssetImage(
                      "assets/icons/${boxGlobal.get("iconSet")}/Standard_Moreleft.png"),
                  color: widget.theme == "L"
                      ? ThemeColors.dark
                      : ThemeColors.light,
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget searchView(ScrollController controller) {
    return SingleChildScrollView(
      padding: EdgeInsets.only(top: defaultPadding),
      controller: controller,
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(bottom: defaultPadding),
            height: 7,
            width: 35,
            decoration: BoxDecoration(
                color: widget.theme == "L"
                    ? ThemeColors.disabled
                    : ThemeColors.gray,
                borderRadius: const BorderRadius.all(Radius.circular(20))),
          ),
          Text(
            "Search Result",
            style: TextStyle(
                color:
                    widget.theme == "L" ? ThemeColors.dark : ThemeColors.light,
                fontSize: h1,
                fontWeight: FontWeight.bold),
          ),
          FutureBuilder(
            future: searchFuture,
            builder: (BuildContext context, AsyncSnapshot snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return SizedBox(
                  height: phoneHeight / 3,
                  child: Center(
                    child: CircularProgressIndicator(
                        color: ThemeColors.primaryDark),
                  ),
                );
              } else if (snapshot.connectionState == ConnectionState.done) {
                return Column(
                  children: List.generate(searchList.length, (index) {
                    return ListTile(
                      onTap: () async {
                        if (searchList[index].latitude != null &&
                            searchList[index].longitude != null) {
                          final GoogleMapController controller =
                              await _controller.future;
                          controller.animateCamera(
                              CameraUpdate.newCameraPosition(CameraPosition(
                            target: LatLng(
                                double.parse(searchList[index].latitude!),
                                double.parse(searchList[index].longitude!)),
                            zoom: 15,
                          )));
                        }
                        panelController.open();
                        setState(() {
                          onDetail = true;
                          outletDetail = searchList[index];
                        });
                      },
                      title: Text(
                        searchList[index].name,
                        style: TextStyle(
                          color: widget.theme == "L"
                              ? ThemeColors.dark
                              : ThemeColors.light,
                          fontSize: h2,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      subtitle: Text(
                        "1.2 Away from you",
                        style: TextStyle(
                            color: widget.theme == "L"
                                ? ThemeColors.dark
                                : ThemeColors.light,
                            fontSize: h4),
                      ),
                      trailing: ImageIcon(
                        AssetImage(
                            "assets/icons/${boxGlobal.get("iconSet")}/Standard_Moreleft.png"),
                        color: widget.theme == "L"
                            ? ThemeColors.dark
                            : ThemeColors.light,
                      ),
                    );
                  }),
                );
              } else {
                return Container();
              }
            },
          ),
        ],
      ),
    );
  }

  Widget detailView(ScrollController controller) {
    return Stack(
      children: [
        SingleChildScrollView(
          padding: EdgeInsets.all(defaultPadding),
          controller: controller,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Align(
                alignment: Alignment.center,
                child: Container(
                  margin: EdgeInsets.only(bottom: defaultPadding),
                  height: 7,
                  width: 35,
                  decoration: BoxDecoration(
                      color: widget.theme == "L"
                          ? ThemeColors.disabled
                          : ThemeColors.gray,
                      borderRadius:
                          const BorderRadius.all(Radius.circular(20))),
                ),
              ),
              Text(
                outletDetail.name,
                style: TextStyle(
                    color: widget.theme == "L"
                        ? ThemeColors.dark
                        : ThemeColors.light,
                    fontSize: h1,
                    fontWeight: FontWeight.bold),
              ),
              SizedBox(height: spacingHeightMedium),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      outletDetail.address,
                      style: TextStyle(
                        color: widget.theme == "L"
                            ? ThemeColors.gray
                            : ThemeColors.disabled,
                        fontSize: h4,
                      ),
                    ),
                  ),
                  SizedBox(width: spacingWidth),
                  GestureDetector(
                    onTap: () {
                      showModalBottomSheet(
                        context: context,
                        builder: (context) {
                          return SafeArea(
                            child: Wrap(
                              children: [
                                ListTile(
                                  onTap: () {
                                    openMap(
                                        "waze://?q=${outletDetail.name}&ll=${outletDetail.latitude},${outletDetail.longitude}",
                                        "https://waze.com/ul?q=${outletDetail.name}&ll=${outletDetail.latitude},${outletDetail.longitude}&navigate=yes");
                                  },
                                  leading: Image.asset(
                                    "assets/icons/waze.png",
                                    width: phoneWidth * 0.07,
                                  ),
                                  title: Text('Waze'),
                                ),
                                ListTile(
                                  onTap: () {
                                    openMap(
                                        "google.navigation:q=${outletDetail.latitude},${outletDetail.longitude}",
                                        "https://www.google.com/maps/search/?api=1&query=${outletDetail.latitude},${outletDetail.longitude}");
                                  },
                                  leading: Image.asset(
                                    "assets/icons/google-maps.png",
                                    width: phoneWidth * 0.07,
                                  ),
                                  title: Text('Google Maps'),
                                ),
                              ],
                            ),
                          );
                        },
                      );
                    },
                    child: ImageIcon(AssetImage(
                        "assets/icons/${boxGlobal.get("iconSet")}/Location 02.png")),
                  )
                ],
              ),
              if (outletDetail.operationHours != null)
                Padding(
                  padding: EdgeInsets.only(top: verticalPaddingSmall),
                  child: Text(
                    "Business Hours",
                    style: TextStyle(
                        color: widget.theme == "L"
                            ? ThemeColors.dark
                            : ThemeColors.light,
                        fontSize: h2,
                        fontWeight: FontWeight.bold),
                  ),
                ),
              if (outletDetail.operationHours != null)
                Column(
                  children: List.generate(outletDetail.operationHours!.length,
                      (operationHoursIndex) {
                    return Padding(
                      padding:
                          EdgeInsets.symmetric(vertical: spacingHeightSmall),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            outletDetail
                                .operationHours![operationHoursIndex].name,
                            style: TextStyle(
                              color: widget.theme == "L"
                                  ? ThemeColors.dark
                                  : ThemeColors.light,
                              fontSize: h3,
                            ),
                          ),
                          if (outletDetail
                                  .operationHours![operationHoursIndex].hours !=
                              null)
                            Column(
                              children: List.generate(
                                outletDetail
                                    .operationHours![operationHoursIndex]
                                    .hours!
                                    .length,
                                (hoursIndex) => Padding(
                                  padding: EdgeInsets.symmetric(vertical: 2),
                                  child: Text(
                                    "${outletDetail.operationHours![operationHoursIndex].hours![hoursIndex].startAt} - ${outletDetail.operationHours![operationHoursIndex].hours![hoursIndex].endAt}",
                                    style: TextStyle(
                                      color: widget.theme == "L"
                                          ? ThemeColors.dark
                                          : ThemeColors.light,
                                      fontSize: h3,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    );
                  }),
                ),
            ],
          ),
        ),
        if (outletDetail.phone != null)
          Positioned(
            bottom: defaultPadding,
            left: defaultPadding,
            right: defaultPadding,
            child: DefaultButton(
              buttonColor: ThemeColors.primaryDark,
              text: "Contact us",
              onPressed: () {
                try {
                  launchUrl(Uri.parse("tel://${outletDetail.phone}"));
                } catch (e) {
                  print(e.toString());
                }
              },
            ),
          ),
        Positioned(
          right: 5,
          child: IconButton(
            icon: ImageIcon(
              AssetImage(
                  "assets/icons/${boxGlobal.get("iconSet")}/Standard_cancel.png"),
              color: widget.theme == "L" ? ThemeColors.dark : ThemeColors.light,
            ),
            onPressed: () {
              setState(() {
                onDetail = false;
              });
            },
          ),
        ),
      ],
    );
  }

  Future<Map?> getOutletSearch(String? searchValue) async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.outletListSearch(searchValue);

    if (data != null && data['data'] != null) {
      searchList = (data['data']['outlets'] as List)
          .map((data) => Outlet.fromJson(data))
          .toList();

      List<Marker> markers = [];
      for (Outlet each in searchList) {
        if (each.latitude != null && each.longitude != null) {
          markers.add(Marker(
              markerId: MarkerId("${each.latitude}, ${each.longitude}"),
              position: LatLng(
                  double.parse(each.latitude!), double.parse(each.longitude!)),
              icon: markerIcon ?? BitmapDescriptor.defaultMarker,
              onTap: () {
                panelController.open();
                setState(() {
                  onDetail = true;
                  outletDetail = each;
                });
              }));
        }
      }
      setState(() {
        searchMarkers.addAll(markers);
      });
    }

    return data;
  }

  openMap(String url, String fallBackUrl) async {
    try {
      bool launched = await launchUrl(Uri.parse(url));
      if (!launched) {
        await launchUrl(Uri.parse(fallBackUrl));
      }
    } catch (e) {
      defaultErrorDialog(context);
    }
  }
}
