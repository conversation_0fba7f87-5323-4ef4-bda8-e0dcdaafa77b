import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/credit_package.dart';
import 'package:amverton/Model/eghl_payment.dart';
import 'package:amverton/Model/payment_method.dart';
import 'package:amverton/Model/user.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/View/Credit/credit_history_page.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_dropdown1.dart';
import 'package:amverton/View/Custom%20Widgets/empty_list.dart';
import 'package:amverton/View/Home/web_view.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hive/hive.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class CreditTopUp extends StatefulWidget {
  const CreditTopUp({Key? key}) : super(key: key);

  static const androidPlatform = const MethodChannel('fnb.android');
  static const iosPlatform = const MethodChannel('fnb.ios');

  @override
  _CreditTopUpState createState() => _CreditTopUpState();
}

class _CreditTopUpState extends State<CreditTopUp> {
  late Future future;
  Box boxGlobal = Hive.box("boxGlobal");

  List<CreditPackage> creditPackageList = [];
  int? selectedCreditPackageId;
  List<PaymentMethod> paymentMethods = [];
  List<Brand> paymentBrands = [];
  PaymentMethod? selectedPaymentMethod;
  Brand? selectedPaymentBrand;

  // todo temp hardcode for obriens (ori dont hv background image)
  String creditPackageRoute = "assets/images/credit_package_";

  @override
  void initState() {
    super.initState();
    future = getCreditPackage();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("Credit Top Up")),
      body: FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            if (creditPackageList.isNotEmpty) {
              return Container(
                height: phoneHeight,
                child: Column(
                  children: [
                    Expanded(
                      flex: 4,
                      child: GridView.builder(
                        padding: EdgeInsets.fromLTRB(
                          defaultPadding,
                          defaultPadding,
                          defaultPadding,
                          verticalPaddingSmall,
                        ),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: spacingWidth,
                          mainAxisExtent: phoneHeight / 4.25,
                          mainAxisSpacing: 10,
                        ),
                        shrinkWrap: true,
                        itemCount: creditPackageList.length,
                        itemBuilder: (BuildContext ctx, index) {
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                selectedCreditPackageId =
                                    creditPackageList[index].id;
                              });
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(21),
                                  border: Border.all(
                                    color: selectedCreditPackageId ==
                                            creditPackageList[index].id
                                        ? ThemeColors.primaryDark
                                        : ThemeColors.disabled,
                                  )),
                              child: Column(
                                children: [
                                  Expanded(
                                    flex: 3,
                                    child: Container(
                                      padding:
                                          EdgeInsets.all(defaultInnerPadding),
                                      width: phoneWidth,
                                      decoration: BoxDecoration(
                                        color: ThemeColors.primaryLight,
                                        borderRadius: const BorderRadius.only(
                                          topLeft: Radius.circular(20),
                                          topRight: Radius.circular(20),
                                        ),
                                        // todo temp hardcode for obriens (ori dont hv background image)
                                        image: index < 3
                                            ? DecorationImage(
                                                image: AssetImage(
                                                    "$creditPackageRoute${index + 1}.png"),
                                                fit: BoxFit.cover)
                                            : null,
                                        //
                                      ),
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            creditPackageList[index].name,
                                            style: TextStyle(
                                                color: ThemeColors.dark,
                                                fontSize: h2,
                                                fontWeight: FontWeight.bold,
                                                height: 1.25),
                                            textAlign: TextAlign.center,
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          SizedBox(height: spacingHeightSmall),
                                          Text(
                                            "Credit ${important_variables.currency}${creditPackageList[index].credit}",
                                            style: TextStyle(
                                              color: ThemeColors.dark,
                                              fontSize: h4,
                                              fontWeight: FontWeight.bold,
                                            ),
                                            textAlign: TextAlign.center,
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: Container(
                                      width: phoneWidth,
                                      padding:
                                          EdgeInsets.all(defaultInnerPadding),
                                      decoration: BoxDecoration(
                                        color: ThemeColors.light,
                                        borderRadius: const BorderRadius.only(
                                          bottomLeft: Radius.circular(20),
                                          bottomRight: Radius.circular(20),
                                        ),
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            important_variables.currency +
                                                creditPackageList[index]
                                                    .discountedPrice,
                                            style: TextStyle(
                                              color: ThemeColors.primaryDark,
                                              fontSize: h3,
                                              fontWeight: FontWeight.bold,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                          Text(
                                            important_variables.currency +
                                                creditPackageList[index].price,
                                            style: TextStyle(
                                              color: ThemeColors.dark,
                                              fontSize: h4,
                                              decoration:
                                                  TextDecoration.lineThrough,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.all(defaultInnerPadding),
                        color: ThemeColors.light,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            DefaultDropdown1<PaymentMethod>(
                              borderRadius: 15,
                              verticalMargin: 0,
                              selectedValue: selectedPaymentMethod!,
                              items: paymentMethods.map((PaymentMethod value) {
                                return DropdownMenuItem<PaymentMethod>(
                                  value: value,
                                  child: Text(
                                    value.name,
                                    style: TextStyle(
                                      color: ThemeColors.dark,
                                      fontSize: h3,
                                    ),
                                  ),
                                );
                              }).toList(),
                              onChanged: (PaymentMethod? value) async {
                                setState(() {
                                  selectedPaymentMethod = value!;
                                  selectedPaymentBrand = null;
                                  paymentBrands.clear();
                                  if (selectedPaymentMethod!
                                      .brands.isNotEmpty) {
                                    paymentBrands =
                                        List.of(selectedPaymentMethod!.brands);
                                    if (paymentBrands.isNotEmpty) {
                                      selectedPaymentBrand =
                                          paymentBrands.first;
                                    }
                                  }
                                });
                              },
                            ),
                            if (paymentBrands.isNotEmpty)
                              DefaultDropdown1<Brand>(
                                borderRadius: 15,
                                verticalMargin: 0,
                                selectedValue: selectedPaymentBrand!,
                                items: paymentBrands.map((Brand paymentBrand) {
                                  return DropdownMenuItem<Brand>(
                                    value: paymentBrand,
                                    child: Text(
                                      paymentBrand.name,
                                      style: TextStyle(
                                        color: ThemeColors.dark,
                                        fontSize: h3,
                                      ),
                                    ),
                                  );
                                }).toList(),
                                onChanged: (Brand? value) async {
                                  setState(() {
                                    selectedPaymentBrand = value!;
                                  });
                                },
                              ),
                            DefaultButton(
                              text: "Check Out",
                              buttonColor:
                                  important_variables.projectName == "obriens"
                                      ? ThemeColors.secondaryDark
                                      : ThemeColors.primaryDark,
                              onPressed: () {
                                topUpCredit();
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            } else {
              return EmptyList(theme: "L");
            }
          }
          return Container();
        },
      ),
    );
  }

  void topUpCredit() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    Position? location = await _determinePosition();

    Map? data = await apiPost.topUpCredit(
        selectedCreditPackageId!,
        selectedPaymentMethod!.code,
        selectedPaymentBrand != null ? selectedPaymentBrand!.code : "",
        location);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        String? paymentUrl = data['data']['payment_url'] ?? null;
        Map? eghl_request = data['data']['eghl_request'];
        if (paymentUrl != null) {
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (BuildContext context) => WebView(
                  url: data['data']['payment_url'],
                ),
              )).then((value) async {
            await getMe().then((value) {
              Navigator.pop(context);
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (BuildContext context) =>
                          const CreditHistoryPage()));
            });
          });
        } else if (eghl_request != null) {
          EghlPayment eghl = EghlPayment.fromJson(data['data']['eghl_request']);

          if (Platform.isIOS) {
            try {
              await CreditTopUp.iosPlatform
                  .invokeMethod('eGHL', <String, dynamic>{
                'transactionType': eghl.transactionType ?? "",
                'paymentMethod': eghl.paymentMethod ?? "",
                'serviceId': eghl.serviceId ?? "",
                'merchantPass': eghl.merchantPass ?? "",
                'paymentId': eghl.paymentId ?? "",
                'orderNumber': eghl.orderNumber ?? "",
                'paymentDesc': eghl.paymentDesc ?? "",
                'merchantName': eghl.merchantName ?? "",
                'merchantCallBackUrl': eghl.merchantCallBackUrl ?? "",
                'amount': eghl.amount ?? "",
                'currencyCode': eghl.currencyCode ?? "",
                'custIp': eghl.custIp ?? "",
                'custName': eghl.custName ?? "",
                'custEmail': eghl.custEmail ?? "",
                'custPhone': eghl.custPhone ?? "",
                'languageCode': eghl.languageCode ?? "",
                'paymentGateway': eghl.paymentGateway ?? "",
              });
            } catch (e) {
              print(e);
            }
          } else {
            try {
              await CreditTopUp.androidPlatform
                  .invokeMethod('eGHL', <String, dynamic>{
                'transactionType': eghl.transactionType ?? "",
                'paymentMethod': eghl.paymentMethod ?? "",
                'serviceId': eghl.serviceId ?? "",
                'merchantPass': eghl.merchantPass ?? "",
                'paymentId': eghl.paymentId ?? "",
                'orderNumber': eghl.orderNumber ?? "",
                'paymentDesc': eghl.paymentDesc ?? "",
                'merchantName': eghl.merchantName ?? "",
                'merchantCallBackUrl': eghl.merchantCallBackUrl ?? "",
                'amount': eghl.amount ?? "",
                'currencyCode': eghl.currencyCode ?? "",
                'custIp': eghl.custIp ?? "",
                'custName': eghl.custName ?? "",
                'custEmail': eghl.custEmail ?? "",
                'custPhone': eghl.custPhone ?? "",
                'languageCode': eghl.languageCode ?? "",
                'paymentGateway': eghl.paymentGateway ?? "",
              });
            } catch (e) {
              print(e);
            }
          }
          await getMe().then((value) {
            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (BuildContext context) =>
                        const CreditHistoryPage())).then((value) {
              Navigator.of(context).pop();
            });
          });
        } else {
          await getMe().then((value) {
            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (BuildContext context) =>
                        const CreditHistoryPage())).then((value) {
              Navigator.of(context).pop();
            });
          });
        }
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  Future<Map?> getCreditPackage() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.creditPackage();

    if (data != null && data['data'] != null) {
      creditPackageList = (data['data']['packs'] as List)
          .map((data) => CreditPackage.fromJson(data))
          .toList();

      paymentMethods = (data['data']['payment_methods'] as List)
          .map((data) => PaymentMethod.fromJson(data))
          .toList();

      if (creditPackageList.isNotEmpty)
        selectedCreditPackageId = creditPackageList.first.id;

      if (paymentMethods.isNotEmpty) {
        selectedPaymentMethod = paymentMethods.first;
        if (selectedPaymentMethod!.brands.isNotEmpty) {
          paymentBrands = List.of(selectedPaymentMethod!.brands);
          if (paymentBrands.isNotEmpty) {
            selectedPaymentBrand = paymentBrands.first;
          }
        }
      }
    }
    return data;
  }

  Future<Map?> getMe() async {
    EasyLoading.show();
    Box box = await Hive.openBox("box");
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.me();
    EasyLoading.dismiss();

    if (data != null && data['data'] != null) {
      User user = User.fromJson(data['data']['me']);
      await box.put("user", user);

      String barcode = data['data']['barcode'];
      await box.put("barcode", barcode);

      int totalDaysCheckIn =
          data['data']['check_in']?['total_days_checked_in'] ?? 0;
      box.put("total_days_checked_in", totalDaysCheckIn);
    }
    return data;
  }

  Future<Position?> _determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return null;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return null;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return null;
    }

    return await Geolocator.getCurrentPosition();
  }
}
