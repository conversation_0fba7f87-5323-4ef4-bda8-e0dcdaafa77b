import 'dart:async';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/credit_history.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Custom%20Widgets/empty_list.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class CreditHistoryPage extends StatefulWidget {
  const CreditHistoryPage({Key? key}) : super(key: key);

  @override
  _CreditHistoryPageState createState() => _CreditHistoryPageState();
}

class _CreditHistoryPageState extends State<CreditHistoryPage> {
  Box boxGlobal = Hive.box("boxGlobal");

  final ScrollController scrollController = ScrollController();
  bool hasMorePages = false;
  int nextPage = 0;
  StreamController streamController = StreamController();
  List<CreditHistory> creditHistoryList = [];

  @override
  void initState() {
    super.initState();
    getCreditHistory();

    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (hasMorePages) getCreditHistory();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeColors.light,
      appBar: AppBar(title: Text("Credit Top Up History")),
      body: StreamBuilder(
          stream: streamController.stream,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            if (snapshot.hasError) {
              return Container();
            } else {
              if (creditHistoryList.isNotEmpty) {
                return ListView.builder(
                  padding: EdgeInsets.all(defaultPadding),
                  itemCount: creditHistoryList.length,
                  itemBuilder: (BuildContext context, int index) {
                    return Container(
                      margin: EdgeInsets.only(bottom: defaultInnerPadding),
                      padding: EdgeInsets.symmetric(
                        vertical: defaultPadding,
                        horizontal: defaultInnerPadding,
                      ),
                      width: phoneWidth,
                      decoration: BoxDecoration(
                        color: ThemeColors.light,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: ThemeColors.disabled),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  "Credit reload",
                                  style: TextStyle(
                                    color: ThemeColors.dark,
                                    fontSize: h3,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              SizedBox(width: spacingWidth),
                              Text(
                                DateFormat('d MMM yyyy HH:mm')
                                    .format(creditHistoryList[index].createdAt),
                                style: TextStyle(
                                  color: ThemeColors.gray,
                                  fontSize: h4,
                                ),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  "Credit:",
                                  style: TextStyle(
                                    color: ThemeColors.gray,
                                    fontSize: h3,
                                  ),
                                ),
                              ),
                              SizedBox(width: spacingWidth),
                              Text(
                                important_variables.currency +
                                    creditHistoryList[index].credit,
                                style: TextStyle(
                                  color: ThemeColors.dark,
                                  fontSize: h4,
                                ),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  "Top Up:",
                                  style: TextStyle(
                                    color: ThemeColors.gray,
                                    fontSize: h3,
                                  ),
                                ),
                              ),
                              SizedBox(width: spacingWidth),
                              Text(
                                important_variables.currency +
                                    creditHistoryList[index].total,
                                style: TextStyle(
                                  color: ThemeColors.dark,
                                  fontSize: h4,
                                ),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  "Payment Status:",
                                  style: TextStyle(
                                    color: ThemeColors.gray,
                                    fontSize: h3,
                                  ),
                                ),
                              ),
                              SizedBox(width: spacingWidth),
                              Text(
                                creditHistoryList[index].paymentStatus,
                                style: TextStyle(
                                  color: ThemeColors.dark,
                                  fontSize: h4,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                );
              } else {
                return EmptyList(theme: "L");
              }
            }
          }),
    );
  }

  Future<Map?> getCreditHistory() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.creditHistory(nextPage);

    if (data != null && data['data'] != null) {
      List<CreditHistory> newList = (data['data']['credits'] as List)
          .map((data) => CreditHistory.fromJson(data))
          .toList();

      hasMorePages = data["data"]["has_more_pages"];
      if (hasMorePages) {
        nextPage = data["data"]["next_page"] ?? 0;
      }
      creditHistoryList.addAll(newList);
      streamController.add(data);
    }
    return data;
  }
}
