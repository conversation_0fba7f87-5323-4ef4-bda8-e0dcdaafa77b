import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/cart/brand.dart';
import 'package:amverton/Model/cart/payment_method.dart';
import 'package:hive/hive.dart';

class PaymentOption extends StatefulWidget {
  final List<PaymentMethod> paymentMethods;
  PaymentMethod? selectedPaymentMethod;
  Brand? selectedPaymentBrand;

  PaymentOption({
    Key? key,
    required this.paymentMethods,
    required this.selectedPaymentMethod,
    required this.selectedPaymentBrand,
  }) : super(key: key);

  @override
  _PaymentOptionState createState() => _PaymentOptionState();
}

class _PaymentOptionState extends State<PaymentOption> {
  Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeColors.light,
      appBar: AppBar(title: const Text("Payment Method")),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(defaultPadding),
            child: Text(
              'Payment Method',
              style: TextStyle(
                fontSize: h2,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: widget.paymentMethods.length,
              itemBuilder: (BuildContext context, int index) {
                PaymentMethod paymentMethod = widget.paymentMethods[index];
                return buildPaymentOption(
                    name: paymentMethod.name,
                    isSelected: widget.selectedPaymentMethod == paymentMethod,
                    onTap: () {
                      widget.selectedPaymentMethod =
                          widget.paymentMethods[index];
                      widget.selectedPaymentBrand = null;
                      if (paymentMethod.brands.length > 0) {
                        showBottomSheet(context, paymentMethod);
                      } else {
                        Navigator.pop(context, [
                          widget.selectedPaymentMethod,
                          widget.selectedPaymentBrand
                        ]);
                      }
                    });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget buildPaymentOption({
    required String name,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? ThemeColors.gray.withOpacity(0.2)
              : Colors.transparent,
        ),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.symmetric(vertical: 8),
              child: Row(
                children: [
                  Radio<String>(
                    value: name,
                    groupValue: widget.selectedPaymentMethod != null
                        ? widget.selectedPaymentMethod!.name
                        : null,
                    onChanged: (String? value) {
                      onTap();
                    },
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          name,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: isSelected ? Colors.black : ThemeColors.gray,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Container(height: 1, color: ThemeColors.gray.withOpacity(0.2)),
          ],
        ),
      ),
    );
  }

  void showBottomSheet(BuildContext context, PaymentMethod paymentMethod) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.0)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.all(defaultPadding),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20.0)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Center(
                child: Container(
                  width: 50,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(10.0),
                  ),
                  margin: EdgeInsets.only(bottom: 16.0),
                ),
              ),
              Flexible(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: paymentMethod.brands.length,
                  itemBuilder: (BuildContext context, int index) {
                    final brand = paymentMethod.brands[index];
                    return ListTile(
                      leading: Icon(Icons.account_balance, color: Colors.blue),
                      title: Text(brand.name,
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      subtitle: Text(""),
                      onTap: () {
                        widget.selectedPaymentBrand = brand;
                        Navigator.of(context).pop();
                        Navigator.pop(context, [
                          widget.selectedPaymentMethod,
                          widget.selectedPaymentBrand
                        ]);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
