import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/cart.dart';
import 'package:amverton/Model/cart/cart_item.dart';
import 'package:amverton/View/Cart/cart_page.dart';
import 'package:hive/hive.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class ProductTemplate1 extends StatefulWidget {
  // final Cart cartItem;
  final CartItem cartItem;
  final int itemIndex;

  const ProductTemplate1({
    Key? key,
    required this.cartItem,
    required this.itemIndex,
  }) : super(key: key);

  @override
  State<ProductTemplate1> createState() => _ProductTemplate1State();
}

class _ProductTemplate1State extends State<ProductTemplate1> {
  Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Container(
      width: phoneWidth,
      margin: EdgeInsets.only(bottom: spacingHeightSmall),
      padding: EdgeInsets.all(defaultInnerPadding),
      decoration: BoxDecoration(
        color: ThemeColors.light,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.cartItem.product.name,
                  style: TextStyle(
                    color: ThemeColors.dark,
                    fontSize: h3,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                // selected bundle //
                if (widget.cartItem.product.type == "BUNDLE")
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: List.generate(
                        widget.cartItem.product.bundleCategory!.length,
                        (categoryIndex) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: List.generate(
                            widget
                                .cartItem
                                .product
                                .bundleCategory![categoryIndex]
                                .bundleItems
                                .length, (itemIndex) {
                          if (widget
                                  .cartItem
                                  .product
                                  .bundleCategory![categoryIndex]
                                  .bundleItems[itemIndex]
                                  .isSelected ==
                              1) {
                            return Text(
                              widget
                                  .cartItem
                                  .product
                                  .bundleCategory![categoryIndex]
                                  .bundleItems[itemIndex]
                                  .product
                                  .name,
                              style: TextStyle(
                                  color: ThemeColors.dark, fontSize: h4),
                            );
                          }
                          return SizedBox();
                        }),
                      );
                    }),
                  ),
                // selected modifier //
                if (widget.cartItem.product.modifierCategory!.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: List.generate(
                        widget.cartItem.product.modifierCategory!.length,
                        (categoryIndex) {
                      return Column(
                        children: List.generate(
                            widget
                                .cartItem
                                .product
                                .modifierCategory![categoryIndex]
                                .modifierItems
                                .length, (itemIndex) {
                          if (widget
                                  .cartItem
                                  .product
                                  .modifierCategory![categoryIndex]
                                  .modifierItems[itemIndex]
                                  .isSelected ==
                              1) {
                            return Text(
                              widget
                                  .cartItem
                                  .product
                                  .modifierCategory![categoryIndex]
                                  .modifierItems[itemIndex]
                                  .name,
                              style: TextStyle(
                                  color: ThemeColors.dark, fontSize: h4),
                            );
                          }
                          return SizedBox();
                        }),
                      );
                    }),
                  ),
                if (widget.cartItem.remark != null)
                  Text(
                    widget.cartItem.remark!,
                    style: TextStyle(color: ThemeColors.gray, fontSize: h4),
                  ),
                SizedBox(height: spacingHeightMedium),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      important_variables.currency + widget.cartItem.price,
                      style: TextStyle(
                        color: ThemeColors.secondaryDark,
                        fontSize: h3,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.right,
                    ),
                    Row(
                      children: [
                        GestureDetector(
                          onTap: () {
                            if (widget.cartItem.quantity > 1) {
                              // CartPage.updateCart(
                              //     context,
                              //     widget.cartItem.id,
                              //     widget.cartItem.quantity - 1,
                              //     widget.cartItem.remark,
                              //     selectedVoucherIndex == ""
                              //         ? null
                              //         : voucherList[
                              //                 int.parse(selectedVoucherIndex)]
                              //             .pivot!
                              //             .voucherId,
                              //     widget.cartItem.product.bundleCategory,
                              //     widget.cartItem.product.modifierCategory);
                            }
                          },
                          child: ImageIcon(
                            AssetImage(
                                "assets/icons/${boxGlobal.get("iconSet")}/General_Minus.png"),
                            color: widget.cartItem.quantity > 1
                                ? ThemeColors.primaryDark
                                : ThemeColors.disabled,
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: spacingHeightMedium),
                          child: Text(
                            widget.cartItem.quantity.toString(),
                            style: TextStyle(
                                color: ThemeColors.dark, fontSize: h3),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            // CartPage.updateCart(
                            //     context,
                            //     widget.cartItem.id,
                            //     widget.cartItem.quantity + 1,
                            //     widget.cartItem.remark,
                            //     selectedVoucherIndex == ""
                            //         ? null
                            //         : voucherList[
                            //                 int.parse(selectedVoucherIndex)]
                            //             .pivot!
                            //             .voucherId,
                            //     widget.cartItem.product.bundleCategory,
                            //     widget.cartItem.product.modifierCategory);
                          },
                          child: ImageIcon(
                            AssetImage(
                                "assets/icons/${boxGlobal.get("iconSet")}/General_Plus.png"),
                            color: ThemeColors.primaryDark,
                          ),
                        ),
                      ],
                    )
                  ],
                ),
                GestureDetector(
                  onTap: () {
                    // CartPage.editItem(context, widget.itemIndex);
                  },
                  child: Text(
                    "Edit",
                    style: TextStyle(
                      color: ThemeColors.secondaryDark,
                      fontSize: h4,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
