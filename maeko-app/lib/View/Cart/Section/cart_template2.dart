import 'dart:convert';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/address.dart';
import 'package:amverton/Model/bottom_bar.dart';
import 'package:amverton/Model/cart/brand.dart';
import 'package:amverton/Model/cart/cart.dart';
import 'package:amverton/Model/cart/cart_page_variable.dart';
import 'package:amverton/Model/cart/payment_method.dart';
import 'package:amverton/Model/cart/recommended_product.dart';
import 'package:amverton/Model/product.dart';
import 'package:amverton/Notifier/notifiers.dart';
import 'package:amverton/Provider/providers.dart';
import 'package:amverton/Repository/api.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Cart/Section/addon_template1.dart';
import 'package:amverton/View/Cart/Section/product_template1.dart';
import 'package:amverton/View/Cart/Section/product_template2.dart';
import 'package:amverton/View/Cart/payment_option.dart';
import 'package:amverton/View/Cart/voucher_option.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_dropdown1.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield3.dart';
import 'package:amverton/View/OrderMenu/deliver_address_option.dart';
import 'package:amverton/main.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class CartTemplate2 extends ConsumerStatefulWidget {
  final Function editItem;
  final Function getCart;
  final Function updateCart;
  final Function deleteCart;
  final Function makeOrder;
  final Function onRefresh;

  const CartTemplate2({
    super.key,
    required this.editItem,
    required this.getCart,
    required this.updateCart,
    required this.deleteCart,
    required this.makeOrder,
    required this.onRefresh,
  });

  @override
  _CartTemplate2State createState() => _CartTemplate2State();
}

class _CartTemplate2State extends ConsumerState<CartTemplate2> {
  late Box box;
  bool isLogin = false;
  late Box boxMenu;
  late Box boxGlobal;
  late Future boxFuture;
  Cart? cart;
  CartPageVariable? cartPageVariable;
  bool usePoint = true;
  String? pointUsable;

  TextEditingController remarkController = TextEditingController();

  List<Brand> paymentBrands = [];

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();
  }

  openBox() async {
    CartPageVariable? cartPageVariable = ref.read(cartPageVariableProvider);
    box = Hive.box('box');
    isLogin = box.get("is_login") ?? false;
    boxMenu = Hive.box('boxMenu');
    boxGlobal = Hive.box('boxGlobal');
    paymentBrands = List.of(cartPageVariable!.paymentBrands);
  }

  @override
  Widget build(BuildContext context) {
    cart = ref.watch(cartProvider);
    cartPageVariable = ref.watch(cartPageVariableProvider);

    //make the point no decimal
    pointUsable = cart!.checkoutWith != "POINT"
        ? pointUsable
        : double.parse(cart!.pointUsable).truncate().toString();

    if (usePoint && cart!.checkoutWith == "POINT") {
      Decimal totalRemain =
          (Decimal.parse(cart!.total) - Decimal.parse('${pointUsable}'));

      if (totalRemain <= Decimal.parse("5") && totalRemain != Decimal.zero) {
        pointUsable = (Decimal.parse('${pointUsable}') -
                (Decimal.fromInt(5) - totalRemain))
            .toString();
        pointUsable = double.parse('${pointUsable}').truncate().toString();

        WidgetsBinding.instance.addPostFrameCallback((_) {
          defaultDialog(
                  context,
                  400,
                  "The minimum payment is 5, the total coins available is adjusted.",
                  () {})
              .show();
        });
      }
    }

    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            return isLogin ? contents() : guestView();
          }
          return Container();
        });
  }

  Widget contents() {
    if (cart!.items.length == 0) {
      return emptyList();
    } else {
      return SizedBox(
        height: phoneHeight,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 7,
              child: Container(
                padding: EdgeInsets.only(
                  left: defaultPadding,
                  right: defaultPadding,
                  top: defaultPadding,
                ),
                child: RefreshIndicator(
                    color: ThemeColors.primaryDark,
                    onRefresh: () {
                      return Future.delayed(const Duration(seconds: 1), () {
                        setState(() {
                          widget.getCart();
                        });
                      });
                    },
                    child: LayoutBuilder(builder: (context, constraint) {
                      return SingleChildScrollView(
                        padding: EdgeInsets.only(bottom: defaultInnerPadding),
                        physics: const AlwaysScrollableScrollPhysics(),
                        child: ConstrainedBox(
                          constraints:
                              BoxConstraints(minHeight: constraint.maxHeight),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              header(),
                              cartItemWidget(),
                              addOnWidget(),
                              SizedBox(
                                height: 8,
                              ),
                              if (cart!.freeItem != null) freeItemWidget(),
                              DefaultTextField3(
                                label: "Special Remark",
                                controller: remarkController,
                                hintText: "Let us know if you need anything",
                              ),
                              summary(),
                            ],
                          ),
                        ),
                      );
                    })),
              ),
            ),
            Expanded(
              flex: 1,
              child: total(),
            ),
          ],
        ),
      );
    }
  }

  Widget header() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // outlet
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "${cartPageVariable!.selectedDeliverMethod == 1 ? "Pick Up" : "Selected"} At",
              style: TextStyle(
                color: ThemeColors.dark,
                fontSize: h3,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: spacingHeightSmall),
            Text(
              cart!.outlet.name,
              style: TextStyle(color: ThemeColors.dark, fontSize: h3),
            ),
            Text(
              cart!.outlet.address,
              style: TextStyle(
                color: ThemeColors.gray,
                fontSize: h5,
              ),
            ),
          ],
        ),
        // deliver address
        if (cartPageVariable!.selectedDeliverMethod == 0)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: spacingHeightMedium),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Delivery to",
                    style: TextStyle(
                      color: ThemeColors.dark,
                      fontSize: h3,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  GestureDetector(
                    onTap: () async {
                      if (cartPageVariable!.selectedDeliverMethod == 0 &&
                          cartPageVariable!.selectedAddressId == 0) {
                        int index = registerAsset.indexWhere(
                            (element) => element.sectionName == "STEP_1");
                        // element.sectionName == "STEP_3");
                        if (index != -1)
                          Navigator.pushNamed(context,
                                  "/add_address/${registerAsset[index].template}")
                              .then((_) async {
                            await widget.onRefresh();
                          });
                      } else {
                        final result = await Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => DeliverAddressOption(
                                selectedAddressId:
                                    cartPageVariable!.selectedAddressId,
                                updateCartDeliverAddress: true,
                              ),
                            ));

                        setState(() {
                          if (result != null)
                            cartPageVariable!.selectedAddressId = result;
                          widget.getCart();
                        });
                      }
                    },
                    child: Text(
                      cartPageVariable!.selectedDeliverMethod == 0 &&
                              cartPageVariable!.selectedAddressId == 0
                          ? "Add"
                          : "Change",
                      style: TextStyle(
                        color: ThemeColors.secondaryDark,
                        fontSize: h4,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: spacingHeightSmall),
              Text(
                () {
                  for (Address each in cartPageVariable!.addressList) {
                    if (cartPageVariable!.selectedAddressId == each.id)
                      return "${each.name}\n${each.countryCode}${each.phone}\n${each.address1}, ${each.address2 ?? ""}\n${each.postcode} ${each.city}, ${each.state?.name ?? ""}";
                  }
                  return "You don't have a delivery address yet.";
                }(),
                style: TextStyle(
                    color: cartPageVariable!.selectedAddressId == 0
                        ? ThemeColors.secondaryDark
                        : ThemeColors.dark,
                    fontSize: h4),
              ),
            ],
          ),
        // delivery time
        if (cartPageVariable!.schedules.length > 2)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: spacingHeightMedium),
              Text(
                "Schedule",
                style: TextStyle(
                  color: ThemeColors.dark,
                  fontSize: h3,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: spacingHeightSmall),
              DefaultDropdown1(
                verticalMargin: 0,
                borderRadius: 20,
                selectedValue: cartPageVariable!.selectedSchedule!,
                items: cartPageVariable!.schedules.map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(
                      value,
                      style: TextStyle(
                        color: ThemeColors.dark,
                        fontSize: h3,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (String? value) async {
                  setState(() {
                    cartPageVariable!.selectedSchedule = value!;
                  });
                  boxMenu.put(
                      "selected_schedule", cartPageVariable!.selectedSchedule);
                },
              ),
            ],
          ),
        SizedBox(height: defaultInnerPadding),
      ],
    );
  }

  Widget cartItemWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Items you order: ",
          style: TextStyle(
            color: ThemeColors.dark,
            fontSize: h3,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: spacingHeightSmall),
        ListView.builder(
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          itemCount: cart!.items.length,
          itemBuilder: (BuildContext context, int index) {
            return Dismissible(
              key: Key(cart!.items[index].id.toString()),
              direction: DismissDirection.endToStart,
              background: Container(
                  padding: const EdgeInsets.only(right: 30),
                  alignment: Alignment.centerRight,
                  child: ImageIcon(
                    AssetImage(
                        "assets/icons/${boxGlobal.get("iconSet")}/trash-02.png"),
                    color: Colors.red,
                  )),
              confirmDismiss: (DismissDirection direction) async {
                return await AwesomeDialog(
                  context: context,
                  dialogType: DialogType.warning,
                  animType: AnimType.bottomSlide,
                  dismissOnTouchOutside: false,
                  dialogBackgroundColor: Colors.white,
                  autoDismiss: false,
                  onDismissCallback: (DismissType dismissType) {},
                  padding: EdgeInsets.all(defaultPadding),
                  body: Center(
                    child: Text(
                      "Are you sure you want to delete this item?",
                      style: const TextStyle(fontStyle: FontStyle.italic),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  // Yes
                  btnCancel: GestureDetector(
                    onTap: () => Navigator.of(context).pop(true),
                    child: Container(
                      alignment: Alignment.center,
                      height: buttonHeight,
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        "Yes",
                        style: TextStyle(
                          color: ThemeColors.light,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  // Cancel
                  btnOk: GestureDetector(
                    onTap: () => Navigator.of(context).pop(false),
                    child: Container(
                      alignment: Alignment.center,
                      height: buttonHeight,
                      decoration: BoxDecoration(
                        color: ThemeColors.light,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        "Cancel",
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ).show();
              },
              onDismissed: (direction) {
                widget.deleteCart(
                    cart!.items[index].id,
                    cartPageVariable!.selectedVoucherIndex == ""
                        ? null
                        : cart!
                            .vouchers[int.parse(
                                cartPageVariable!.selectedVoucherIndex)]
                            .id);
              },
              // todo temp hardcode for obriens (they say wanna make template for cart item list, but dk will proceed or nt)
              child: important_variables.projectName == "sbk"
                  ? ProductTemplate1(
                      cartItem: cart!.items[index],
                      itemIndex: index,
                    )
                  : ProductTemplate2(
                      editItem: widget.editItem,
                      updateCart: widget.updateCart,
                      deleteCart: widget.deleteCart,
                      cartItem: cart!.items[index],
                      itemIndex: index,
                      cartPageVariable: cartPageVariable,
                      voucherList: cart!.vouchers,
                    ),
            );
          },
        ),
      ],
    );
  }

  Widget addOnWidget() {
    if (cart!.recommendedProducts.length > 0)
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Add ons ",
            style: TextStyle(
              color: ThemeColors.dark,
              fontSize: h3,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: spacingHeightSmall),
          Container(
            height: 170,
            child: ListView.builder(
              padding: EdgeInsets.zero,
              scrollDirection: Axis.horizontal,
              shrinkWrap: true,
              itemCount: cart!.recommendedProducts.length,
              itemBuilder: (BuildContext context, int index) {
                RecommendedProduct recommendedProduct =
                    cart!.recommendedProducts[index];
                return AddOnTemplate1(
                  recommededProduct: recommendedProduct,
                  tag: "Cart/recommended/${recommendedProduct.id}",
                  outlet: cart!.outlet.toGlobalOutlet(),
                  getCart: widget.getCart,
                );
              },
            ),
          ),
        ],
      );
    return SizedBox();
  }

  Widget freeItemWidget() {
    return Container(
        padding: EdgeInsets.only(top: defaultInnerPadding),
        decoration: BoxDecoration(
            border: Border(top: BorderSide(color: ThemeColors.disabled))),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: phoneWidth,
              padding: EdgeInsets.symmetric(horizontal: defaultInnerPadding),
              margin: EdgeInsets.only(bottom: spacingHeightMedium),
              decoration: BoxDecoration(
                color: ThemeColors.light,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    cart!.freeItem?.name ?? "",
                    style: TextStyle(color: ThemeColors.dark, fontSize: h3),
                  ),
                  SizedBox(height: spacingHeightMedium),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "${important_variables.currency}0.00",
                        style: TextStyle(
                          color: ThemeColors.secondaryDark,
                          fontSize: h3,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.right,
                      ),
                      Text(
                        "Free Item",
                        style: TextStyle(
                          color: ThemeColors.dark,
                          fontSize: h3,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ],
        ));
  }

  Widget summary() {
    return Container(
      padding: EdgeInsets.only(top: defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          //voucher
          if (cart!.vouchers.isNotEmpty) ...[
            Container(
              margin: EdgeInsets.only(bottom: defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: EdgeInsets.only(bottom: spacingHeightSmall),
                    child: Text(
                      'Voucher',
                      style: TextStyle(
                        fontSize: h3,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () async {
                      final result = await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => VoucherOption(
                              voucherList: cart!.vouchers,
                              selectedVoucherIndex:
                                  cartPageVariable!.selectedVoucherIndex,
                            ),
                          ));
                      if (result != null) {
                        setState(() {
                          cartPageVariable!.selectedVoucherIndex = result[0];
                        });
                        widget.updateCart(
                            null,
                            null,
                            null,
                            cart!
                                .vouchers[int.parse(
                                    cartPageVariable!.selectedVoucherIndex)]
                                .id,
                            null,
                            null);
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.all(defaultInnerPadding),
                      width: phoneWidth,
                      height: buttonHeight,
                      alignment: Alignment.centerLeft,
                      decoration: BoxDecoration(
                        color: ThemeColors.light,
                        border: Border.all(color: ThemeColors.disabled),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                AutoSizeText(
                                  cartPageVariable!.selectedVoucherIndex == ""
                                      ? "Select a voucher"
                                      : cart!
                                          .vouchers[int.parse(cartPageVariable!
                                              .selectedVoucherIndex)]
                                          .title,
                                  style: TextStyle(
                                    color: cartPageVariable!
                                                .selectedVoucherIndex ==
                                            ""
                                        ? Colors.black
                                        : ThemeColors.dark,
                                  ),
                                  minFontSize: h5,
                                  maxFontSize: h4,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                if (cartPageVariable!.selectedVoucherIndex ==
                                    "") ...[
                                  Icon(
                                    Icons.arrow_forward_ios,
                                    color: Colors.black,
                                  ),
                                ]
                              ],
                            ),
                          ),
                          if (cartPageVariable!.selectedVoucherIndex != "")
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  cartPageVariable!.selectedVoucherIndex = "";
                                });
                                widget.getCart();
                              },
                              child: Text(
                                "Remove",
                                style: TextStyle(
                                  color: Colors.red,
                                  fontSize: h5,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          //Payment Method
          Container(
            margin: EdgeInsets.only(bottom: defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(bottom: spacingHeightSmall),
                  child: Text(
                    'Payment Method',
                    style: TextStyle(
                      fontSize: h3,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () async {
                    final result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => PaymentOption(
                            paymentMethods: cart!.paymentMethods,
                            selectedPaymentMethod:
                                cartPageVariable!.selectedPaymentMethod,
                            selectedPaymentBrand:
                                cartPageVariable!.selectedPaymentBrand,
                          ),
                        ));
                    if (result != null) {
                      setState(() {
                        cartPageVariable!.selectedPaymentMethod = result[0];
                        cartPageVariable!.selectedPaymentBrand = result[1];
                      });
                    }
                  },
                  child: Container(
                    padding: EdgeInsets.all(defaultInnerPadding),
                    width: phoneWidth,
                    height: buttonHeight,
                    alignment: Alignment.centerLeft,
                    decoration: BoxDecoration(
                      color: ThemeColors.light,
                      border: Border.all(color: ThemeColors.disabled),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              AutoSizeText(
                                cartPageVariable!.selectedPaymentMethod == null
                                    ? "Choose A Payment Method"
                                    : cartPageVariable!
                                            .selectedPaymentMethod!.name +
                                        (cartPageVariable!
                                                    .selectedPaymentBrand !=
                                                null
                                            ? "- ${cartPageVariable!.selectedPaymentBrand!.name}"
                                            : ""),
                                style: TextStyle(
                                  color:
                                      cartPageVariable!.selectedPaymentMethod ==
                                              null
                                          ? Colors.black
                                          : ThemeColors.dark,
                                ),
                                minFontSize: h5,
                                maxFontSize: h4,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Icon(
                                Icons.arrow_forward_ios,
                                color: Colors.black,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          //points
          Row(
            children: [
              Text(
                'Redeem ${pointUsable} OB Coins',
                style: TextStyle(
                  fontSize: h4,
                ),
              ),
              Spacer(),
              Text(
                '-RM ${pointUsable}',
                style: TextStyle(
                    color: Colors.red,
                    fontSize: h4,
                    fontWeight: FontWeight.bold),
              ),
              Switch(
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                value: usePoint,
                onChanged: (value) {
                  setState(() {
                    usePoint = value;
                  });
                },
                activeColor: Colors.green,
              ),
            ],
          ),
          Container(
            margin: EdgeInsets.only(bottom: 8),
            height: 1,
            color: ThemeColors.disabled,
          ),
          Container(
            margin: EdgeInsets.only(bottom: 8),
            child: Text(
              'Payment Details',
              style: TextStyle(
                fontSize: h3,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                Text(
                  "Subtotal",
                  style: TextStyle(color: ThemeColors.gray, fontSize: h3),
                ),
                const Spacer(),
                SizedBox(
                  width: phoneWidth / 1.8,
                  child: Text(
                    important_variables.currency + cart!.subtotal,
                    style: TextStyle(
                      color: ThemeColors.dark,
                      fontSize: h2,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ),
          ),
          summaryItem(
            "Service Tax (10%)",
            important_variables.currency + cart!.serviceTax,
          ),
          summaryItem(
            "SST (6%)",
            important_variables.currency + cart!.tax,
          ),
          Container(
            margin: EdgeInsets.only(bottom: 8),
            height: 1,
            color: ThemeColors.disabled,
          ),
          summaryItem(
            "Discount",
            "-${important_variables.currency}${cart!.discount}",
          ),
          summaryItem(
            "Delivery Fee",
            cartPageVariable!.selectedDeliverMethod == 0
                ? 'RM 2.00'
                : 'RM 0.00',
          ),
          summaryItem(
            "OB Coins Redeemed",
            usePoint ? '-RM ${pointUsable}' : '-RM 0.00',
          ),
          Container(
            margin: EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                Text(
                  "Total",
                  style: TextStyle(
                      color: ThemeColors.secondaryDark,
                      fontSize: h1,
                      fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Text(
                  "${important_variables.currency}${usePoint ? (Decimal.parse(cart!.total) - Decimal.parse('${pointUsable}')) : cart!.total}",
                  style: TextStyle(
                      color: ThemeColors.secondaryDark,
                      fontSize: h1,
                      fontWeight: FontWeight.bold),
                  textAlign: TextAlign.end,
                ),
              ],
            ),
          ),
          summaryItem("OB Coins Earned", "${cart!.pointEarned} Coins"),
        ],
      ),
    );
  }

  Widget summaryItem(String title, String pricing,
      {Color? color, double? fontSize}) {
    color = color ?? ThemeColors.gray;
    fontSize = fontSize ?? h3;
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text(
            title,
            style: TextStyle(color: color, fontSize: fontSize),
          ),
          const Spacer(),
          Text(
            pricing,
            style: TextStyle(color: color, fontSize: fontSize),
            textAlign: TextAlign.end,
          ),
        ],
      ),
    );
  }

  Widget total() {
    return Container(
      // color: ThemeColors.light,
      padding: EdgeInsets.only(left: defaultPadding, right: defaultPadding),
      decoration: BoxDecoration(
        color: ThemeColors.light,
        border: Border.all(
          color: ThemeColors.disabled,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Grand Total",
                    style: TextStyle(
                      fontSize: h5,
                      fontWeight: FontWeight.bold,
                      color: ThemeColors.secondaryDark,
                    ),
                  ),
                  Text(
                    "${important_variables.currency}${usePoint ? (Decimal.parse(cart!.total) - Decimal.parse('${pointUsable}')) : cart!.total}",
                    style: TextStyle(
                      fontSize: h1,
                      fontWeight: FontWeight.bold,
                      color: ThemeColors.secondaryDark,
                    ),
                  ),
                ],
              ),
              Spacer(),
              Container(
                width: phoneWidth * 0.4,
                child: DefaultButton(
                  text: "Check Out",
                  height: phoneHeight * 0.05,
                  buttonColor: ThemeColors.secondaryDark,
                  onPressed: () {
                    if (cartPageVariable!.selectedDeliverMethod == 0 &&
                        cartPageVariable!.selectedAddressId == 0) {
                      defaultWarningDialog(
                              context,
                              "Please select a delivery address to continue.",
                              () {})
                          .show();
                    } else if (cartPageVariable!.selectedPaymentMethod ==
                        null) {
                      defaultWarningDialog(
                              context, "Please Select Payment Method.", () {})
                          .show();
                    } else {
                      widget.makeOrder(
                          usePoint ? pointUsable : "0", remarkController.text);
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget emptyList() {
    return RefreshIndicator(
      color: ThemeColors.primaryDark,
      onRefresh: () {
        return Future.delayed(const Duration(seconds: 1), () {
          setState(() {
            widget.getCart();
          });
        });
      },
      child: SizedBox(
          width: phoneWidth,
          height: phoneHeight,
          child: SingleChildScrollView(
            physics: AlwaysScrollableScrollPhysics(),
            child: Column(
              children: [
                SizedBox(height: phoneHeight / 4),
                Image.asset(
                    "assets/icons/${boxGlobal.get("iconSet")}/Cart 01.png"),
                SizedBox(height: spacingHeightLarge),
                Text(
                  "No items found",
                  style: TextStyle(
                    color: ThemeColors.dark,
                    fontSize: h2,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: spacingHeightSmall),
                Text(
                  "Look like you have not added\nanything to your cart.",
                  textAlign: TextAlign.center,
                  style: TextStyle(color: ThemeColors.dark, fontSize: h3),
                ),
                SizedBox(height: spacingHeightLarge),
                SizedBox(
                  width: phoneWidth / 2,
                  child: DefaultButton(
                    text: "Browse",
                    buttonColor: important_variables.projectName == "obriens"
                        ? ThemeColors.secondaryDark
                        : ThemeColors.primaryDark,
                    onPressed: () {
                      List<BottomBar> bottomBarList =
                          boxGlobal.get("bottomBar") ?? [];
                      if (bottomBarList.isNotEmpty) {
                        int index = bottomBarList
                            .indexWhere((element) => element.name == "ORDER");
                        if (index != -1) {
                          final dynamic navigationBar =
                              barGlobalKey.currentWidget;
                          navigationBar.onTap(index);
                        }
                      }
                    },
                  ),
                ),
              ],
            ),
          )),
    );
  }

  Widget guestView() {
    return SizedBox(
      width: phoneWidth,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.warning_amber_outlined,
            color: Colors.red,
            size: 60,
          ),
          SizedBox(height: spacingHeightLarge),
          Text(
            "Hi, you're not logging in.",
            style: TextStyle(
              color: ThemeColors.dark,
              fontSize: h2,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: spacingHeightSmall),
          Text(
            "Please login or sign up to continue.",
            textAlign: TextAlign.center,
            style: TextStyle(color: ThemeColors.dark, fontSize: h3),
          ),
          SizedBox(height: spacingHeightLarge),
          SizedBox(
            width: phoneWidth / 2,
            child: DefaultButton(
              text: "Login",
              buttonColor: ThemeColors.primaryDark,
              onPressed: () {
                Navigator.of(context, rootNavigator: true)
                    .pushNamed("/login/${loginAsset.template}");
              },
            ),
          ),
        ],
      ),
    );
  }
}
