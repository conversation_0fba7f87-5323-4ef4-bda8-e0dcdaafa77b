import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/cart.dart';
import 'package:amverton/Model/cart/cart_item.dart';
import 'package:amverton/Model/cart/cart_page_variable.dart';
import 'package:amverton/Model/cart/voucher.dart';
import 'package:amverton/View/Cart/cart_page.dart';
import 'package:hive/hive.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class ProductTemplate2 extends StatefulWidget {
  final Function editItem;
  final Function updateCart;
  final Function deleteCart;
  final CartItem cartItem;
  final int itemIndex;
  final CartPageVariable? cartPageVariable;
  final List<Voucher> voucherList;

  const ProductTemplate2({
    Key? key,
    required this.editItem,
    required this.updateCart,
    required this.deleteCart,
    required this.cartItem,
    required this.itemIndex,
    required this.cartPageVariable,
    required this.voucherList,
  }) : super(key: key);

  @override
  State<ProductTemplate2> createState() => _ProductTemplate2State();
}

class _ProductTemplate2State extends State<ProductTemplate2> {
  Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Container(
      width: phoneWidth,
      margin: EdgeInsets.only(bottom: spacingHeightSmall),
      padding: EdgeInsets.symmetric(vertical: defaultInnerPadding),
      decoration: BoxDecoration(
        color: ThemeColors.light,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(15)),
            child: CachedNetworkImage(
              imageUrl: widget.cartItem.product.image ?? "",
              height: phoneWidth / 4.5,
              width: phoneWidth / 4.5,
              fit: BoxFit.cover,
              placeholder: (context, url) {
                return Center(
                  child: CircularProgressIndicator(
                    color: ThemeColors.primaryDark,
                  ),
                );
              },
              errorWidget: (context, url, error) {
                return Icon(
                  Icons.error_outline_outlined,
                  color: ThemeColors.primaryDark,
                );
              },
            ),
          ),
          SizedBox(width: spacingHeightSmall),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        widget.cartItem.product.name,
                        style: TextStyle(
                          color: ThemeColors.dark,
                          fontSize: h3,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(width: spacingHeightSmall),
                    GestureDetector(
                      onTap: () {
                        widget.editItem(widget.itemIndex);
                      },
                      child: Text(
                        "Edit",
                        style: TextStyle(
                          color: ThemeColors.secondaryDark,
                          fontSize: h4,
                        ),
                      ),
                    ),
                  ],
                ),
                // selected bundle //
                if (widget.cartItem.product.type == "BUNDLE")
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: List.generate(
                        widget.cartItem.product.bundleCategory!.length,
                        (categoryIndex) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: List.generate(
                            widget
                                .cartItem
                                .product
                                .bundleCategory![categoryIndex]
                                .bundleItems
                                .length, (itemIndex) {
                          if (widget
                                  .cartItem
                                  .product
                                  .bundleCategory![categoryIndex]
                                  .bundleItems[itemIndex]
                                  .isSelected ==
                              1) {
                            return Text(
                              widget
                                  .cartItem
                                  .product
                                  .bundleCategory![categoryIndex]
                                  .bundleItems[itemIndex]
                                  .product
                                  .name,
                              style: TextStyle(
                                  color: ThemeColors.dark, fontSize: h4),
                            );
                          }
                          return SizedBox();
                        }),
                      );
                    }),
                  ),
                // selected modifier //
                if (widget.cartItem.product.modifierCategory != null)
                  if (widget.cartItem.product.modifierCategory!.isNotEmpty)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: List.generate(
                          widget.cartItem.product.modifierCategory!.length,
                          (categoryIndex) {
                        return Column(
                          children: List.generate(
                              widget
                                  .cartItem
                                  .product
                                  .modifierCategory![categoryIndex]
                                  .modifierItems
                                  .length, (itemIndex) {
                            if (widget
                                    .cartItem
                                    .product
                                    .modifierCategory![categoryIndex]
                                    .modifierItems[itemIndex]
                                    .isSelected ==
                                1) {
                              return Text(
                                widget
                                    .cartItem
                                    .product
                                    .modifierCategory![categoryIndex]
                                    .modifierItems[itemIndex]
                                    .name,
                                style: TextStyle(
                                    color: ThemeColors.dark, fontSize: h4),
                              );
                            }
                            return SizedBox();
                          }),
                        );
                      }),
                    ),
                if (widget.cartItem.remark != null)
                  Text(
                    widget.cartItem.remark!,
                    style: TextStyle(color: ThemeColors.gray, fontSize: h4),
                  ),
                SizedBox(height: spacingHeightMedium),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      important_variables.currency + widget.cartItem.price,
                      style: TextStyle(
                        color: ThemeColors.secondaryDark,
                        fontSize: h3,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.right,
                    ),
                    Row(
                      children: [
                        GestureDetector(
                          onTap: () {
                            if (widget.cartItem.quantity == 1) {
                              AwesomeDialog(
                                context: context,
                                dialogType: DialogType.warning,
                                animType: AnimType.bottomSlide,
                                dismissOnTouchOutside: false,
                                dialogBackgroundColor: Colors.white,
                                autoDismiss: false,
                                onDismissCallback: (DismissType dismissType) {},
                                padding: EdgeInsets.all(defaultPadding),
                                body: Center(
                                  child: Text(
                                    "Are you sure you want to delete this item?",
                                    style: const TextStyle(
                                        fontStyle: FontStyle.italic),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                // Yes
                                btnCancel: GestureDetector(
                                  onTap: () {
                                    Navigator.of(context).pop(true);
                                    widget.deleteCart(
                                        widget.cartItem.id,
                                        widget.cartPageVariable!
                                                    .selectedVoucherIndex ==
                                                ""
                                            ? null
                                            : widget
                                                .voucherList![int.parse(widget
                                                    .cartPageVariable!
                                                    .selectedVoucherIndex)]
                                                .id);
                                  },
                                  child: Container(
                                    alignment: Alignment.center,
                                    height: buttonHeight,
                                    decoration: BoxDecoration(
                                      color: Colors.red,
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Text(
                                      "Yes",
                                      style: TextStyle(
                                        color: ThemeColors.light,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                                // Cancel
                                btnOk: GestureDetector(
                                  onTap: () => Navigator.of(context).pop(false),
                                  child: Container(
                                    alignment: Alignment.center,
                                    height: buttonHeight,
                                    decoration: BoxDecoration(
                                      color: ThemeColors.light,
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Text(
                                      "Cancel",
                                      style: TextStyle(
                                        color: Colors.red,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ).show();
                            }
                            if (widget.cartItem.quantity > 1) {
                              widget.updateCart(
                                  widget.cartItem.id,
                                  widget.cartItem.quantity - 1,
                                  widget.cartItem.remark,
                                  widget.cartPageVariable!
                                              .selectedVoucherIndex ==
                                          ""
                                      ? null
                                      : widget
                                          .voucherList[int.parse(widget
                                              .cartPageVariable!
                                              .selectedVoucherIndex)]
                                          .id,
                                  widget.cartItem.product.bundleCategory,
                                  widget.cartItem.product.modifierCategory);
                            }
                          },
                          child: ImageIcon(
                            //colorD3 didnt used, used to be cannot minus when it is only 1 quantity left
                            AssetImage(
                                "assets/icons/${boxGlobal.get("iconSet")}/General_Minus.png"),
                            color: widget.cartItem.quantity > 0
                                ? ThemeColors.primaryDark
                                : ThemeColors.disabled,
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: spacingHeightMedium),
                          child: Text(
                            widget.cartItem.quantity.toString(),
                            style: TextStyle(
                                color: ThemeColors.dark, fontSize: h3),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            widget.updateCart(
                                widget.cartItem.id,
                                widget.cartItem.quantity + 1,
                                widget.cartItem.remark,
                                widget.cartPageVariable!.selectedVoucherIndex ==
                                        ""
                                    ? null
                                    : widget
                                        .voucherList[int.parse(widget
                                            .cartPageVariable!
                                            .selectedVoucherIndex)]
                                        .id,
                                widget.cartItem.product.bundleCategory,
                                widget.cartItem.product.modifierCategory);
                          },
                          child: ImageIcon(
                            AssetImage(
                                "assets/icons/${boxGlobal.get("iconSet")}/General_Plus.png"),
                            color: ThemeColors.primaryDark,
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
