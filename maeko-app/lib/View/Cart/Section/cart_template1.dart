import 'dart:convert';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/address.dart';
import 'package:amverton/Model/bottom_bar.dart';
import 'package:amverton/Model/cart/brand.dart';
import 'package:amverton/Model/cart/cart.dart';
import 'package:amverton/Model/cart/cart_page_variable.dart';
import 'package:amverton/Model/cart/payment_method.dart';
import 'package:amverton/Model/product.dart';
import 'package:amverton/Notifier/notifiers.dart';
import 'package:amverton/Provider/providers.dart';
import 'package:amverton/Repository/api.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Cart/Section/product_template1.dart';
import 'package:amverton/View/Cart/Section/product_template2.dart';
import 'package:amverton/View/Cart/voucher_option.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_dropdown1.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
import 'package:amverton/View/OrderMenu/deliver_address_option.dart';
import 'package:amverton/main.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class CartTemplate1 extends ConsumerStatefulWidget {
  final Function editItem;
  final Function getCart;
  final Function updateCart;
  final Function deleteCart;
  final Function makeOrder;
  final Function onRefresh;

  const CartTemplate1({
    super.key,
    required this.editItem,
    required this.getCart,
    required this.updateCart,
    required this.deleteCart,
    required this.makeOrder,
    required this.onRefresh,
  });

  @override
  _CartTemplate1State createState() => _CartTemplate1State();
}

class _CartTemplate1State extends ConsumerState<CartTemplate1> {
  late Box box;
  bool isLogin = false;
  late Box boxMenu;
  late Box boxGlobal;
  late Future boxFuture;
  Cart? cart;
  CartPageVariable? cartPageVariable;

  TextEditingController remarkController = TextEditingController();
  TextEditingController creditController = TextEditingController();

  List<Brand> paymentBrands = [];

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();
  }

  openBox() async {
    CartPageVariable? cartPageVariable = ref.read(cartPageVariableProvider);
    box = Hive.box('box');
    isLogin = box.get("is_login") ?? false;
    boxMenu = Hive.box('boxMenu');
    boxGlobal = Hive.box('boxGlobal');
    paymentBrands = List.of(cartPageVariable!.paymentBrands);
    creditController.text = cartPageVariable.pointUsed.toString();
  }

  @override
  Widget build(BuildContext context) {
    cart = ref.watch(cartProvider);
    cartPageVariable = ref.watch(cartPageVariableProvider);

    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            return isLogin ? contents() : guestView();
          }
          return Container();
        });
  }

  Widget contents() {
    if (cart!.items.length == 0) {
      return emptyList();
    } else {
      return SizedBox(
        height: phoneHeight,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 3,
              child: Container(
                padding: EdgeInsets.only(
                  left: defaultPadding,
                  right: defaultPadding,
                  top: defaultPadding,
                ),
                child: RefreshIndicator(
                    color: ThemeColors.primaryDark,
                    onRefresh: () {
                      return Future.delayed(const Duration(seconds: 1), () {
                        setState(() {
                          widget.getCart;
                        });
                      });
                    },
                    child: LayoutBuilder(builder: (context, constraint) {
                      return SingleChildScrollView(
                        padding: EdgeInsets.only(bottom: defaultInnerPadding),
                        physics: const AlwaysScrollableScrollPhysics(),
                        child: ConstrainedBox(
                          constraints:
                              BoxConstraints(minHeight: constraint.maxHeight),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              header(),
                              cartItemWidget(),
                              if (cart!.freeItem != null) freeItemWidget(),
                              DefaultTextField1(
                                controller: remarkController,
                                keyBoardType: TextInputType.text,
                                hintText: "Remark",
                              ),
                            ],
                          ),
                        ),
                      );
                    })),
              ),
            ),
            Expanded(
              flex: 3,
              child: summary(),
            ),
          ],
        ),
      );
    }
  }

  Widget header() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // outlet
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "${cartPageVariable!.selectedDeliverMethod == 1 ? "Pick Up" : "Selected"} Outlet",
              style: TextStyle(
                color: ThemeColors.dark,
                fontSize: h3,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: spacingHeightSmall),
            Text(
              cart!.outlet.name,
              style: TextStyle(color: ThemeColors.dark, fontSize: h4),
            ),
          ],
        ),
        // deliver address
        if (cartPageVariable!.selectedDeliverMethod == 0)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: spacingHeightMedium),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Delivery to",
                    style: TextStyle(
                      color: ThemeColors.dark,
                      fontSize: h3,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  GestureDetector(
                    onTap: () async {
                      if (cartPageVariable!.selectedDeliverMethod == 0 &&
                          cartPageVariable!.selectedAddressId == 0) {
                        int index = registerAsset.indexWhere(
                            (element) => element.sectionName == "STEP_1");
                        // element.sectionName == "STEP_3");
                        if (index != -1)
                          Navigator.pushNamed(context,
                                  "/add_address/${registerAsset[index].template}")
                              .then((_) async {
                            await widget.onRefresh;
                          });
                      } else {
                        final result = await Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => DeliverAddressOption(
                                selectedAddressId:
                                    cartPageVariable!.selectedAddressId,
                                updateCartDeliverAddress: true,
                              ),
                            ));

                        setState(() {
                          if (result != null)
                            cartPageVariable!.selectedAddressId = result;
                          widget.getCart;
                        });
                      }
                    },
                    child: Text(
                      cartPageVariable!.selectedDeliverMethod == 0 &&
                              cartPageVariable!.selectedAddressId == 0
                          ? "Add"
                          : "Change",
                      style: TextStyle(
                        color: ThemeColors.secondaryDark,
                        fontSize: h4,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: spacingHeightSmall),
              Text(
                () {
                  for (Address each in cartPageVariable!.addressList) {
                    if (cartPageVariable!.selectedAddressId == each.id)
                      return "${each.name}\n${each.countryCode}${each.phone}\n${each.address1}, ${each.address2 ?? ""}\n${each.postcode} ${each.city}, ${each.state?.name ?? ""}";
                  }
                  return "You don't have a delivery address yet.";
                }(),
                style: TextStyle(
                    color: cartPageVariable!.selectedAddressId == 0
                        ? ThemeColors.secondaryDark
                        : ThemeColors.dark,
                    fontSize: h4),
              ),
            ],
          ),
        // delivery time
        if (cartPageVariable!.schedules.length > 2)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: spacingHeightMedium),
              Text(
                "Schedule",
                style: TextStyle(
                  color: ThemeColors.dark,
                  fontSize: h3,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: spacingHeightSmall),
              DefaultDropdown1(
                borderRadius: 20,
                selectedValue: cartPageVariable!.selectedSchedule!,
                items: cartPageVariable!.schedules.map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(
                      value,
                      style: TextStyle(
                        color: ThemeColors.dark,
                        fontSize: h5,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (String? value) async {
                  setState(() {
                    cartPageVariable!.selectedSchedule = value!;
                  });
                  boxMenu.put(
                      "selected_schedule", cartPageVariable!.selectedSchedule);
                },
              ),
            ],
          ),
        SizedBox(height: defaultInnerPadding),
      ],
    );
  }

  Widget cartItemWidget() {
    return ListView.builder(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemCount: cart!.items.length,
      itemBuilder: (BuildContext context, int index) {
        return Dismissible(
          key: Key(cart!.items[index].id.toString()),
          direction: DismissDirection.endToStart,
          background: Container(
              padding: const EdgeInsets.only(right: 30),
              alignment: Alignment.centerRight,
              child: ImageIcon(
                AssetImage(
                    "assets/icons/${boxGlobal.get("iconSet")}/trash-02.png"),
                color: Colors.red,
              )),
          confirmDismiss: (DismissDirection direction) async {
            return await AwesomeDialog(
              context: context,
              dialogType: DialogType.warning,
              animType: AnimType.bottomSlide,
              dismissOnTouchOutside: false,
              dialogBackgroundColor: Colors.white,
              autoDismiss: false,
              onDismissCallback: (DismissType dismissType) {},
              padding: EdgeInsets.all(defaultPadding),
              body: Center(
                child: Text(
                  "Are you sure you want to delete this item?",
                  style: const TextStyle(fontStyle: FontStyle.italic),
                  textAlign: TextAlign.center,
                ),
              ),
              // Yes
              btnCancel: GestureDetector(
                onTap: () => Navigator.of(context).pop(true),
                child: Container(
                  alignment: Alignment.center,
                  height: buttonHeight,
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    "Yes",
                    style: TextStyle(
                      color: ThemeColors.light,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              // Cancel
              btnOk: GestureDetector(
                onTap: () => Navigator.of(context).pop(false),
                child: Container(
                  alignment: Alignment.center,
                  height: buttonHeight,
                  decoration: BoxDecoration(
                    color: ThemeColors.light,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    "Cancel",
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ).show();
          },
          onDismissed: (direction) {
            widget.deleteCart(
                cart!.items[index].id,
                cartPageVariable!.selectedVoucherIndex == ""
                    ? null
                    : cart!
                        .vouchers[
                            int.parse(cartPageVariable!.selectedVoucherIndex)]
                        .id);
          },
          // todo temp hardcode for obriens (they say wanna make template for cart item list, but dk will proceed or nt)
          child: important_variables.projectName == "sbk"
              ? ProductTemplate1(
                  cartItem: cart!.items[index],
                  itemIndex: index,
                )
              : ProductTemplate2(
                  editItem: widget.editItem,
                  updateCart: widget.updateCart,
                  deleteCart: widget.deleteCart,
                  cartItem: cart!.items[index],
                  itemIndex: index,
                  cartPageVariable: cartPageVariable,
                  voucherList: cart!.vouchers,
                ),
        );
      },
    );
  }

  Widget freeItemWidget() {
    return Container(
        padding: EdgeInsets.only(top: defaultInnerPadding),
        decoration: BoxDecoration(
            border: Border(top: BorderSide(color: ThemeColors.disabled))),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: phoneWidth,
              padding: EdgeInsets.symmetric(horizontal: defaultInnerPadding),
              margin: EdgeInsets.only(bottom: spacingHeightMedium),
              decoration: BoxDecoration(
                color: ThemeColors.light,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    cart!.freeItem?.name ?? "",
                    style: TextStyle(color: ThemeColors.dark, fontSize: h3),
                  ),
                  SizedBox(height: spacingHeightMedium),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "${important_variables.currency}0.00",
                        style: TextStyle(
                          color: ThemeColors.secondaryDark,
                          fontSize: h3,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.right,
                      ),
                      Text(
                        "Free Item",
                        style: TextStyle(
                          color: ThemeColors.dark,
                          fontSize: h3,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ],
        ));
  }

  Widget summary() {
    return Container(
      color: ThemeColors.light,
      padding: EdgeInsets.only(
          left: defaultPadding, right: defaultPadding, bottom: defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          if (cart!.vouchers.isNotEmpty)
            Container(
              padding: EdgeInsets.all(defaultInnerPadding),
              width: phoneWidth,
              height: buttonHeight,
              alignment: Alignment.centerLeft,
              decoration: BoxDecoration(
                color: ThemeColors.light,
                border: Border.all(color: ThemeColors.disabled),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () async {
                        final result = await Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => VoucherOption(
                                voucherList: cart!.vouchers,
                                selectedVoucherIndex:
                                    cartPageVariable!.selectedVoucherIndex,
                              ),
                            ));
                        if (result != null) {
                          setState(() {
                            cartPageVariable!.selectedVoucherIndex = result[0];
                          });
                          widget.updateCart(
                              null,
                              null,
                              null,
                              cart!
                                  .vouchers[int.parse(
                                      cartPageVariable!.selectedVoucherIndex)]
                                  .id,
                              null,
                              null);
                        }
                      },
                      child: AutoSizeText(
                        cartPageVariable!.selectedVoucherIndex == ""
                            ? "Click to select voucher"
                            : cart!
                                .vouchers[int.parse(
                                    cartPageVariable!.selectedVoucherIndex)]
                                .title,
                        style: TextStyle(
                          color: cartPageVariable!.selectedVoucherIndex == ""
                              ? ThemeColors.primaryDark
                              : ThemeColors.dark,
                        ),
                        minFontSize: h4,
                        maxFontSize: h3,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  SizedBox(width: spacingWidth),
                  if (cartPageVariable!.selectedVoucherIndex != "")
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          cartPageVariable!.selectedVoucherIndex = "";
                        });
                        widget.getCart;
                      },
                      child: Text(
                        "Remove",
                        style: TextStyle(
                          color: Colors.red,
                          fontSize: h5,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          Row(
            children: [
              Text(
                "Subtotal",
                style: TextStyle(color: ThemeColors.dark, fontSize: h3),
              ),
              const Spacer(),
              SizedBox(
                width: phoneWidth / 1.8,
                child: Text(
                  important_variables.currency + cart!.subtotal,
                  style: TextStyle(color: ThemeColors.dark, fontSize: h3),
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
          Row(
            children: [
              Text(
                "Discount",
                style: TextStyle(color: ThemeColors.dark, fontSize: h3),
              ),
              const Spacer(),
              Text(
                "-${important_variables.currency}${cart!.discount}",
                style: TextStyle(color: ThemeColors.dark, fontSize: h3),
                textAlign: TextAlign.end,
              ),
            ],
          ),
          Row(
            children: [
              Text(
                "Service Tax (10%)",
                style: TextStyle(color: ThemeColors.dark, fontSize: h3),
              ),
              const Spacer(),
              Text(
                important_variables.currency + cart!.serviceTax,
                style: TextStyle(color: ThemeColors.dark, fontSize: h3),
                textAlign: TextAlign.end,
              ),
            ],
          ),
          Row(
            children: [
              Text(
                "SST (6%)",
                style: TextStyle(color: ThemeColors.dark, fontSize: h3),
              ),
              const Spacer(),
              Text(
                important_variables.currency + cart!.tax,
                style: TextStyle(color: ThemeColors.dark, fontSize: h3),
                textAlign: TextAlign.end,
              ),
            ],
          ),
          Row(
            children: [
              Text(
                "Credit (" + cart!.creditAvailable.toString() + ")",
                style: TextStyle(
                    color: ThemeColors.secondaryDark,
                    fontSize: h3,
                    fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Text(
                "-RM",
                style: TextStyle(color: ThemeColors.dark, fontSize: h3),
                textAlign: TextAlign.end,
              ),
              SizedBox(
                width: defaultInnerPadding,
              ),
              SizedBox(
                width: 50,
                child: TextField(
                  keyboardType: TextInputType.number,
                  controller: creditController,
                  style: TextStyle(
                    fontSize: h3,
                    height: 1,
                  ),
                  decoration: InputDecoration(
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(vertical: 4),
                    border: UnderlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      value = value != '' ? value : "0.00";
                      cartPageVariable!.pointUsed = value;
                      cartPageVariable!.actualTotal =
                          (Decimal.parse(cart!.total) - Decimal.parse(value))
                              .toString();
                    });
                  },
                ),
              ),
            ],
          ),
          Row(
            children: [
              Text(
                "Total",
                style: TextStyle(
                    color: ThemeColors.secondaryDark,
                    fontSize: h3,
                    fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Text(
                important_variables.currency + cartPageVariable!.actualTotal,
                style: TextStyle(
                    color: ThemeColors.secondaryDark,
                    fontSize: h3,
                    fontWeight: FontWeight.bold),
                textAlign: TextAlign.end,
              ),
            ],
          ),
          DefaultDropdown1<PaymentMethod>(
            borderRadius: 15,
            verticalMargin: 0,
            selectedValue: cartPageVariable!.selectedPaymentMethod!,
            items: cartPageVariable!.paymentMethods
                .map((PaymentMethod paymentMethod) {
              return DropdownMenuItem<PaymentMethod>(
                value: paymentMethod,
                child: Text(
                  paymentMethod.name,
                  style: TextStyle(
                    color: ThemeColors.dark,
                    fontSize: h3,
                  ),
                ),
              );
            }).toList(),
            onChanged: (PaymentMethod? value) async {
              setState(() {
                cartPageVariable!.selectedPaymentMethod = value!;
                cartPageVariable!.selectedPaymentBrand = null;
                paymentBrands.clear();
                if (cartPageVariable!
                    .selectedPaymentMethod!.brands.isNotEmpty) {
                  paymentBrands =
                      List.of(cartPageVariable!.selectedPaymentMethod!.brands);
                  if (paymentBrands.isNotEmpty) {
                    cartPageVariable!.selectedPaymentBrand =
                        paymentBrands.first;
                  }
                }
              });
            },
          ),
          if (paymentBrands.isNotEmpty)
            DefaultDropdown1<Brand>(
              borderRadius: 15,
              verticalMargin: 0,
              selectedValue: cartPageVariable!.selectedPaymentBrand!,
              items: paymentBrands.map((Brand paymentBrand) {
                return DropdownMenuItem<Brand>(
                  value: paymentBrand,
                  child: Text(
                    paymentBrand.name,
                    style: TextStyle(
                      color: ThemeColors.dark,
                      fontSize: h3,
                    ),
                  ),
                );
              }).toList(),
              onChanged: (Brand? value) async {
                setState(() {
                  cartPageVariable!.selectedPaymentBrand = value!;
                });
              },
            ),
          DefaultButton(
            text: "Check Out",
            buttonColor: important_variables.projectName == "obriens"
                ? ThemeColors.secondaryDark
                : ThemeColors.primaryDark,
            onPressed: () {
              if (cartPageVariable!.selectedDeliverMethod == 0 &&
                  cartPageVariable!.selectedAddressId == 0) {
                defaultWarningDialog(context,
                        "Please select a delivery address to continue.", () {})
                    .show();
              } else {
                widget.makeOrder(creditController.text, remarkController.text);
                // Navigator.push(
                //     context,
                //     MaterialPageRoute(
                //         builder: (BuildContext context) =>
                //             const CartConfirmation()));
              }
            },
          ),
        ],
      ),
    );
  }

  Widget emptyList() {
    return RefreshIndicator(
      color: ThemeColors.primaryDark,
      onRefresh: () {
        return Future.delayed(const Duration(seconds: 1), () {
          setState(() {
            widget.getCart;
          });
        });
      },
      child: SizedBox(
          width: phoneWidth,
          height: phoneHeight,
          child: SingleChildScrollView(
            physics: AlwaysScrollableScrollPhysics(),
            child: Column(
              children: [
                SizedBox(height: phoneHeight / 4),
                Image.asset(
                    "assets/icons/${boxGlobal.get("iconSet")}/Cart 01.png"),
                SizedBox(height: spacingHeightLarge),
                Text(
                  "No items found",
                  style: TextStyle(
                    color: ThemeColors.dark,
                    fontSize: h2,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: spacingHeightSmall),
                Text(
                  "Look like you have not added\nanything to your cart.",
                  textAlign: TextAlign.center,
                  style: TextStyle(color: ThemeColors.dark, fontSize: h3),
                ),
                SizedBox(height: spacingHeightLarge),
                SizedBox(
                  width: phoneWidth / 2,
                  child: DefaultButton(
                    text: "Browse",
                    buttonColor: important_variables.projectName == "obriens"
                        ? ThemeColors.secondaryDark
                        : ThemeColors.primaryDark,
                    onPressed: () {
                      List<BottomBar> bottomBarList =
                          boxGlobal.get("bottomBar") ?? [];
                      if (bottomBarList.isNotEmpty) {
                        int index = bottomBarList
                            .indexWhere((element) => element.name == "ORDER");
                        if (index != -1) {
                          final dynamic navigationBar =
                              barGlobalKey.currentWidget;
                          navigationBar.onTap(index);
                        }
                      }
                    },
                  ),
                ),
              ],
            ),
          )),
    );
  }

  Widget guestView() {
    return SizedBox(
      width: phoneWidth,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.warning_amber_outlined,
            color: Colors.red,
            size: 60,
          ),
          SizedBox(height: spacingHeightLarge),
          Text(
            "Hi, you're not logging in.",
            style: TextStyle(
              color: ThemeColors.dark,
              fontSize: h2,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: spacingHeightSmall),
          Text(
            "Please login or sign up to continue.",
            textAlign: TextAlign.center,
            style: TextStyle(color: ThemeColors.dark, fontSize: h3),
          ),
          SizedBox(height: spacingHeightLarge),
          SizedBox(
            width: phoneWidth / 2,
            child: DefaultButton(
              text: "Login",
              buttonColor: ThemeColors.primaryDark,
              onPressed: () {
                Navigator.of(context, rootNavigator: true)
                    .pushNamed("/login/${loginAsset.template}");
              },
            ),
          ),
        ],
      ),
    );
  }
}
