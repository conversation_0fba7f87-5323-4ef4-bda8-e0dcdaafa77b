import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/cart/recommended_product.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Model/product.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/OrderMenu/order_menu_detail_template1.dart';
import 'package:hive_flutter/hive_flutter.dart';

class AddOnTemplate1 extends StatefulWidget {
  final RecommendedProduct recommededProduct;
  final String tag;
  final Outlet outlet;
  final Function getCart;

  AddOnTemplate1(
      {required this.recommededProduct,
      required this.tag,
      required this.outlet,
      required this.getCart});

  @override
  State<AddOnTemplate1> createState() => _AddOnTemplate1State();
}

class _AddOnTemplate1State extends State<AddOnTemplate1> {
  final Box boxGlobal = Hive.box('boxGlobal');

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        getProductDetail(widget.recommededProduct.id);
      },
      child: Container(
        width: phoneWidth * 0.7,
        margin: EdgeInsets.only(top: 0, bottom: 8, left: 3, right: 8),
        padding: EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.0),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 2,
              blurRadius: 5,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(10.0),
                  child: CachedNetworkImage(
                    imageUrl: widget.recommededProduct.image,
                    height: 80,
                    width: 80,
                    fit: BoxFit.cover,
                  ),
                ),
                SizedBox(
                  width: 8,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.recommededProduct.name,
                        style: TextStyle(
                          fontSize: h4,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '+ RM ${widget.recommededProduct.price}',
                        style: TextStyle(
                          fontSize: h4,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Row(
              children: [
                SizedBox(
                  width: phoneWidth * 0.4,
                ),
                Expanded(
                  child: DefaultButton(
                    height: phoneHeight * 0.05,
                    text: "+ Add",
                    buttonColor: ThemeColors.secondaryDark,
                    onPressed: () {
                      getProductDetail(widget.recommededProduct.id);
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  getProductDetail(int productId) async {
    EasyLoading.show();
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.productDetail(productId);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        Product product = Product.fromJson(data['data']['product']);
        List<Product> recommended =
            (data['data']['recommended_products'] as List)
                .map((data) => Product.fromJson(data))
                .toList();
        String? inviteMsg = data['data']['invite_message'] ?? null;
        String? inviteUrl = data['data']['invite_url'] ?? null;

        await Navigator.push(
            context,
            MaterialPageRoute(
                builder: (BuildContext context) => OrderMenuDetailTemplate1(
                      outlet: widget.outlet,
                      product: product,
                      recommended: recommended,
                      inviteMsg: inviteMsg,
                      inviteUrl: inviteUrl,
                      tag: widget.tag,
                    ))).then((value) => widget.getCart());
      }
    }
  }
}
