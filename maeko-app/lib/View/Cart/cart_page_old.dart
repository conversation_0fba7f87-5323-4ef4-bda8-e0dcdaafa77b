// import 'dart:async';
// import 'dart:convert';
// import 'dart:developer';
// import 'dart:io';
// import 'package:auto_size_text/auto_size_text.dart';
// import 'package:awesome_dialog/awesome_dialog.dart';
// import 'package:decimal/decimal.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_easyloading/flutter_easyloading.dart';
// import 'package:amverton/Constant/theme_size.dart';
// import 'package:amverton/Model/address.dart';
// import 'package:amverton/Model/bottom_bar.dart';
// import 'package:amverton/Model/cart.dart';
// import 'package:amverton/Model/eghl_payment.dart';
// import 'package:amverton/Model/outlet.dart';
// import 'package:amverton/Model/payment_method.dart';
// import 'package:amverton/Model/product.dart';
// import 'package:amverton/Model/voucher.dart';
// import 'package:amverton/Repository/api_get.dart';
// import 'package:amverton/Repository/api_post.dart';
// import 'package:amverton/Repository/modify_assets.dart';
// import 'package:amverton/View/Cart/Section/product_template1.dart';
// import 'package:amverton/View/Cart/Section/product_template2.dart';
// import 'package:amverton/View/Custom%20Widgets/default_textfield2.dart';
// import 'package:amverton/View/OrderMenu/deliver_address_option.dart';
// import 'package:amverton/View/Cart/voucher_option.dart';
// import 'package:amverton/View/Custom%20Widgets/default_button.dart';
// import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
// import 'package:amverton/View/Custom%20Widgets/default_dropdown1.dart';
// import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
// import 'package:amverton/View/Home/web_view.dart';
// import 'package:amverton/View/OrderMenu/order_menu_detail_template1.dart';
// import 'package:amverton/View/OrderMenu/order_menu_page.dart';
// import 'package:amverton/main.dart';
// import 'package:hive/hive.dart';
// import 'package:collection/collection.dart';
// import 'package:amverton/Constant/important_variables.dart' as important_variables;

// class CartPage extends StatefulWidget {
//   const CartPage({Key? key}) : super(key: key);

//   static const androidPlatform = const MethodChannel('fnb.android');
//   static const iosPlatform = const MethodChannel('fnb.ios');

//   @override
//   _CartPageState createState() => _CartPageState();

//   static void updateCart(
//       BuildContext context,
//       int? id,
//       int? quantity,
//       String? remark,
//       int? voucherId,
//       List<BundleCategory>? productBundle,
//       List<ModifierCategory>? productModifier) {
//     _CartPageState? state = context.findAncestorStateOfType<_CartPageState>();
//     state?.updateCart(
//         id, quantity, remark, voucherId, productBundle, productModifier);
//   }

//   static void editItem(BuildContext context, int itemIndex) {
//     _CartPageState? state = context.findAncestorStateOfType<_CartPageState>();
//     state?.editItem(itemIndex);
//   }

//   static void deleteCart(BuildContext context, id, int? voucherId) {
//     _CartPageState? state = context.findAncestorStateOfType<_CartPageState>();
//     state?.deleteCart(id, voucherId);
//   }
// }

// List<Voucher> voucherList = [];
// String selectedVoucherIndex = "";

// class _CartPageState extends State<CartPage> {
//   late Box box;
//   bool isLogin = false;
//   late Box boxMenu;
//   late Box boxGlobal;
//   late Future boxFuture;
//   late Future future;

//   Outlet? outlet;
//   List<Cart> cartList = [];
//   Product? freeItem;
//   String subtotal = "0.00";
//   String discount = "0.00";
//   String tax = "0.00";
//   String serviceTax = "0.00";
//   String total = "0.00";
//   String actualTotal = "0.00";
//   double creditAvailable = 0.00;
//   double creditUsable = 0.00;
//   TextEditingController remarkController = TextEditingController();
//   TextEditingController creditController = TextEditingController();

//   List<String> schedule = [];
//   String? selectedSchedule;
//   int selectedDeliverMethod = 0; // 0 = Delivery; 1 = Pickup
//   List<Address> addressList = [];
//   int selectedAddressId = 0;

//   List<PaymentMethod> paymentMethods = [];
//   List<Brand> paymentBrands = [];
//   PaymentMethod? selectedPaymentMethod;
//   Brand? selectedPaymentBrand;

//   @override
//   void initState() {
//     super.initState();
//     boxFuture = openBox();
//     future = getCart();
//   }

//   openBox() async {
//     box = await Hive.openBox('box');
//     isLogin = box.get("is_login") ?? false;
//     boxGlobal = await Hive.openBox('boxGlobal');
//   }

//   @override
//   Widget build(BuildContext context) {
//     return FutureBuilder(
//         future: boxFuture,
//         builder: (BuildContext context, AsyncSnapshot snapshot) {
//           if (snapshot.connectionState == ConnectionState.done) {
//             return GestureDetector(
//               onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
//               behavior: HitTestBehavior.translucent,
//               child: Scaffold(
//                 backgroundColor: ThemeColors.light,
//                 appBar: AppBar(title: const Text("Cart")),
//                 body: isLogin
//                     ? FutureBuilder(
//                         future: future,
//                         builder:
//                             (BuildContext context, AsyncSnapshot snapshot) {
//                           if (snapshot.connectionState ==
//                               ConnectionState.done) {
//                             if (cartList.isEmpty) {
//                               return emptyList();
//                             } else {
//                               return SizedBox(
//                                 height: phoneHeight,
//                                 child: Column(
//                                   crossAxisAlignment: CrossAxisAlignment.start,
//                                   children: [
//                                     Expanded(
//                                       flex: 3,
//                                       child: Container(
//                                         padding: EdgeInsets.only(
//                                           left: defaultPadding,
//                                           right: defaultPadding,
//                                           top: defaultPadding,
//                                         ),
//                                         child: RefreshIndicator(
//                                             color: Color(int.parse(
//                                                 boxGlobal.get("colorP1"))),
//                                             onRefresh: () {
//                                               return Future.delayed(
//                                                   const Duration(seconds: 1),
//                                                   () {
//                                                 setState(() {
//                                                   future = getCart();
//                                                 });
//                                               });
//                                             },
//                                             child: LayoutBuilder(
//                                                 builder: (context, constraint) {
//                                               return SingleChildScrollView(
//                                                 padding: EdgeInsets.only(
//                                                     bottom:
//                                                         defaultInnerPadding),
//                                                 physics:
//                                                     const AlwaysScrollableScrollPhysics(),
//                                                 child: ConstrainedBox(
//                                                   constraints: BoxConstraints(
//                                                       minHeight:
//                                                           constraint.maxHeight),
//                                                   child: Column(
//                                                     crossAxisAlignment:
//                                                         CrossAxisAlignment
//                                                             .start,
//                                                     children: [
//                                                       header(),
//                                                       cartItemWidget(),
//                                                       if (freeItem != null)
//                                                         freeItemWidget(),
//                                                       DefaultTextField1(
//                                                         controller:
//                                                             remarkController,
//                                                         keyBoardType:
//                                                             TextInputType.text,
//                                                         hintText: "Remark",
//                                                       ),
//                                                     ],
//                                                   ),
//                                                 ),
//                                               );
//                                             })),
//                                       ),
//                                     ),
//                                     Expanded(
//                                       flex: 3,
//                                       child: summary(),
//                                     ),
//                                   ],
//                                 ),
//                               );
//                             }
//                           }
//                           return Container();
//                         })
//                     : guestView(),
//               ),
//             );
//           }
//           return Container();
//         });
//   }

//   Widget header() {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         // outlet
//         Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text(
//               "${selectedDeliverMethod == 1 ? "Pick Up" : "Selected"} Outlet",
//               style: TextStyle(
//                 color: ThemeColors.dark,
//                 fontSize: h3,
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//             SizedBox(height: spacingHeightSmall),
//             Text(
//               outlet?.name ?? "",
//               style: TextStyle(
//                   color: ThemeColors.dark,
//                   fontSize: h4),
//             ),
//           ],
//         ),
//         // deliver address
//         if (selectedDeliverMethod == 0)
//           Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               SizedBox(height: spacingHeightMedium),
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Text(
//                     "Delivery to",
//                     style: TextStyle(
//                       color: ThemeColors.dark,
//                       fontSize: h3,
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                   GestureDetector(
//                     onTap: () async {
//                       if (selectedDeliverMethod == 0 &&
//                           selectedAddressId == 0) {
//                         int index = registerAsset.indexWhere(
//                             (element) => element.sectionName == "STEP_1");
//                         // element.sectionName == "STEP_3");
//                         if (index != -1)
//                           Navigator.pushNamed(context,
//                                   "/add_address/${registerAsset[index].template}")
//                               .then(onRefresh);
//                       } else {
//                         final result = await Navigator.push(
//                             context,
//                             MaterialPageRoute(
//                               builder: (context) => DeliverAddressOption(
//                                 selectedAddressId: selectedAddressId,
//                                 updateCartDeliverAddress: true,
//                               ),
//                             ));

//                         setState(() {
//                           if (result != null) selectedAddressId = result;
//                           future = getCart();
//                         });
//                       }
//                     },
//                     child: Text(
//                       selectedDeliverMethod == 0 && selectedAddressId == 0
//                           ? "Add"
//                           : "Change",
//                       style: TextStyle(
//                         color: ThemeColors.secondaryDark,
//                         fontSize: h4,
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//               SizedBox(height: spacingHeightSmall),
//               Text(
//                 () {
//                   for (Address each in addressList) {
//                     if (selectedAddressId == each.id)
//                       return "${each.name}\n${each.countryCode}${each.phone}\n${each.address1}, ${each.address2 ?? ""}\n${each.postcode} ${each.city}, ${each.states?.name ?? ""}";
//                   }
//                   return "You don't have a delivery address yet.";
//                 }(),
//                 style: TextStyle(
//                     color: selectedAddressId == 0
//                         ? ThemeColors.secondaryDark
//                         : ThemeColors.dark,
//                     fontSize: h4),
//               ),
//             ],
//           ),
//         // delivery time
//         if (schedule.length > 2)
//           Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               SizedBox(height: spacingHeightMedium),
//               Text(
//                 "Schedule",
//                 style: TextStyle(
//                   color: ThemeColors.dark,
//                   fontSize: h3,
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//               SizedBox(height: spacingHeightSmall),
//               DefaultDropdown1(
//                 borderRadius: 20,
//                 selectedValue: selectedSchedule!,
//                 items: schedule.map((String value) {
//                   return DropdownMenuItem<String>(
//                     value: value,
//                     child: Text(
//                       value,
//                       style: TextStyle(
//                         color: ThemeColors.dark,
//                         fontSize: h5,
//                       ),
//                     ),
//                   );
//                 }).toList(),
//                 onChanged: (String? value) async {
//                   setState(() {
//                     selectedSchedule = value!;
//                   });
//                   await boxMenu.put("selected_schedule", selectedSchedule);
//                 },
//               ),
//             ],
//           ),
//         SizedBox(height: defaultInnerPadding),
//       ],
//     );
//   }

//   Widget cartItemWidget() {
//     return ListView.builder(
//       padding: EdgeInsets.zero,
//       shrinkWrap: true,
//       physics: NeverScrollableScrollPhysics(),
//       itemCount: cartList.length,
//       itemBuilder: (BuildContext context, int index) {
//         return Dismissible(
//           key: Key(cartList[index].id.toString()),
//           direction: DismissDirection.endToStart,
//           background: Container(
//               padding: const EdgeInsets.only(right: 30),
//               alignment: Alignment.centerRight,
//               child: ImageIcon(
//                 AssetImage(
//                     "assets/icons/${boxGlobal.get("iconSet")}/trash-02.png"),
//                 color: Colors.red,
//               )),
//           confirmDismiss: (DismissDirection direction) async {
//             return await AwesomeDialog(
//               context: context,
//               dialogType: DialogType.warning,
//               animType: AnimType.bottomSlide,
//               dismissOnTouchOutside: false,
//               dialogBackgroundColor: Colors.white,
//               autoDismiss: false,
//               onDismissCallback: (DismissType dismissType) {},
//               padding: EdgeInsets.all(defaultPadding),
//               body: Center(
//                 child: Text(
//                   "Are you sure you want to delete this item?",
//                   style: const TextStyle(fontStyle: FontStyle.italic),
//                   textAlign: TextAlign.center,
//                 ),
//               ),
//               // Yes
//               btnCancel: GestureDetector(
//                 onTap: () => Navigator.of(context).pop(true),
//                 child: Container(
//                   alignment: Alignment.center,
//                   height: buttonHeight,
//                   decoration: BoxDecoration(
//                     color: Colors.red,
//                     borderRadius: BorderRadius.circular(20),
//                   ),
//                   child: Text(
//                     "Yes",
//                     style: TextStyle(
//                       color: ThemeColors.light,
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                 ),
//               ),
//               // Cancel
//               btnOk: GestureDetector(
//                 onTap: () => Navigator.of(context).pop(false),
//                 child: Container(
//                   alignment: Alignment.center,
//                   height: buttonHeight,
//                   decoration: BoxDecoration(
//                     color: ThemeColors.light,
//                     borderRadius: BorderRadius.circular(20),
//                   ),
//                   child: Text(
//                     "Cancel",
//                     style: TextStyle(
//                       color: Colors.red,
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                 ),
//               ),
//             ).show();
//           },
//           onDismissed: (direction) {
//             deleteCart(
//                 cartList[index].id,
//                 selectedVoucherIndex == ""
//                     ? null
//                     : voucherList[int.parse(selectedVoucherIndex)]
//                         .pivot!
//                         .voucherId);
//           },
//           // todo temp hardcode for obriens (they say wanna make template for cart item list, but dk will proceed or nt)
//           child: important_variables.projectName == "sbk"
//               ? ProductTemplate1(
//                   cartItem: cartList[index],
//                   itemIndex: index,
//                 )
//               : ProductTemplate2(
//                   cartItem: cartList[index],
//                   itemIndex: index,
//                 ),
//         );
//       },
//     );
//   }

//   Widget freeItemWidget() {
//     return Container(
//         padding: EdgeInsets.only(top: defaultInnerPadding),
//         decoration: BoxDecoration(
//             border: Border(
//                 top: BorderSide(
//                     color: ThemeColors.disabled))),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Container(
//               width: phoneWidth,
//               padding: EdgeInsets.symmetric(horizontal: defaultInnerPadding),
//               margin: EdgeInsets.only(bottom: spacingHeightMedium),
//               decoration: BoxDecoration(
//                 color: ThemeColors.light,
//                 borderRadius: BorderRadius.circular(20),
//               ),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     freeItem!.name,
//                     style: TextStyle(
//                         color: ThemeColors.dark,
//                         fontSize: h3),
//                   ),
//                   SizedBox(height: spacingHeightMedium),
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Text(
//                         "${important_variables.currency}0.00",
//                         style: TextStyle(
//                           color: ThemeColors.secondaryDark,
//                           fontSize: h3,
//                           fontWeight: FontWeight.bold,
//                         ),
//                         textAlign: TextAlign.right,
//                       ),
//                       Text(
//                         "Free Item",
//                         style: TextStyle(
//                           color: ThemeColors.dark,
//                           fontSize: h3,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                     ],
//                   )
//                 ],
//               ),
//             ),
//           ],
//         ));
//   }

//   Widget summary() {
//     return Container(
//       color: ThemeColors.light,
//       padding: EdgeInsets.only(
//           left: defaultPadding, right: defaultPadding, bottom: defaultPadding),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//         children: [
//           if (voucherList.isNotEmpty)
//             Container(
//               padding: EdgeInsets.all(defaultInnerPadding),
//               width: phoneWidth,
//               height: buttonHeight,
//               alignment: Alignment.centerLeft,
//               decoration: BoxDecoration(
//                 color: ThemeColors.light,
//                 border: Border.all(
//                     color: ThemeColors.disabled),
//                 borderRadius: BorderRadius.circular(15),
//               ),
//               child: Row(
//                 children: [
//                   Expanded(
//                     child: GestureDetector(
//                       onTap: () async {
//                         final result = await Navigator.push(
//                             context,
//                             MaterialPageRoute(
//                               builder: (context) => VoucherOption(
//                                 voucherList: voucherList,
//                                 selectedVoucherIndex: selectedVoucherIndex,
//                               ),
//                             ));
//                         if (result != null) {
//                           setState(() {
//                             selectedVoucherIndex = result[0];
//                           });
//                           updateCart(
//                               null,
//                               null,
//                               null,
//                               voucherList[int.parse(selectedVoucherIndex)]
//                                   .pivot!
//                                   .voucherId,
//                               null,
//                               null);
//                         }
//                       },
//                       child: AutoSizeText(
//                         selectedVoucherIndex == ""
//                             ? "Click to select voucher"
//                             : voucherList[int.parse(selectedVoucherIndex)]
//                                 .title,
//                         style: TextStyle(
//                           color: selectedVoucherIndex == ""
//                               ? ThemeColors.primaryDark
//                               : ThemeColors.dark,
//                         ),
//                         minFontSize: h4,
//                         maxFontSize: h3,
//                         maxLines: 1,
//                         overflow: TextOverflow.ellipsis,
//                       ),
//                     ),
//                   ),
//                   SizedBox(width: spacingWidth),
//                   if (selectedVoucherIndex != "")
//                     GestureDetector(
//                       onTap: () {
//                         setState(() {
//                           selectedVoucherIndex = "";
//                         });
//                         future = getCart();
//                       },
//                       child: Text(
//                         "Remove",
//                         style: TextStyle(
//                           color: Colors.red,
//                           fontSize: h5,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                     ),
//                 ],
//               ),
//             ),
//           Row(
//             children: [
//               Text(
//                 "Subtotal",
//                 style: TextStyle(
//                     color: ThemeColors.dark,
//                     fontSize: h3),
//               ),
//               const Spacer(),
//               SizedBox(
//                 width: phoneWidth / 1.8,
//                 child: Text(
//                   important_variables.currency + subtotal,
//                   style: TextStyle(
//                       color: ThemeColors.dark,
//                       fontSize: h3),
//                   textAlign: TextAlign.end,
//                 ),
//               ),
//             ],
//           ),
//           Row(
//             children: [
//               Text(
//                 "Discount",
//                 style: TextStyle(
//                     color: ThemeColors.dark,
//                     fontSize: h3),
//               ),
//               const Spacer(),
//               Text(
//                 "-${important_variables.currency}${discount}",
//                 style: TextStyle(
//                     color: ThemeColors.dark,
//                     fontSize: h3),
//                 textAlign: TextAlign.end,
//               ),
//             ],
//           ),
//           Row(
//             children: [
//               Text(
//                 "Service Tax (10%)",
//                 style: TextStyle(
//                     color: ThemeColors.dark,
//                     fontSize: h3),
//               ),
//               const Spacer(),
//               Text(
//                 important_variables.currency + serviceTax,
//                 style: TextStyle(
//                     color: ThemeColors.dark,
//                     fontSize: h3),
//                 textAlign: TextAlign.end,
//               ),
//             ],
//           ),
//           Row(
//             children: [
//               Text(
//                 "SST (6%)",
//                 style: TextStyle(
//                     color: ThemeColors.dark,
//                     fontSize: h3),
//               ),
//               const Spacer(),
//               Text(
//                 important_variables.currency + tax,
//                 style: TextStyle(
//                     color: ThemeColors.dark,
//                     fontSize: h3),
//                 textAlign: TextAlign.end,
//               ),
//             ],
//           ),
//           Row(
//             children: [
//               Text(
//                 "Credit (" + creditAvailable.toString() + ")",
//                 style: TextStyle(
//                     color: ThemeColors.secondaryDark,
//                     fontSize: h3,
//                     fontWeight: FontWeight.bold),
//               ),
//               const Spacer(),
//               Text(
//                 "-RM",
//                 style: TextStyle(
//                     color: ThemeColors.dark,
//                     fontSize: h3),
//                 textAlign: TextAlign.end,
//               ),
//               SizedBox(
//                 width: defaultInnerPadding,
//               ),
//               SizedBox(
//                 width: 50,
//                 child: TextField(
//                   keyboardType: TextInputType.number,
//                   controller: creditController,
//                   style: TextStyle(
//                     fontSize: h3,
//                     height: 1,
//                   ),
//                   decoration: InputDecoration(
//                     isDense: true,
//                     contentPadding: EdgeInsets.symmetric(vertical: 4),
//                     border: UnderlineInputBorder(),
//                   ),
//                   onChanged: (value) {
//                     setState(() {
//                       value = value != '' ? value : "0.00";
//                       actualTotal =
//                           (Decimal.parse(total) - Decimal.parse(value))
//                               .toString();
//                     });
//                   },
//                 ),
//               ),
//             ],
//           ),
//           Row(
//             children: [
//               Text(
//                 "Total",
//                 style: TextStyle(
//                     color: ThemeColors.secondaryDark,
//                     fontSize: h3,
//                     fontWeight: FontWeight.bold),
//               ),
//               const Spacer(),
//               Text(
//                 important_variables.currency + actualTotal,
//                 style: TextStyle(
//                     color: ThemeColors.secondaryDark,
//                     fontSize: h3,
//                     fontWeight: FontWeight.bold),
//                 textAlign: TextAlign.end,
//               ),
//             ],
//           ),
//           DefaultDropdown1<PaymentMethod>(
//             borderRadius: 15,
//             verticalMargin: 0,
//             selectedValue: selectedPaymentMethod!,
//             items: paymentMethods.map((PaymentMethod paymentMethod) {
//               return DropdownMenuItem<PaymentMethod>(
//                 value: paymentMethod,
//                 child: Text(
//                   paymentMethod.name,
//                   style: TextStyle(
//                     color: ThemeColors.dark,
//                     fontSize: h3,
//                   ),
//                 ),
//               );
//             }).toList(),
//             onChanged: (PaymentMethod? value) async {
//               setState(() {
//                 selectedPaymentMethod = value!;
//                 selectedPaymentBrand = null;
//                 paymentBrands.clear();
//                 print(selectedPaymentMethod!.name);
//                 print(selectedPaymentMethod!.brands);
//                 if (selectedPaymentMethod!.brands.isNotEmpty) {
//                   paymentBrands = List.of(selectedPaymentMethod!.brands);
//                   if (paymentBrands.isNotEmpty) {
//                     selectedPaymentBrand = paymentBrands.first;
//                   }
//                 }
//               });
//             },
//           ),
//           if (paymentBrands.isNotEmpty)
//             DefaultDropdown1<Brand>(
//               borderRadius: 15,
//               verticalMargin: 0,
//               selectedValue: selectedPaymentBrand!,
//               items: paymentBrands.map((Brand paymentBrand) {
//                 return DropdownMenuItem<Brand>(
//                   value: paymentBrand,
//                   child: Text(
//                     paymentBrand.name,
//                     style: TextStyle(
//                       color: ThemeColors.dark,
//                       fontSize: h3,
//                     ),
//                   ),
//                 );
//               }).toList(),
//               onChanged: (Brand? value) async {
//                 setState(() {
//                   selectedPaymentBrand = value!;
//                 });
//               },
//             ),
//           DefaultButton(
//             text: "Check Out",
//             buttonColor: important_variables.projectName == "obriens"
//                 ? ThemeColors.secondaryDark
//                 : ThemeColors.primaryDark,
//             onPressed: () {
//               if (selectedDeliverMethod == 0 && selectedAddressId == 0) {
//                 defaultWarningDialog(context,
//                         "Please select a delivery address to continue.", () {})
//                     .show();
//               } else {
//                 makeOrder();
//                 // Navigator.push(
//                 //     context,
//                 //     MaterialPageRoute(
//                 //         builder: (BuildContext context) =>
//                 //             const CartConfirmation()));
//               }
//             },
//           ),
//         ],
//       ),
//     );
//   }

//   Widget emptyList() {
//     return RefreshIndicator(
//       color: ThemeColors.primaryDark,
//       onRefresh: () {
//         return Future.delayed(const Duration(seconds: 1), () {
//           setState(() {
//             future = getCart();
//           });
//         });
//       },
//       child: SizedBox(
//           width: phoneWidth,
//           height: phoneHeight,
//           child: SingleChildScrollView(
//             physics: AlwaysScrollableScrollPhysics(),
//             child: Column(
//               children: [
//                 SizedBox(height: phoneHeight / 4),
//                 Image.asset(
//                     "assets/icons/${boxGlobal.get("iconSet")}/Cart 01.png"),
//                 SizedBox(height: spacingHeightLarge),
//                 Text(
//                   "No items found",
//                   style: TextStyle(
//                     color: ThemeColors.dark,
//                     fontSize: h2,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//                 SizedBox(height: spacingHeightSmall),
//                 Text(
//                   "Look like you have not added\nanything to your cart.",
//                   textAlign: TextAlign.center,
//                   style: TextStyle(
//                       color: ThemeColors.dark,
//                       fontSize: h3),
//                 ),
//                 SizedBox(height: spacingHeightLarge),
//                 SizedBox(
//                   width: phoneWidth / 2,
//                   child: DefaultButton(
//                     text: "Browse",
//                     buttonColor: important_variables.projectName == "obriens"
//                         ? ThemeColors.secondaryDark
//                         : ThemeColors.primaryDark,
//                     onPressed: () {
//                       List<BottomBar> bottomBarList =
//                           boxGlobal.get("bottomBar") ?? [];
//                       if (bottomBarList.isNotEmpty) {
//                         int index = bottomBarList
//                             .indexWhere((element) => element.name == "ORDER");
//                         if (index != -1) {
//                           final dynamic navigationBar =
//                               barGlobalKey.currentWidget;
//                           navigationBar.onTap(index);
//                         }
//                       }
//                     },
//                   ),
//                 ),
//               ],
//             ),
//           )),
//     );
//   }

//   Widget guestView() {
//     return SizedBox(
//       width: phoneWidth,
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Icon(
//             Icons.warning_amber_outlined,
//             color: Colors.red,
//             size: 60,
//           ),
//           SizedBox(height: spacingHeightLarge),
//           Text(
//             "Hi, you're not logging in.",
//             style: TextStyle(
//               color: ThemeColors.dark,
//               fontSize: h2,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//           SizedBox(height: spacingHeightSmall),
//           Text(
//             "Please login or sign up to continue.",
//             textAlign: TextAlign.center,
//             style: TextStyle(
//                 color: ThemeColors.dark,
//                 fontSize: h3),
//           ),
//           SizedBox(height: spacingHeightLarge),
//           SizedBox(
//             width: phoneWidth / 2,
//             child: DefaultButton(
//               text: "Login",
//               buttonColor: ThemeColors.primaryDark,
//               onPressed: () {
//                 Navigator.of(context, rootNavigator: true)
//                     .pushNamed("/login/${loginAsset.template}");
//               },
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   void editItem(int itemIndex) {
//     if (orderMainTemplate == "1") {
//       Navigator.push(
//           context,
//           MaterialPageRoute(
//               builder: (BuildContext context) => OrderMenuDetailTemplate1(
//                     outlet: outlet!,
//                     product: cartList[itemIndex].product,
//                     recommended: [],
//                     inviteMsg: null,
//                     inviteUrl: null,
//                     tag: "Cart/${cartList[itemIndex].id}",
//                     editMode: true,
//                     cartItemId: cartList[itemIndex].id,
//                     quantity: cartList[itemIndex].quantity,
//                     remark: cartList[itemIndex].remark,
//                   ))).then((data) {
//         if (data != null) {
//           setState(() {
//             cartList = (data['data']['items'] as List)
//                 .map((data) => Cart.fromJson(data))
//                 .toList();
//             freeItem = data['data']['free_item'] == null
//                 ? null
//                 : Product.fromJson(data['data']['free_item']);
//             subtotal = data['data']['subtotal'] ?? "0.00";
//             discount = data['data']['discount'] ?? "0.00";
//             tax = data['data']['tax'] ?? "0.00";
//             serviceTax = data['data']['service_tax'] ?? "0.00";
//             total = data['data']['total'];

//             if (data['data']['voucher_error'] == true) {
//               selectedVoucherIndex = "";

//               final snackBar = SnackBar(
//                 content: Text(
//                     data['data']['voucher_message'] ?? "Something went error."),
//                 backgroundColor: Colors.red,
//                 behavior: SnackBarBehavior.floating,
//                 margin: EdgeInsets.all(defaultPadding),
//               );
//               ScaffoldMessenger.of(context).showSnackBar(snackBar);
//             }
//           });
//         }
//       });
//     }
//   }

//   void updateCart(
//       int? id,
//       int? quantity,
//       String? remark,
//       int? voucherId,
//       List<BundleCategory>? productBundle,
//       List<ModifierCategory>? productModifier) async {
//     EasyLoading.show();
//     ApiPost apiPost = ApiPost();

//     // bundle //
//     List<int> bundleItemId = [];
//     if (productBundle != null) {
//       for (int a = 0; a < productBundle.length; a++) {
//         for (int b = 0; b < productBundle[a].bundleItems.length; b++) {
//           if (productBundle[a].bundleItems[b].isSelected == 1) {
//             bundleItemId.add(productBundle[a].bundleItems[b].id);
//           }
//         }
//       }
//     }

//     // modifier //
//     List<int> modifierItemId = [];
//     if (productModifier != null) {
//       for (int a = 0; a < productModifier.length; a++) {
//         for (int b = 0; b < productModifier[a].modifierItems.length; b++) {
//           if (productModifier[a].modifierItems[b].isSelected == 1) {
//             modifierItemId.add(productModifier[a].modifierItems[b].id);
//           }
//         }
//       }
//     }

//     Map? data = await apiPost.updateCart(outlet!.id, id, quantity, remark,
//         voucherId, bundleItemId, modifierItemId);
//     EasyLoading.dismiss();

//     if (data != null) {
//       if (data['code'] == 200) {
//         setState(() {
//           cartList = (data['data']['items'] as List)
//               .map((data) => Cart.fromJson(data))
//               .toList();
//           freeItem = data['data']['free_item'] == null
//               ? null
//               : Product.fromJson(data['data']['free_item']);
//           subtotal = data['data']['subtotal'] ?? "0.00";
//           discount = data['data']['discount'] ?? "0.00";
//           tax = data['data']['tax'] ?? "0.00";
//           serviceTax = data['data']['service_tax'] ?? "0.00";
//           total = data['data']['total'];

//           if (data['data']['voucher_error'] == true) {
//             selectedVoucherIndex = "";

//             final snackBar = SnackBar(
//               content: Text(
//                   data['data']['voucher_message'] ?? "Something went error."),
//               backgroundColor: Colors.red,
//               behavior: SnackBarBehavior.floating,
//               margin: EdgeInsets.all(defaultPadding),
//             );
//             ScaffoldMessenger.of(context).showSnackBar(snackBar);
//           }

//           int quantity = 0;
//           for (Cart each in cartList) quantity += each.quantity;
//           cartQuantity.value = quantity;
//         });
//       } else {
//         defaultDialog(context, data['code'], data['message'], () {
//           if (voucherId != null) selectedVoucherIndex = "";
//         }).show();
//       }
//     } else {
//       defaultErrorDialog(context).show();
//     }
//   }

//   void deleteCart(int id, int? voucherId) async {
//     EasyLoading.show();
//     ApiPost apiPost = ApiPost();
//     Map? data = await apiPost.deleteCart(id, voucherId);
//     EasyLoading.dismiss();

//     print("call this?");

//     if (data != null) {
//       if (data['code'] == 200) {
//         setState(() {
//           cartList = (data['data']['items'] as List)
//               .map((data) => Cart.fromJson(data))
//               .toList();
//           freeItem = data['data']['free_item'] == null
//               ? null
//               : Product.fromJson(data['data']['free_item']);
//           subtotal = data['data']['subtotal'] ?? "0.00";
//           discount = data['data']['discount'] ?? "0.00";
//           tax = data['data']['tax'] ?? "0.00";
//           serviceTax = data['data']['service_tax'] ?? "0.00";
//           total = data['data']['total'];

//           if (data['data']['voucher_error'] == true) {
//             selectedVoucherIndex = "";

//             final snackBar = SnackBar(
//               content: Text(
//                   data['data']['voucher_message'] ?? "Something went error."),
//               backgroundColor: Colors.red,
//               behavior: SnackBarBehavior.floating,
//               margin: EdgeInsets.all(defaultPadding),
//             );
//             ScaffoldMessenger.of(context).showSnackBar(snackBar);
//           }

//           int quantity = 0;
//           for (Cart each in cartList) quantity += each.quantity;
//           cartQuantity.value = quantity;
//         });
//       } else {
//         defaultDialog(context, data['code'], data['message'], () {
//           setState(() {
//             future = getCart();
//           });
//         }).show();
//       }
//     } else {
//       defaultErrorDialog(context).show();
//     }
//   }

//   void makeOrder() async {
//     EasyLoading.show();

//     List<int> cartId = [];
//     for (Cart each in cartList) cartId.add(each.id);

//     int? voucherId;
//     if (selectedVoucherIndex != "")
//       voucherId = voucherList[int.parse(selectedVoucherIndex)].pivot!.voucherId;

//     ApiPost apiPost = ApiPost();
//     Map? data = await apiPost.makeOrder(
//         cartId,
//         voucherId,
//         selectedPaymentMethod!.code,
//         selectedPaymentBrand != null ? selectedPaymentBrand!.code : null,
//         selectedDeliverMethod == 0 ? "DELIVERY" : "SELF_PICKUP",
//         selectedSchedule,
//         double.parse(creditController.text),
//         selectedDeliverMethod == 0 ? selectedAddressId : null,
//         remarkController.text);
//     EasyLoading.dismiss();

//     if (data != null) {
//       if (data['code'] == 200) {
//         String? paymentUrl = data['data']['payment_url'] ?? null;
//         List<dynamic>? eghl_request = data['data']['eghl_request'];

//         //todo now only support all payment gateway that have payment_url, and EGHL
//         if (paymentUrl != null) {
//           Navigator.push(
//               context,
//               MaterialPageRoute(
//                 builder: (BuildContext context) => WebView(
//                   url: data['data']['payment_url'],
//                 ),
//               )).then((value) async {
//             navigateToOrderHistory();
//           });
//         } else if (eghl_request != null) {
//           EghlPayment eghl = EghlPayment.fromJson(data['data']['eghl_request']);
//           if (Platform.isIOS) {
//             try {
//               await CartPage.iosPlatform.invokeMethod('eGHL', <String, dynamic>{
//                 'transactionType': eghl.transactionType ?? "",
//                 'paymentMethod': eghl.paymentMethod ?? "",
//                 'serviceId': eghl.serviceId ?? "",
//                 'merchantPass': eghl.merchantPass ?? "",
//                 'paymentId': eghl.paymentId ?? "",
//                 'orderNumber': eghl.orderNumber ?? "",
//                 'paymentDesc': eghl.paymentDesc ?? "",
//                 'merchantName': eghl.merchantName ?? "",
//                 'merchantCallBackUrl': eghl.merchantCallBackUrl ?? "",
//                 'amount': eghl.amount ?? "",
//                 'currencyCode': eghl.currencyCode ?? "",
//                 'custIp': eghl.custIp ?? "",
//                 'custName': eghl.custName ?? "",
//                 'custEmail': eghl.custEmail ?? "",
//                 'custPhone': eghl.custPhone ?? "",
//                 'languageCode': eghl.languageCode ?? "",
//                 'paymentGateway': eghl.paymentGateway ?? "",
//               }).then((value) {
//                 if (value["txnStatus"].toString() == "0") {
//                   defaultDialog(context, 200, value['txnMessage'], () {
//                     navigateToOrderHistory();
//                   }).show();
//                 } else if (value["txnStatus"].toString() == "1") {
//                   defaultDialog(context, 400, value['txnMessage'], () {
//                     navigateToOrderHistory();
//                   }).show();
//                 } else {
//                   defaultWarningDialog(
//                       context,
//                       value['txnMessage'] == ""
//                           ? "Something went wrong. Please try again."
//                           : value['txnMessage'], () {
//                     navigateToOrderHistory();
//                   }).show();
//                 }
//               });
//             } catch (e) {
//               print(e);
//             }
//           } else {
//             try {
//               await CartPage.androidPlatform
//                   .invokeMethod('eGHL', <String, dynamic>{
//                 'transactionType': eghl.transactionType ?? "",
//                 'paymentMethod': eghl.paymentMethod ?? "",
//                 'serviceId': eghl.serviceId ?? "",
//                 'merchantPass': eghl.merchantPass ?? "",
//                 'paymentId': eghl.paymentId ?? "",
//                 'orderNumber': eghl.orderNumber ?? "",
//                 'paymentDesc': eghl.paymentDesc ?? "",
//                 'merchantName': eghl.merchantName ?? "",
//                 'merchantCallBackUrl': eghl.merchantCallBackUrl ?? "",
//                 'amount': eghl.amount ?? "",
//                 'currencyCode': eghl.currencyCode ?? "",
//                 'custIp': eghl.custIp ?? "",
//                 'custName': eghl.custName ?? "",
//                 'custEmail': eghl.custEmail ?? "",
//                 'custPhone': eghl.custPhone ?? "",
//                 'languageCode': eghl.languageCode ?? "",
//                 'paymentGateway': eghl.paymentGateway ?? "",
//               }).then((value) {
//                 defaultDialog(context, 200, value, () {
//                   navigateToOrderHistory();
//                 }).show();
//               });
//             } on PlatformException catch (e) {
//               if (e.code == "failed") {
//                 defaultDialog(context, 400, e.message!, () {
//                   navigateToOrderHistory();
//                 }).show();
//               } else {
//                 defaultWarningDialog(context,
//                     e.message ?? "Something went wrong. Please try again.", () {
//                   navigateToOrderHistory();
//                 }).show();
//               }
//             } catch (e) {
//               print(e);
//             }
//           }
//         } else {
//           navigateToOrderHistory();
//         }
//       } else {
//         defaultDialog(context, data['code'], data['message'], () {}).show();
//       }
//     } else {
//       defaultErrorDialog(context).show();
//     }
//   }

//   Future<Map?> getCart() async {
//     ApiGet apiGet = ApiGet();
//     boxMenu = await Hive.openBox('boxMenu');
//     outlet = await boxMenu.get("selected_outlet");
//     selectedSchedule = await boxMenu.get("selected_schedule") ?? "";
//     selectedDeliverMethod = await boxMenu.get("selected_deliver_method");
//     if (selectedAddressId == 0 && selectedDeliverMethod == 0) {
//       Address? selectedAddress = await boxMenu.get("selected_address") ?? null;
//       selectedAddressId = selectedAddress?.id ?? 0;
//     }

//     freeItem = null;
//     selectedVoucherIndex = "";

//     Map? data = await apiGet.cartList(outlet!.id);

//     if (data != null && data['data'] != null) {
//       cartList = (data['data']['items'] as List)
//           .map((data) => Cart.fromJson(data))
//           .toList();
//       subtotal = data['data']['subtotal'] ?? "0.00";
//       discount = data['data']['discount'] ?? "0.00";
//       tax = data['data']['tax'] ?? "0.00";
//       serviceTax = data['data']['service_tax'] ?? "0.00";
//       total = data['data']['total'];
//       actualTotal = total;

//       creditAvailable = double.parse(data['data']['credit_available']);
//       creditUsable = double.parse(data['data']['credit_usable']);

//       creditController.text = creditUsable > creditAvailable
//           ? creditAvailable.toString()
//           : creditUsable.toString();

//       actualTotal =
//           (Decimal.parse(total) - Decimal.parse(creditController.text))
//               .toString();

//       voucherList = (data['data']['vouchers'] as List)
//           .map((data) => Voucher.fromJson(data))
//           .toList();

//       addressList = (data['data']['addresses'] as List)
//           .map((data) => Address.fromJson(data))
//           .toList();

//       if (selectedAddressId == 0) {
//         Address? defaultAddress = data['data']['default_address'] == null
//             ? null
//             : Address.fromJson(data['data']['default_address']);
//         if (defaultAddress != null) {
//           selectedAddressId = defaultAddress.id;
//         } else {
//           if (addressList.isNotEmpty) selectedAddressId = addressList.first.id;
//         }
//       } else {
//         if (addressList.isEmpty) selectedAddressId = 0;
//       }

//       schedule = () {
//         if (selectedDeliverMethod == 0) {
//           return (data['data']['delivery_hours'] as List)
//               .map((data) => data as String)
//               .toList();
//         } else {
//           return (data['data']['pickup_hours'] as List)
//               .map((data) => data as String)
//               .toList();
//         }
//       }();
//       schedule.insert(0, "ASAP");
//       if (schedule.isNotEmpty && selectedSchedule == "") {
//         selectedSchedule = schedule.first;
//       }

//       paymentMethods = (data['data']['payment_methods'] as List)
//           .map((data) => PaymentMethod.fromJson(data))
//           .toList();

//       if (paymentMethods.isNotEmpty) {
//         selectedPaymentMethod = paymentMethods.first;
//         print(selectedPaymentMethod!.name);
//         if (selectedPaymentMethod!.brands.isNotEmpty) {
//           paymentBrands = List.of(selectedPaymentMethod!.brands);
//           if (paymentBrands.isNotEmpty) {
//             selectedPaymentBrand = paymentBrands.first;
//           }
//         }
//       }
//     }
//     return data;
//   }

//   navigateToOrderHistory() {
//     Navigator.popAndPushNamed(context,
//         "/order_history/${accountAsset.firstWhereOrNull((element) => element.sectionName == "ORDER_HISTORY")?.template ?? ""}",
//         arguments: {
//           "theme": accountAsset
//                   .firstWhereOrNull(
//                       (element) => element.sectionName == "ORDER_HISTORY")
//                   ?.theme ??
//               "L",
//         });
//   }

//   FutureOr onRefresh(dynamic value) {
//     setState(() {
//       future = getCart();
//     });
//   }
// }
