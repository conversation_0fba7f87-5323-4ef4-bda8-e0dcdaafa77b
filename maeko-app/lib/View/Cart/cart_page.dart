import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/address.dart';
import 'package:amverton/Model/cart/cart.dart';
import 'package:amverton/Model/cart/cart_item.dart';
import 'package:amverton/Model/cart/cart_page_variable.dart';
import 'package:amverton/Model/cart/free_item.dart';
import 'package:amverton/Model/eghl_payment.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Model/product.dart';
import 'package:amverton/Notifier/notifiers.dart';
import 'package:amverton/Provider/providers.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Cart/Section/cart_template1.dart';
import 'package:amverton/View/Cart/Section/cart_template2.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Home/web_view.dart';
import 'package:amverton/View/OrderMenu/order_menu_detail_template1.dart';
import 'package:amverton/View/OrderMenu/order_menu_page.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:collection/collection.dart';

late CartNotifier cartNotifier;

class CartPage extends ConsumerStatefulWidget {
  const CartPage({super.key});
  static const androidPlatform = const MethodChannel('fnb.android');
  static const iosPlatform = const MethodChannel('fnb.ios');

  @override
  _CartPageState createState() => _CartPageState();
}

class _CartPageState extends ConsumerState<CartPage> {
  Outlet? outlet;
  Cart? cart;
  CartPageVariable? cartPageVariable;
  late Future future;
  late Box boxGlobal;

  @override
  void initState() {
    super.initState();
    boxGlobal = Hive.box('boxGlobal');
    future = getCart();
  }

  Widget _getTemplate() {
    int selectedTemplate = 2;

    switch (selectedTemplate) {
      case 1:
        return CartTemplate1(
          editItem: editItem,
          getCart: getCart,
          updateCart: updateCart,
          deleteCart: deleteCart,
          makeOrder: makeOrder,
          onRefresh: onRefresh,
        );
      case 2:
        return CartTemplate2(
          editItem: editItem,
          getCart: getCart,
          updateCart: updateCart,
          deleteCart: deleteCart,
          makeOrder: makeOrder,
          onRefresh: onRefresh,
        );
      default:
        return Container(
          child: Center(
            child: Text("default"),
          ),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    cart = ref.watch(cartProvider);
    cartNotifier = ref.read(cartProvider.notifier);
    cartPageVariable = ref.watch(cartPageVariableProvider);

    return FutureBuilder(
      future: future,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          return Scaffold(
            backgroundColor: ThemeColors.light,
            appBar: AppBar(title: const Text("Cart")),
            body: _getTemplate(),
          );
        }
        return Scaffold(
          backgroundColor: ThemeColors.light,
          appBar: AppBar(title: const Text("Cart")),
          body: Container(),
        );
      },
    );
  }

  void editItem(int itemIndex) {
    if (orderMainTemplate == "1") {
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (BuildContext context) => OrderMenuDetailTemplate1(
                    outlet: outlet!,
                    product: cart!.items[itemIndex].product,
                    recommended: [],
                    inviteMsg: null,
                    inviteUrl: null,
                    tag: "Cart/${cart!.items[itemIndex].id}",
                    editMode: true,
                    cartItemId: cart!.items[itemIndex].id,
                    quantity: cart!.items[itemIndex].quantity,
                    remark: cart!.items[itemIndex].remark,
                  ))).then((data) {
        if (data != null) {
          setState(() {
            cartNotifier.updateItem((data['data']['items'] as List)
                .map((data) => CartItem.fromJson(data))
                .toList());

            cart!.freeItem = data['data']['free_item'] == null
                ? null
                : FreeItem.fromJson(data['data']['free_item']);
            cart!.subtotal = data['data']['subtotal'] ?? "0.00";
            cart!.discount = data['data']['discount'] ?? "0.00";
            cart!.tax = data['data']['tax'] ?? "0.00";
            cart!.serviceTax = data['data']['service_tax'] ?? "0.00";
            cart!.total = data['data']['total'];
            cartNotifier.update(cart!);

            if (data['data']['voucher_error'] == true) {
              cartPageVariable!.selectedVoucherIndex = "";

              final snackBar = SnackBar(
                content: Text(
                    data['data']['voucher_message'] ?? "Something went error."),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
                margin: EdgeInsets.all(defaultPadding),
              );
              ScaffoldMessenger.of(context).showSnackBar(snackBar);
            }
          });
        }
      });
    }
  }

  void updateCart(
    int? id,
    int? quantity,
    String? remark,
    int? voucherId,
    List<BundleCategory>? productBundle,
    List<ModifierCategory>? productModifier,
  ) async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    // bundle //
    List<int> bundleItemId = [];
    if (productBundle != null) {
      for (int a = 0; a < productBundle.length; a++) {
        for (int b = 0; b < productBundle[a].bundleItems.length; b++) {
          if (productBundle[a].bundleItems[b].isSelected == 1) {
            bundleItemId.add(productBundle[a].bundleItems[b].id);
          }
        }
      }
    }

    // modifier //
    List<int> modifierItemId = [];
    if (productModifier != null) {
      for (int a = 0; a < productModifier.length; a++) {
        for (int b = 0; b < productModifier[a].modifierItems.length; b++) {
          if (productModifier[a].modifierItems[b].isSelected == 1) {
            modifierItemId.add(productModifier[a].modifierItems[b].id);
          }
        }
      }
    }

    Map? data = await apiPost.updateCart(outlet!.id, id, quantity, remark,
        voucherId, bundleItemId, modifierItemId);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        setState(() {
          Cart cart = Cart.fromJson(data['data']);
          cartPageVariable!.actualTotal = (Decimal.parse(cart.total) -
                  Decimal.parse(cartPageVariable!.pointUsed.toString()))
              .toString();

          cartNotifier.update(cart);

          if (data['data']['voucher_error'] == true) {
            cartPageVariable!.selectedVoucherIndex = "";

            final snackBar = SnackBar(
              content: Text(
                  data['data']['voucher_message'] ?? "Something went error."),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              margin: EdgeInsets.all(defaultPadding),
            );
            ScaffoldMessenger.of(context).showSnackBar(snackBar);
          }

          int quantity = 0;
          for (CartItem each in cart!.items) quantity += each.quantity;
          cartQuantity.value = quantity;
        });
      } else {
        defaultDialog(context, data['code'], data['message'], () {
          if (voucherId != null) cartPageVariable!.selectedVoucherIndex = "";
        }).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  void deleteCart(int id, int? voucherId) async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    Map? data = await apiPost.deleteCart(id, voucherId);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        setState(() {
          Cart cart = Cart.fromJson(data['data']);
          cartNotifier.update(cart);

          if (data['data']['voucher_error'] == true) {
            cartPageVariable!.selectedVoucherIndex = "";

            final snackBar = SnackBar(
              content: Text(
                  data['data']['voucher_message'] ?? "Something went error."),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              margin: EdgeInsets.all(defaultPadding),
            );
            ScaffoldMessenger.of(context).showSnackBar(snackBar);
          }

          int quantity = 0;
          for (CartItem each in cart!.items) quantity += each.quantity;
          cartQuantity.value = quantity;
        });
      } else {
        defaultDialog(context, data['code'], data['message'], () {
          setState(() {
            future = getCart();
          });
        }).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  void makeOrder(String credit, String? remark) async {
    EasyLoading.show();

    List<int> cartId = [];
    for (CartItem each in cart!.items) cartId.add(each.id);

    int? voucherId;
    if (cartPageVariable!.selectedVoucherIndex != "")
      voucherId =
          cart!.vouchers[int.parse(cartPageVariable!.selectedVoucherIndex)].id;

    ApiPost apiPost = ApiPost();
    Map? data = await apiPost.makeOrder(
        cartId,
        voucherId,
        cartPageVariable!.selectedPaymentMethod!.code,
        cartPageVariable!.selectedPaymentBrand != null
            ? cartPageVariable!.selectedPaymentBrand!.code
            : null,
        cartPageVariable!.selectedDeliverMethod == 0
            ? "DELIVERY"
            : "SELF_PICKUP",
        cartPageVariable!.selectedSchedule,
        cart!.checkoutWith == "CREDIT" ? double.parse(credit) : 0.00,
        cart!.checkoutWith == "POINT" ? double.parse(credit) : 0.00,
        cartPageVariable!.selectedDeliverMethod == 0
            ? cartPageVariable!.selectedAddressId
            : null,
        remark);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        String? paymentUrl = data['data']['payment_url'] ?? null;
        Map? eghl_request = data['data']['eghl_request'];

        cartQuantity.value = 0;
        //todo now only support all payment gateway that have payment_url, and EGHL
        if (paymentUrl != null) {
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (BuildContext context) => WebView(
                  url: data['data']['payment_url'],
                ),
              )).then((value) async {
            navigateToOrderHistory();
          });
        } else if (eghl_request != null) {
          EghlPayment eghl = EghlPayment.fromJson(data['data']['eghl_request']);
          if (Platform.isIOS) {
            try {
              await CartPage.iosPlatform.invokeMethod('eGHL', <String, dynamic>{
                'transactionType': eghl.transactionType ?? "",
                'paymentMethod': eghl.paymentMethod ?? "",
                'serviceId': eghl.serviceId ?? "",
                'merchantPass': eghl.merchantPass ?? "",
                'paymentId': eghl.paymentId ?? "",
                'orderNumber': eghl.orderNumber ?? "",
                'paymentDesc': eghl.paymentDesc ?? "",
                'merchantName': eghl.merchantName ?? "",
                'merchantCallBackUrl': eghl.merchantCallBackUrl ?? "",
                'amount': eghl.amount ?? "",
                'currencyCode': eghl.currencyCode ?? "",
                'custIp': eghl.custIp ?? "",
                'custName': eghl.custName ?? "",
                'custEmail': eghl.custEmail ?? "",
                'custPhone': eghl.custPhone ?? "",
                'languageCode': eghl.languageCode ?? "",
                'paymentGateway': eghl.paymentGateway ?? "",
              }).then((value) {
                if (value["txnStatus"].toString() == "0") {
                  defaultDialog(context, 200, value['txnMessage'], () {
                    navigateToOrderHistory();
                  }).show();
                } else if (value["txnStatus"].toString() == "1") {
                  defaultDialog(context, 400, value['txnMessage'], () {
                    navigateToOrderHistory();
                  }).show();
                } else {
                  defaultWarningDialog(
                      context,
                      value['txnMessage'] == ""
                          ? "Something went wrong. Please try again."
                          : value['txnMessage'], () {
                    navigateToOrderHistory();
                  }).show();
                }
              });
            } catch (e) {
              print(e);
            }
          } else {
            try {
              await CartPage.androidPlatform
                  .invokeMethod('eGHL', <String, dynamic>{
                'transactionType': eghl.transactionType ?? "",
                'paymentMethod': eghl.paymentMethod ?? "",
                'serviceId': eghl.serviceId ?? "",
                'merchantPass': eghl.merchantPass ?? "",
                'paymentId': eghl.paymentId ?? "",
                'orderNumber': eghl.orderNumber ?? "",
                'paymentDesc': eghl.paymentDesc ?? "",
                'merchantName': eghl.merchantName ?? "",
                'merchantCallBackUrl': eghl.merchantCallBackUrl ?? "",
                'amount': eghl.amount ?? "",
                'currencyCode': eghl.currencyCode ?? "",
                'custIp': eghl.custIp ?? "",
                'custName': eghl.custName ?? "",
                'custEmail': eghl.custEmail ?? "",
                'custPhone': eghl.custPhone ?? "",
                'languageCode': eghl.languageCode ?? "",
                'paymentGateway': eghl.paymentGateway ?? "",
              }).then((value) {
                defaultDialog(context, 200, value, () {
                  navigateToOrderHistory();
                }).show();
              });
            } on PlatformException catch (e) {
              if (e.code == "failed") {
                defaultDialog(context, 400, e.message!, () {
                  navigateToOrderHistory();
                }).show();
              } else {
                defaultWarningDialog(context,
                    e.message ?? "Something went wrong. Please try again.", () {
                  navigateToOrderHistory();
                }).show();
              }
            } catch (e) {
              print(e);
            }
          }
        } else {
          navigateToOrderHistory();
        }
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  Future<Map?> getCart() async {
    ApiGet apiGet = ApiGet();
    Box boxMenu = Hive.box('boxMenu');
    outlet = boxMenu.get("selected_outlet");

    Map? data = await apiGet.cartList(outlet!.id);

    if (data != null && data['data'] != null) {
      cartPageVariable!.selectedSchedule =
          boxMenu.get("selected_schedule") ?? "";
      cartPageVariable!.selectedDeliverMethod =
          boxMenu.get("selected_deliver_method");
      if (cartPageVariable!.selectedAddressId == 0 &&
          cartPageVariable!.selectedDeliverMethod == 0) {
        Address? selectedAddress = boxMenu.get("selected_address") ?? null;
        cartPageVariable!.selectedAddressId = selectedAddress?.id ?? 0;
      }

      Cart cart = Cart.fromJson(data['data']);
      cartNotifier.update(cart);
      cartPageVariable!.actualTotal = cart.total;

      //tenplate 2 didnt use this
      cartPageVariable!.pointType = data['data']['checkout_with'];
      if (cartPageVariable!.pointType == "CREDIT") {
        cartPageVariable!.pointAvailable =
            double.parse(data['data']['credit_available']);
        cartPageVariable!.pointUsable =
            double.parse(data['data']['credit_usable']);
      } else {
        //point
        cartPageVariable!.pointAvailable =
            double.parse(data['data']['point_available']);
        cartPageVariable!.pointUsable =
            double.parse(data['data']['point_usable']);
      }

      cartPageVariable!.pointUsed =
          cartPageVariable!.pointUsable > cartPageVariable!.pointAvailable
              ? cartPageVariable!.pointAvailable.toString()
              : cartPageVariable!.pointUsable.toString();

      cartPageVariable!.actualTotal = (Decimal.parse(cart.total) -
              Decimal.parse(cartPageVariable!.pointUsed.toString()))
          .toString();

      cartPageVariable!.addressList = cart.addresses;

      if (cartPageVariable!.selectedAddressId == 0) {
        Address? defaultAddress = data['data']['default_address'] == null
            ? null
            : Address.fromJson(data['data']['default_address']);
        if (defaultAddress != null) {
          cartPageVariable!.selectedAddressId = defaultAddress.id;
        } else {
          if (cartPageVariable!.addressList.isNotEmpty)
            cartPageVariable!.selectedAddressId =
                cartPageVariable!.addressList.first.id;
        }
      } else {
        if (cartPageVariable!.addressList.isEmpty)
          cartPageVariable!.selectedAddressId = 0;
      }

      cartPageVariable!.schedules = () {
        if (cartPageVariable!.selectedDeliverMethod == 0) {
          return (data['data']['delivery_hours'] as List)
              .map((data) => data as String)
              .toList();
        } else {
          return (data['data']['pickup_hours'] as List)
              .map((data) => data as String)
              .toList();
        }
      }();
      cartPageVariable!.schedules.insert(0, "ASAP");
      if (cartPageVariable!.schedules.isNotEmpty &&
          cartPageVariable!.selectedSchedule == "") {
        cartPageVariable!.selectedSchedule = cartPageVariable!.schedules.first;
      }
    }
    return data;
  }

  navigateToOrderHistory() {
    Navigator.popAndPushNamed(
        context,
        //todo get from asset
        // "/order_history/${accountAsset.firstWhereOrNull((element) => element.sectionName == "ORDER_HISTORY")?.template ?? ""}",
        "/order_history/2",
        arguments: {
          "theme": accountAsset
                  .firstWhereOrNull(
                      (element) => element.sectionName == "ORDER_HISTORY")
                  ?.theme ??
              "L",
        });
  }

  onRefresh() {
    setState(() {
      getCart();
    });
  }
}
