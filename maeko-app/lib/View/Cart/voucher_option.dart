import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/cart/voucher.dart';
import 'package:amverton/View/Custom%20Widgets/voucher_card.dart';
import 'package:hive/hive.dart';

class VoucherOption extends StatefulWidget {
  // final List<Voucher> voucherList;
  final List<Voucher> voucherList;
  String selectedVoucherIndex;

  VoucherOption({
    Key? key,
    required this.voucherList,
    required this.selectedVoucherIndex,
  }) : super(key: key);

  @override
  _VoucherOptionState createState() => _VoucherOptionState();
}

class _VoucherOptionState extends State<VoucherOption> {
  Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeColors.light,
      appBar: AppBar(title: const Text("My Voucher")),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(
              left: defaultPadding,
              right: defaultPadding,
              top: defaultPadding,
            ),
            child: Text(
              "My Voucher",
              style: TextStyle(
                fontSize: h2,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.all(defaultPadding),
              itemCount: widget.voucherList.length,
              itemBuilder: (BuildContext context, int index) {
                Voucher voucher = widget.voucherList[index];
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      widget.selectedVoucherIndex = index.toString();
                    });
                    Navigator.pop(context, [index.toString()]);
                  },
                  child: VoucherCard(
                    imageUrl: voucher.image,
                    title: voucher.title,
                    endon: voucher.endOn.toString(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
