import 'package:cached_network_image/cached_network_image.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/app_settings.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/user.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Auth/Login/social_icon2.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_phonefield1.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
import 'package:amverton/main.dart';
import 'package:hive/hive.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class StaffLoginTemplate1 extends StatefulWidget {
  static String routeName = "/staff_login/1";

  const StaffLoginTemplate1({Key? key}) : super(key: key);

  @override
  _StaffLoginTemplate1State createState() => _StaffLoginTemplate1State();
}

class _StaffLoginTemplate1State extends State<StaffLoginTemplate1> {
  late Future future;
  late Box boxGlobal;
  String theme = loginAsset.theme;

  String selectedCountryCode = "";
  List<String> countryCodeList = [];
  TextEditingController usernameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  AppSettings? appSettings;

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    countryCodeList = boxGlobal.get("countryCode") ?? [];
    if (countryCodeList.isNotEmpty) selectedCountryCode = countryCodeList.first;

    appSettings = boxGlobal.get("appSettings") ?? null;

    // todo temp hardcode for obriens
    return await Hive.openBox<Asset>('LOGIN+MAIN+1');
    // return await Hive.openBox<Asset>('LOGIN+MAIN+3');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                extendBodyBehindAppBar: true,
                appBar: AppBar(
                  iconTheme: IconThemeData(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                  title: Text("Login or sign up"),
                ),
                body: Container(
                  height: phoneHeight,
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  child: SingleChildScrollView(
                    physics: const ClampingScrollPhysics(),
                    child: Container(
                      height: MediaQuery.of(context).size.height,
                      padding: EdgeInsets.fromLTRB(
                        defaultPadding,
                        topPaddingWithoutBar,
                        defaultPadding,
                        verticalPaddingSmall,
                      ),
                      child: Column(
                        children: [
                          SizedBox(height: phoneHeight * 0.09),
                          Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: horizontalPaddingMedium),
                            child: CachedNetworkImage(
                              imageUrl: list.values
                                      .firstWhereOrNull(
                                          (element) => element.name == "IMAGE")
                                      ?.data ??
                                  "",
                              height: phoneHeight / 5,
                              fit: BoxFit.fitWidth,
                              placeholder: (context, url) {
                                return Center(
                                  child: CircularProgressIndicator(
                                    color: ThemeColors.primaryDark,
                                  ),
                                );
                              },
                              errorWidget: (context, url, error) {
                                return Icon(
                                  Icons.error_outline_outlined,
                                  color: ThemeColors.primaryDark,
                                );
                              },
                            ),
                          ),
                          SizedBox(height: phoneHeight * 0.06),
                          Text(
                            // todo temp hardcode for obriens (should take from assets, but merchant portal dont hv this template yet)
                            "Enter Your Credential",
                            // list.values
                            //         .firstWhereOrNull(
                            //             (element) => element.name == "TITLE")
                            //         ?.data ??
                            //     "",
                            style: TextStyle(
                              color: theme == "L"
                                  ? ThemeColors.dark
                                  : ThemeColors.light,
                              fontSize: h3,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: spacingHeightSmall),
                          DefaultTextField1(
                            controller: usernameController,
                            obscureText: false,
                            hintText: "Username",
                          ),
                          DefaultTextField1(
                            controller: passwordController,
                            obscureText: true,
                            hintText: "Password",
                          ),
                          SizedBox(height: spacingHeightLarge),
                          DefaultButton(
                            text: "Login",
                            buttonColor: ThemeColors.primaryDark,
                            borderRadius: 15,
                            onPressed: () {
                              setState(() {
                                loginAsStaff();
                              });
                            },
                          ),
                          const Spacer(),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "Version $versionNumber",
                                style: TextStyle(
                                    color: theme == "L"
                                        ? ThemeColors.dark
                                        : ThemeColors.light,
                                    fontSize: h5),
                              ),
                              if (important_variables.projectName != "sbk")
                                GestureDetector(
                                  onTap: () async {
                                    Uri value = Uri.parse(
                                        "https://digitalise.invoke.my/");

                                    if (await canLaunchUrl(value)) {
                                      await launchUrl(value);
                                    } else {
                                      print("failed to open");
                                    }
                                  },
                                  child: Text(
                                    important_variables.powerBy,
                                    style: TextStyle(
                                      color: Colors.blue,
                                      fontSize: h5,
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }

  void loginAsStaff() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    Map? data = await apiPost.loginAsStaff(
        usernameController.text, passwordController.text);
    print(data);
    if (data != null) {
      if (data['code'] == 200) {
        await getMe();
        EasyLoading.dismiss();
        defaultDialog(context, data['code'], data['message'], () {
          Navigator.pushNamedAndRemoveUntil(
            context,
            "/my_home_page",
            (Route<dynamic> route) => false,
            arguments: {'dailyPopUp': true},
          );
        }).show();
      } else {
        EasyLoading.dismiss();
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      EasyLoading.dismiss();
      defaultErrorDialog(context).show();
    }
  }

  Future<Map?> getMe() async {
    Box box = await Hive.openBox("box");
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.me();
    print(data);

    if (data != null && data['data'] != null) {
      User user = User.fromJson(data['data']['me']);
      box.put("user", user);

      String barcode = data['data']['barcode'];
      box.put("barcode", barcode);

      int totalDaysCheckIn =
          data['data']['check_in']?['total_days_checked_in'] ?? 0;
      box.put("total_days_checked_in", totalDaysCheckIn);
    }
    return data;
  }
}
