import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_size.dart';

class SocialIcon2 extends StatelessWidget {
  final String text;
  final Color? textColor; // default white
  final String iconImage;
  final Color? iconColor;
  final Color? buttonColor; // default white
  final double? borderRadius; // default 30
  final VoidCallback? onPressed; // default pop()

  const SocialIcon2({
    Key? key,
    required this.text,
    this.textColor,
    required this.iconImage,
    this.iconColor,
    this.buttonColor,
    this.borderRadius = 30,
    this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AbsorbPointer(
      absorbing: false,
      child: Container(
        margin: EdgeInsets.symmetric(vertical: defaultInnerPadding),
        width: double.infinity,
        height: buttonHeight,
        child: SizedBox.expand(
          child: GestureDetector(
            onTap: onPressed ?? () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: buttonColor ?? Colors.white,
                borderRadius: BorderRadius.circular(borderRadius!),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ImageIcon(
                    AssetImage(iconImage),
                    size: 22,
                    color: iconColor ?? Colors.white,
                  ),
                  SizedBox(width: spacingWidth),
                  Text(
                    text,
                    style: TextStyle(
                      fontSize: h2,
                      fontWeight: FontWeight.bold,
                      color: textColor ?? Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
