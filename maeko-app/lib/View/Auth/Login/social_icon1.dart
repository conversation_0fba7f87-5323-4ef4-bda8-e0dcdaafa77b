import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:amverton/Constant/theme_size.dart';

class SocialIcon1 extends StatelessWidget {
  final String image;
  final Color bgColor;
  final Function()? onTap;

  const SocialIcon1({
    Key? key,
    required this.image,
    required this.bgColor,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: onTap,
        child: Container(
          height: phoneWidth / 10,
          width: phoneWidth / 10,
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: bgColor,
          ),
          child: SvgPicture.asset(image, color: Colors.white),
        ));
  }
}
