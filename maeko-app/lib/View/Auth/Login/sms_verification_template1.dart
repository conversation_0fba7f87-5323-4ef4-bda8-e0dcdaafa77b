import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/user.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
import 'package:amverton/main.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class SMSVerificationTemplate1 extends StatefulWidget {
  static String routeName = "/sms_verification/1";
  final dynamic args;

  const SMSVerificationTemplate1({Key? key, this.args}) : super(key: key);

  @override
  _SMSVerificationTemplate1State createState() =>
      _SMSVerificationTemplate1State();
}

class _SMSVerificationTemplate1State extends State<SMSVerificationTemplate1> {
  late Future future;
  late Box boxGlobal;
  String theme = "L";

  late Timer timer;
  int timeLeft = 59;
  TextEditingController codeController = TextEditingController();
  String? phone;

  @override
  void initState() {
    super.initState();
    theme = widget.args["theme"];
    phone = widget.args["phone"];
    future = openBox();
    startTimer();
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('REGISTER+STEP_3+1');
  }

  @override
  void dispose() {
    super.dispose();
    try {
      timer.cancel();
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                extendBodyBehindAppBar: true,
                appBar: AppBar(
                  iconTheme: IconThemeData(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                  title: Text(
                    "SMS Verification",
                    style: TextStyle(
                      color:
                          theme == "L" ? ThemeColors.dark : ThemeColors.light,
                    ),
                  ),
                ),
                body: Container(
                  height: phoneHeight,
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  child: SingleChildScrollView(
                      physics: const ClampingScrollPhysics(),
                      child: Container(
                        height: MediaQuery.of(context).size.height,
                        padding: EdgeInsets.fromLTRB(
                          defaultPadding,
                          topPaddingWithoutBar,
                          defaultPadding,
                          verticalPaddingSmall,
                        ),
                        child: Column(
                          children: [
                            SizedBox(height: phoneHeight * 0.09),
                            Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: horizontalPaddingMedium),
                              child: Image.asset(
                                "assets/images/logo_banner.png",
                                height: phoneHeight / 5,
                                fit: BoxFit.fitWidth,
                              ),
                            ),
                            Text(
                              //todo take from asset
                              list.values
                                      .firstWhereOrNull((element) =>
                                          element.name == "HEADLINE")
                                      ?.data ??
                                  "",
                              // "Verification",
                              style: TextStyle(
                                  color: theme == "L"
                                      ? ThemeColors.dark
                                      : ThemeColors.light,
                                  fontSize: h3,
                                  fontWeight: FontWeight.bold),
                            ),
                            SizedBox(height: spacingHeightMedium),
                            Container(
                              margin: EdgeInsets.symmetric(
                                horizontal: horizontalPaddingMedium,
                              ),
                              child: Text(
                                //tod take from asset
                                list.values
                                        .firstWhereOrNull((element) =>
                                            element.name == "SUBHEADLINE")
                                        ?.data ??
                                    "",
                                // "Please enter the verification code that we have sent to your registered phone number",
                                style: TextStyle(
                                  color: theme == "L"
                                      ? ThemeColors.dark
                                      : ThemeColors.light,
                                  fontSize: h4,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            SizedBox(height: spacingHeightMedium),
                            DefaultTextField1(
                              controller: codeController,
                              keyBoardType: TextInputType.number,
                              hintText: "000000",
                            ),
                            SizedBox(height: spacingHeightSmall),
                            Align(
                              alignment: Alignment.center,
                              child: RichText(
                                text: TextSpan(
                                  style: TextStyle(
                                      color: theme == "L"
                                          ? ThemeColors.gray
                                          : ThemeColors.disabled,
                                      fontSize: h3),
                                  children: [
                                    TextSpan(
                                      text:
                                          "${list.values.firstWhereOrNull((element) => element.name == "UNDELIVERED_TEXT")?.data ?? ""} ",
                                    ),
                                    TextSpan(
                                      text: timeLeft == 0
                                          ? list.values
                                                  .firstWhereOrNull((element) =>
                                                      element.name ==
                                                      "RESEND_BUTTON_TEXT")
                                                  ?.data ??
                                              ""
                                          : '00:${timeLeft.toString().padLeft(2, '0')}',
                                      style: TextStyle(
                                          color: ThemeColors.primaryDark),
                                      recognizer: TapGestureRecognizer()
                                        ..onTap = () {
                                          if (timeLeft == 0)
                                            resendVerificationCode();
                                        },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      )),
                ),
                bottomNavigationBar: Padding(
                  padding: EdgeInsets.fromLTRB(
                    defaultInnerPadding,
                    defaultInnerPadding,
                    defaultInnerPadding,
                    bottomPaddingWithoutBar,
                  ),
                  child: DefaultButton(
                    text: "Submit",
                    buttonColor: ThemeColors.primaryDark,
                    onPressed: () {
                      verifyOTPLogin();
                    },
                  ),
                ),
              ),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }

  void verifyOTPLogin() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    Map? data = await apiPost.verifyOTPLogin(widget.args['selectedCountryCode'],
        widget.args['phone'], codeController.text);

    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        await getMe();
        EasyLoading.dismiss();
        defaultDialog(context, data['code'], data['message'], () {
          Navigator.pushNamedAndRemoveUntil(
            context,
            "/my_home_page",
            (Route<dynamic> route) => false,
            arguments: {'dailyPopUp': true},
          );
        }).show();
      } else {
        EasyLoading.dismiss();
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      EasyLoading.dismiss();
      defaultErrorDialog(context).show();
    }
  }

  void resendVerificationCode() async {
    startTimer();

    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    Map? data = await apiPost.loginWithOTP(widget.args['selectedCountryCode'],
        widget.args['phoneNumber'], widget.args["sendVia"] ?? "SMS");

    if (data != null) {
      EasyLoading.dismiss();
      defaultDialog(context, data['code'], data['message'], () {
        if (data['code'] == 200) {
          setState(() {
            phone = data['data']['phone'];
          });
        }
      }).show();
    } else {
      EasyLoading.dismiss();
      defaultErrorDialog(context).show();
    }
  }

  void startTimer() {
    timeLeft = 59;
    timer = Timer.periodic(
      const Duration(seconds: 1),
      (Timer timer) {
        if (timeLeft == 0) {
          setState(() {
            timer.cancel();
          });
        } else {
          setState(() {
            timeLeft--;
          });
        }
      },
    );
  }

  Future<Map?> getMe() async {
    Box box = await Hive.openBox("box");
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.me();
    print(data);

    if (data != null && data['data'] != null) {
      User user = User.fromJson(data['data']['me']);
      box.put("user", user);

      String barcode = data['data']['barcode'];
      box.put("barcode", barcode);

      int totalDaysCheckIn =
          data['data']['check_in']?['total_days_checked_in'] ?? 0;
      box.put("total_days_checked_in", totalDaysCheckIn);
    }
    return data;
  }
}
