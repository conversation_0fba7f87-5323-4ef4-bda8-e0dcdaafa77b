// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:collection/collection.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_easyloading/flutter_easyloading.dart';
// import 'package:amverton/Constant/theme_colors.dart';
// import 'package:amverton/Constant/theme_size.dart';
// import 'package:amverton/Model/app_settings.dart';
// import 'package:amverton/Model/pages.dart';
// import 'package:amverton/Repository/api_post.dart';
// import 'package:amverton/Repository/modify_assets.dart';
// import 'package:amverton/View/Auth/Login/social_icon2.dart';
// import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
// import 'package:amverton/View/Custom%20Widgets/default_phonefield1.dart';
// import 'package:amverton/main.dart';
// import 'package:hive/hive.dart';
// import 'package:url_launcher/url_launcher.dart';
// import 'package:amverton/Constant/important_variables.dart'
//     as important_variables;

// class LoginTemplate1 extends StatefulWidget {
//   static String routeName = "/login/1";

//   const LoginTemplate1({Key? key}) : super(key: key);

//   @override
//   _LoginTemplate1State createState() => _LoginTemplate1State();
// }

// class _LoginTemplate1State extends State<LoginTemplate1> {
//   late Future future;
//   late Box boxGlobal;
//   String theme = loginAsset.theme;

//   String selectedCountryCode = "";
//   List<String> countryCodeList = [];
//   TextEditingController phoneController = TextEditingController();
//   AppSettings? appSettings;

//   @override
//   void initState() {
//     super.initState();
//     future = openBox();
//   }

//   openBox() async {
//     boxGlobal = await Hive.openBox('boxGlobal');
//     countryCodeList = boxGlobal.get("countryCode") ?? [];
//     if (countryCodeList.isNotEmpty) selectedCountryCode = countryCodeList.first;

//     appSettings = boxGlobal.get("appSettings") ?? null;

//     return await Hive.openBox<Asset>('LOGIN+MAIN+1');
//   }

//   @override
//   Widget build(BuildContext context) {
//     return FutureBuilder(
//         future: future,
//         builder: (BuildContext context, AsyncSnapshot snapshot) {
//           if (snapshot.connectionState == ConnectionState.done) {
//             final Box list = snapshot.data;
//             list.watch();

//             String? backgroundType = list.values
//                     .firstWhereOrNull(
//                         (element) => element.name == "BACKGROUND_TYPE")
//                     ?.data ??
//                 "";
//             Asset? background = list.values.firstWhereOrNull(
//                 (element) => element.name == "BACKGROUND_$backgroundType");

//             return GestureDetector(
//               onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
//               behavior: HitTestBehavior.translucent,
//               child: Scaffold(
//                 extendBodyBehindAppBar: true,
//                 appBar: AppBar(
//                   iconTheme: IconThemeData(
//                     color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
//                   ),
//                   title: Text("Login or sign up"),
//                 ),
//                 body: Container(
//                   height: phoneHeight,
//                   color: Colors.white,
//                   child: SingleChildScrollView(
//                     physics: const ClampingScrollPhysics(),
//                     child: Container(
//                       height: MediaQuery.of(context).size.height,
//                       padding: EdgeInsets.fromLTRB(
//                         defaultPadding,
//                         topPaddingWithoutBar,
//                         defaultPadding,
//                         verticalPaddingSmall,
//                       ),
//                       child: Column(
//                         children: [
//                           SizedBox(height: phoneHeight * 0.09),
//                           Padding(
//                             padding: EdgeInsets.symmetric(
//                                 horizontal: horizontalPaddingMedium),
//                             child: CachedNetworkImage(
//                               imageUrl: list.values
//                                       .firstWhereOrNull(
//                                           (element) => element.name == "IMAGE")
//                                       ?.data ??
//                                   "",
//                               height: phoneHeight / 5,
//                               fit: BoxFit.fitWidth,
//                               placeholder: (context, url) {
//                                 return Center(
//                                   child: CircularProgressIndicator(
//                                     color: ThemeColors.primaryDark,
//                                   ),
//                                 );
//                               },
//                               errorWidget: (context, url, error) {
//                                 return Icon(
//                                   Icons.error_outline_outlined,
//                                   color: ThemeColors.primaryDark,
//                                 );
//                               },
//                             ),
//                           ),
//                           SizedBox(height: phoneHeight * 0.06),
//                           Text(
//                             // todo temp hardcode for obriens (should take from assets, but merchant portal dont hv this template yet)
//                             "Enter your phone number",
//                             // list.values
//                             //         .firstWhereOrNull(
//                             //             (element) => element.name == "TITLE")
//                             //         ?.data ??
//                             //     "",
//                             style: TextStyle(
//                               color: theme == "L"
//                                   ? ThemeColors.dark
//                                   : ThemeColors.light,
//                               fontSize: h3,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ),
//                           SizedBox(height: spacingHeightSmall),
//                           DefaultPhoneField1(
//                             controller: phoneController,
//                             countryCodeList: countryCodeList,
//                             selectedCountryCode: selectedCountryCode,
//                             onDropdownChanged: (value) {
//                               setState(() {
//                                 setState(() {
//                                   selectedCountryCode = value!;
//                                 });
//                               });
//                             },
//                           ),
//                           SizedBox(height: spacingHeightLarge),
//                           Text(
//                             // todo temp hardcode for obriens (should take from assets, but merchant portal dont hv this template yet)
//                             "Receive Code via",
//                             // list.values
//                             //         .firstWhereOrNull(
//                             //             (element) => element.name == "TITLE")
//                             //         ?.data ??
//                             //     "",
//                             style: TextStyle(
//                               color: theme == "L"
//                                   ? ThemeColors.dark
//                                   : ThemeColors.light,
//                               fontSize: h3,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ),
//                           //temporary remove
//                           // if (appSettings?.logins?.hasWhatsAppLogin == 1)
//                           SocialIcon2(
//                             text: "WhatsApp",
//                             iconImage: "assets/icons/Social media-whatsapp.png",
//                             buttonColor: ThemeColors.primaryDark,
//                             onPressed: () {
//                               loginWithSMS("WHATSAPP");
//                             },
//                           ),
//                           //temporary remove
//                           // if (appSettings?.logins?.hasSmsLogin == 1)
//                           SocialIcon2(
//                             text: "SMS",
//                             iconImage:
//                                 "assets/icons/${boxGlobal.get("iconSet")}/Chat_chatbubble.png",
//                             buttonColor: ThemeColors.primaryDark,
//                             onPressed: () {
//                               loginWithSMS("SMS");
//                             },
//                           ),
//                           const Spacer(),
//                           Row(
//                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                             children: [
//                               Text(
//                                 "Version $versionNumber",
//                                 style: TextStyle(
//                                     color: theme == "L"
//                                         ? ThemeColors.dark
//                                         : ThemeColors.light,
//                                     fontSize: h5),
//                               ),
//                               if (important_variables.projectName != "sbk")
//                                 GestureDetector(
//                                   onTap: () async {
//                                     Uri value = Uri.parse(
//                                         "https://digitalise.invoke.my/");

//                                     if (await canLaunchUrl(value)) {
//                                       await launchUrl(value);
//                                     } else {
//                                       print("failed to open");
//                                     }
//                                   },
//                                   child: Text(
//                                     important_variables.powerBy,
//                                     style: TextStyle(
//                                       color: Colors.blue,
//                                       fontSize: h5,
//                                       decoration: TextDecoration.underline,
//                                     ),
//                                   ),
//                                 ),
//                             ],
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//             );
//           }
//           return Scaffold(appBar: AppBar());
//         });
//   }

//   void loginWithSMS(String sendVia) async {
//     EasyLoading.show();
//     ApiPost apiPost = ApiPost();
//     Map? data = await apiPost.loginWithOTP(
//         selectedCountryCode, phoneController.text, sendVia);
//     if (data != null) {
//       if (data['code'] == 200) {
//         EasyLoading.dismiss();
//         defaultDialog(context, data['code'], data['message'], () {
//           int index = registerAsset
//               .indexWhere((element) => element.sectionName == "STEP_3");
//           // .indexWhere((element) => element.sectionName == "STEP_2");
//           if (index != -1) {
//             //tod hard code to sms verfication, tbh get from asset
//             Navigator.pushNamed(
//                 context, "/sms_verification/${registerAsset[index].template}",
//                 arguments: {
//                   "theme": registerAsset[index].theme,
//                   "phone": data['data']['phone'],
//                   "selectedCountryCode": selectedCountryCode,
//                   "phoneNumber": phoneController.text,
//                   "sendVia": sendVia,
//                 });
//           }
//         }).show();
//       } else {
//         EasyLoading.dismiss();
//         if (data['data']?['should_register'] == 1) {
//           defaultDialog(context, data['code'], data['message'], () {
//             int index = registerAsset
//                 .indexWhere((element) => element.sectionName == "STEP_2");
//             // .indexWhere((element) => element.sectionName == "STEP_1");
//             if (index != -1) {
//               Navigator.pushNamed(
//                   context, "/register1/${registerAsset[index].template}",
//                   arguments: {
//                     "theme": registerAsset[index].theme,
//                     "socialLogin": false,
//                     "phoneNumber": phoneController.text,
//                     "sendVia": sendVia,
//                   });
//             }
//           }).show();
//         } else {
//           defaultDialog(context, data['code'], data['message'], () {}).show();
//         }
//       }
//     } else {
//       EasyLoading.dismiss();
//       // defaultErrorDialog(context).show();
//     }
//   }
// }

import 'package:cached_network_image/cached_network_image.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/app_settings.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Auth/Login/social_icon2.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_phonefield1.dart';
import 'package:amverton/main.dart';
import 'package:hive/hive.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class LoginTemplate1 extends StatefulWidget {
  static String routeName = "/login/1";

  const LoginTemplate1({Key? key}) : super(key: key);

  @override
  _LoginTemplate1State createState() => _LoginTemplate1State();
}

class _LoginTemplate1State extends State<LoginTemplate1> {
  late Future future;
  late Box boxGlobal;
  String theme = "L";// loginAsset.theme;

  String selectedCountryCode = "";
  List<String> countryCodeList = [];
  TextEditingController usernameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  AppSettings? appSettings;

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    countryCodeList = boxGlobal.get("countryCode") ?? [];
    if (countryCodeList.isNotEmpty) selectedCountryCode = countryCodeList.first;

    appSettings = boxGlobal.get("appSettings") ?? null;

    return await Hive.openBox<Asset>('LOGIN+MAIN+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            return Scaffold(
              body: Container(
                height: MediaQuery.of(context).size.height,
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: CachedNetworkImageProvider(
                      list.values
                              .firstWhereOrNull(
                                  (element) => element.name == "BACKGROUND_IMAGE")
                              ?.data ??
                          "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
                    ),
                    fit: BoxFit.cover,
                  ),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.4),
                  ),
                  child: SafeArea(
                    child: Center(
                      child: SingleChildScrollView(
                        padding: EdgeInsets.symmetric(horizontal: 10),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Logo and Hotel Name Section
                            Container(
                              margin: EdgeInsets.only(bottom: 30),
                              child: Column(
                                children: [
                                  // Logo with geometric pattern
                                  Container(
                                    width: 80,
                                    height: 80,
                                    margin: EdgeInsets.only(bottom: 16),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Center(
                                      child: Container(
                                        width: 60,
                                        height: 60,
                                        child: CustomPaint(
                                          painter: GeometricLogoPainter(),
                                        ),
                                      ),
                                    ),
                                  ),
                                  // Hotel Name
                                  Text(
                                    "CONCORDE HOTEL",
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: h1,
                                      fontFamily: fontChosenFamily,
                                      fontWeight: FontWeight.w300,
                                      letterSpacing: 3,
                                    ),
                                  ),
                                  SizedBox(height: 4),
                                  Text(
                                    "KUALA LUMPUR",
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.8),
                                      fontSize: h3,
                                      fontFamily: fontChosenFamily,
                                      fontWeight: FontWeight.w300,
                                      letterSpacing: 2,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // Login Card
                            Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(15),
                              decoration: BoxDecoration(
                                color: Color(0xFF5b5b5b).withOpacity(0.6),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    offset: Offset(0, 10),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  // Username Field
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Username",
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: h2,
                                          fontFamily: fontChosenFamily,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      SizedBox(height: 8),
                                      Container(
                                        height: 56,
                                        decoration: BoxDecoration(
                                          color: Colors.grey[100],
                                          borderRadius: BorderRadius.circular(12),
                                          border: Border.all(
                                            color: Colors.grey[300]!,
                                            width: 1,
                                          ),
                                        ),
                                        child: TextField(
                                          controller: usernameController,
                                          decoration: InputDecoration(
                                            border: InputBorder.none,
                                            contentPadding: EdgeInsets.symmetric(
                                              horizontal: 16,
                                              vertical: 16,
                                            ),
                                            hintText: "Enter your username",
                                            hintStyle: TextStyle(
                                              color: Colors.grey[500],
                                              fontFamily: fontChosenFamily,
                                              fontSize: h2,
                                            ),
                                          ),
                                          style: TextStyle(
                                            fontSize: h2,
                                            fontFamily: fontChosenFamily,
                                            color: Colors.grey[800],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),

                                  SizedBox(height: 15),

                                  // Password Field
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Password",
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontFamily: fontChosenFamily,
                                          fontSize: h2,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      SizedBox(height: 8),
                                      Container(
                                        height: 56,
                                        decoration: BoxDecoration(
                                          color: Colors.grey[100],
                                          borderRadius: BorderRadius.circular(12),
                                          border: Border.all(
                                            color: Colors.grey[300]!,
                                            width: 1,
                                          ),
                                        ),
                                        child: TextField(
                                          controller: passwordController,
                                          obscureText: true,
                                          decoration: InputDecoration(
                                            border: InputBorder.none,
                                            contentPadding: EdgeInsets.symmetric(
                                              horizontal: 16,
                                              vertical: 16,
                                            ),
                                            hintText: "Enter your password",
                                            hintStyle: TextStyle(
                                              color: Colors.grey[500],
                                              fontSize: h2,
                                              fontFamily: fontChosenFamily,
                                            ),
                                          ),
                                          style: TextStyle(
                                            fontSize: 16,
                                            color: Colors.grey[800],
                                            fontFamily: fontChosenFamily,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                      SizedBox(height: 5),
                                  Align(
                                    alignment: Alignment.centerRight,
                                    child: GestureDetector(
                                      onTap: () {
                                        Navigator.pushNamed(
                                          context,
                                          "/forgot_password/1",
                                          arguments: {
                                            "theme": "L",
                                          },
                                        );
                                      },
                                      child: RichText(
                                        text: TextSpan(
                                          text: "Forgot Password?",
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontFamily: fontChosenFamily,
                                            fontSize: h2,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),


                                  SizedBox(height: 20),

                                  // Login Button
                                  Container(
                                    margin: EdgeInsets.symmetric(horizontal: 45),
                                    height: 40,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          Color(0xFF818592),
                                          Color(0xFF595C68),
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: ElevatedButton(
                                      onPressed: () {
                                        login();
                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.transparent, // Make button transparent
                                        shadowColor: Colors.transparent,     // Remove shadow
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        elevation: 0,
                                      ),
                                      child: Text(
                                        "Login",
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 18,
                                          fontFamily: "Montserrat",
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ),

                                  SizedBox(height: 20),
                                ],
                              ),
                            ),

                            SizedBox(height: 20),

                            // Register Link
                            Center(
                              child: GestureDetector(
                                onTap: () {
                                   Navigator.pushNamed(
                                        context, "/register2/1",
                                        arguments: {
                                          "theme": "L",
                                        });
                                  // int index = registerAsset
                                  //     .indexWhere((element) => element.sectionName == "STEP_2");
                                  // // .indexWhere((element) => element.sectionName == "STEP_1");
                                  // if (index != -1) {
                                  //   Navigator.pushNamed(
                                  //       context, "/register2/${registerAsset[index].template}",
                                  //       arguments: {
                                  //         "theme": registerAsset[index].theme,
                                  //       });
                                  // }
                                },
                                child: RichText(
                                  text: TextSpan(
                                    text: "Register An Account, ",
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontFamily: "Poppins",
                                      fontSize: 16,
                                    ),
                                    children: [
                                      TextSpan(
                                        text: "Sign Up",
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                          fontFamily: "Poppins",
                                          decoration: TextDecoration.underline,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),

                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          }
          return Scaffold(
            body: Center(
              child: CircularProgressIndicator(
                color: ThemeColors.primaryDark,
              ),
            ),
          );
        });
  }

  void login() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
 
    Map? data = await apiPost.login(
        usernameController.text, passwordController.text);
    if (data != null) {
      if (data['code'] == 200) {
        EasyLoading.dismiss();
          defaultDialog(context, 200, "Login successful!", () {
          Navigator.pushNamedAndRemoveUntil(
                context,
                "/my_home_page",
                (Route<dynamic> route) => false,
                arguments: {'dailyPopUp': true},
              );
        }).show();
      } else {
        EasyLoading.dismiss();
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      EasyLoading.dismiss();
      // defaultErrorDialog(context).show();
    }
  }

}

// Custom painter for the geometric logo pattern
class GeometricLogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey[800]!
      ..style = PaintingStyle.fill;

    final double width = size.width;
    final double height = size.height;
    final double triangleSize = width / 6;

    // Create a pattern of small triangles similar to the logo in the image
    for (int row = 0; row < 6; row++) {
      for (int col = 0; col < 6; col++) {
        if ((row + col) % 2 == 0) {
          final path = Path();
          final double x = col * triangleSize;
          final double y = row * triangleSize;
          
          path.moveTo(x, y);
          path.lineTo(x + triangleSize, y);
          path.lineTo(x + triangleSize / 2, y + triangleSize);
          path.close();
          
          canvas.drawPath(path, paint);
        }
      }
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}