import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield2.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class ResetPasswordTemplate2 extends StatefulWidget {
  static String routeName = "/reset_password/2";
  final dynamic args;

  const ResetPasswordTemplate2({Key? key, this.args}) : super(key: key);

  @override
  _ResetPasswordTemplate2State createState() => _ResetPasswordTemplate2State();
}

class _ResetPasswordTemplate2State extends State<ResetPasswordTemplate2> {
  late Future future;
  late Box boxGlobal;
  String theme = resetPassAsset.theme;

  late Timer timer;
  int timeLeft = 59;
  TextEditingController codeController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController passwordConfirmController = TextEditingController();

  @override
  void initState() {
    super.initState();
    future = openBox();
    startTimer();
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('RESET_PASSWORD+MAIN+2');
  }

  @override
  void dispose() {
    super.dispose();
    try {
      timer.cancel();
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                extendBodyBehindAppBar: true,
                appBar: AppBar(
                  title: Text(
                    list.values
                            .firstWhereOrNull(
                                (element) => element.name == "TITLE")
                            ?.data ??
                        "",
                    style: TextStyle(
                      color:
                          theme == "L" ? ThemeColors.dark : ThemeColors.light,
                    ),
                  ),
                  iconTheme: IconThemeData(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                ),
                body: Container(
                  height: phoneHeight,
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  child: SingleChildScrollView(
                      padding: EdgeInsets.fromLTRB(defaultPadding,
                          verticalPaddingLarge, defaultPadding, defaultPadding),
                      physics: const ClampingScrollPhysics(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: spacingHeightMedium),
                          Text(
                            list.values
                                    .firstWhereOrNull(
                                        (element) => element.name == "HEADLINE")
                                    ?.data ??
                                "",
                            style: TextStyle(
                              color: theme == "L"
                                  ? ThemeColors.dark
                                  : ThemeColors.light,
                              fontSize: h1,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: spacingHeightSmall),
                          Text(
                            list.values
                                    .firstWhereOrNull((element) =>
                                        element.name == "SUBHEADLINE")
                                    ?.data ??
                                "",
                            style: TextStyle(
                                color: theme == "L"
                                    ? ThemeColors.dark
                                    : ThemeColors.light,
                                fontSize: h3),
                          ),
                          SizedBox(height: spacingHeightMedium),
                          DefaultTextField2(
                            controller: codeController,
                            keyBoardType: TextInputType.number,
                            labelText: "Verification Code",
                            hintText: "000000",
                          ),
                          DefaultTextField2(
                            controller: passwordController,
                            passwordField: true,
                            labelText: "New Password",
                            hintText: "Please fill in a new password",
                            helperText:
                                "At least 12 characters long but 14 or more, A combination of uppercase letters, lowercase letters, numbers, and symbols.",
                          ),
                          DefaultTextField2(
                            controller: passwordConfirmController,
                            passwordField: true,
                            labelText: "New Password Confirmation",
                            hintText: "Please confirm your password",
                          ),
                          SizedBox(height: spacingHeightSmall),
                          Align(
                            alignment: Alignment.center,
                            child: RichText(
                              text: TextSpan(
                                style: TextStyle(
                                    color: theme == "L"
                                        ? ThemeColors.gray
                                        : ThemeColors.disabled,
                                    fontSize: h3),
                                children: [
                                  const TextSpan(text: 'Code not received? '),
                                  TextSpan(
                                    text: timeLeft != 0
                                        ? '00:${timeLeft.toString().padLeft(2, '0')}'
                                        : "Resend code",
                                    style: TextStyle(
                                        color: ThemeColors.primaryDark),
                                    recognizer: TapGestureRecognizer()
                                      ..onTap = () {
                                        if (timeLeft == 0)
                                          requestResetPasswordCode();
                                      },
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      )),
                ),
                bottomNavigationBar: Padding(
                  padding: EdgeInsets.fromLTRB(
                    defaultInnerPadding,
                    defaultInnerPadding,
                    defaultInnerPadding,
                    bottomPaddingWithoutBar,
                  ),
                  child: DefaultButton(
                    text: list.values
                            .firstWhereOrNull((element) =>
                                element.name == "SUBMIT_BUTTON_TEXT")
                            ?.data ??
                        "",
                    buttonColor: ThemeColors.primaryDark,
                    borderRadius: 15,
                    onPressed: () {
                      resetPassword();
                    },
                  ),
                ),
              ),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }

  void resetPassword() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    Map? data = await apiPost.resetPassword(codeController.text,
        passwordController.text, passwordConfirmController.text);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          Navigator.of(context)
            ..pop()
            ..pop();
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  void requestResetPasswordCode() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    Map? data = await apiPost.requestResetPasswordCode(
        widget.args["selectedCountryCode"]);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          setState(() {
            startTimer();
          });
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  void startTimer() {
    timeLeft = 59;
    timer = Timer.periodic(
      const Duration(seconds: 1),
      (Timer timer) {
        if (timeLeft == 0) {
          setState(() {
            timer.cancel();
          });
        } else {
          setState(() {
            timeLeft--;
          });
        }
      },
    );
  }
}
