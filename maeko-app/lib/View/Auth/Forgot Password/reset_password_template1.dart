import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class ResetPasswordTemplate1 extends StatefulWidget {
  static String routeName = "/reset_password/1";
  final dynamic args;

  const ResetPasswordTemplate1({Key? key, this.args}) : super(key: key);

  @override
  _ResetPasswordTemplate1State createState() => _ResetPasswordTemplate1State();
}

class _ResetPasswordTemplate1State extends State<ResetPasswordTemplate1> {
  late Future future;
  late Box boxGlobal;
  String theme = "L";
  String email = "";

  late Timer timer;
  int timeLeft = 59;
  TextEditingController codeController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController passwordConfirmController = TextEditingController();

  @override
  void initState() {
    super.initState();
    future = openBox();
    startTimer();
  }

  @override
  void dispose() {
    super.dispose();
    try {
      timer.cancel();
    } catch (e) {}
  }

  Future<Box> openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
       // Get email from widget args or route arguments
    final routeArgs = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    final Map<String, dynamic>? arguments = widget.args ?? routeArgs;
    
    email = arguments?['email'];
    return boxGlobal;
  }

   void requestResetPasswordCode() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    Map? data = await apiPost.requestResetPasswordCode(email);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          Navigator.pushNamed(
              context, "/reset_password/1",
              arguments: {
                "email": email,
              });
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  
  void resetPassword() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    Map? data = await apiPost.resetPassword(codeController.text,
        passwordController.text, passwordConfirmController.text);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          Navigator.of(context)
            ..pop()
            ..pop();
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  void startTimer() {
    timeLeft = 59;
    timer = Timer.periodic(
      const Duration(seconds: 1),
      (Timer timer) {
        if (timeLeft == 0) {
          setState(() {
            timer.cancel();
          });
        } else {
          setState(() {
            timeLeft--;
          });
        }
      },
    );
  }

   Widget _buildPasswordRequirement(String text) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("• ", style: TextStyle(color: Colors.grey[600])),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: h3,
                fontFamily: fontChosenFamily,
              ),
            ),
          ),
        ],
      ),
    );
  }


  
  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                backgroundColor: Colors.transparent,
                body: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Header with logo
                      Container(
                        width: double.infinity,
                        height: 170, // Adjust height as needed
                        padding: EdgeInsets.fromLTRB(defaultPadding, 60, defaultPadding, 40),
                        decoration: const BoxDecoration(
                          image: DecorationImage(
                            image: AssetImage("assets/images/registration_background.png"),
                            fit: BoxFit.fill,
                          ),
                        ),
                        child: Column(
                          children: [
                            // Logo placeholder - replace with your actual logo
                            Container(
                              width: 280, // Adjust width as needed
                              height: 40, // Adjust height as needed
                              margin: EdgeInsets.only(top: 30),
                              child:   Positioned.fill(
                                child: Image.asset(
                                  important_variables.logoImage,
                                  fit: BoxFit.contain,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 40),
                      Text(
                        "New Password",
                        style: TextStyle(fontSize: h1, fontWeight: FontWeight.bold, fontFamily: fontChosenFamily),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 40, vertical: 10),
                        child: Text(
                          "Please enter your new password",
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: h3, fontFamily: fontChosenFamily, color: ThemeColors.gray),
                        ),
                      ),
                      const SizedBox(height: 20),
                      
                      // Form section
                      Padding(
                        padding: EdgeInsets.all(defaultPadding),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                                        // Email
                            Text(
                              "Code",
                              style: TextStyle(
                                color: ThemeColors.primaryDark,
                                fontSize: h2,
                                fontFamily: fontChosenFamily,
                                fontWeight: FontWeight.w300,
                              ),
                            ),
                           
                            DefaultTextField1(
                              controller: codeController,
                              keyBoardType: TextInputType.emailAddress,
                              hintText: "",
                            ),
                            SizedBox(height: 5),
                              // Password
                            Text(
                              "Password",
                              style: TextStyle(
                                color: ThemeColors.primaryDark,
                                fontSize: h2,
                                fontFamily: fontChosenFamily,
                                fontWeight: FontWeight.w300,
                              ),
                            ),
                          
                            DefaultTextField1(
                              controller: passwordController,
                              obscureText: true,
                              hintText: "",
                            ),
                            SizedBox(height: 5),
                            
                            // Confirm Password
                            Text(
                              "Confirm Password",
                              style: TextStyle(
                                color: ThemeColors.primaryDark,
                                fontSize: h2,
                                fontFamily: fontChosenFamily,
                                fontWeight: FontWeight.w300,
                              ),
                            ),
                       
                            DefaultTextField1(
                              controller: passwordConfirmController,
                              obscureText: true,
                              hintText: "",
                            ),
                            SizedBox(height: 5),
                            
                            // Password requirements
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildPasswordRequirement("Password should be 8-20 characters"),
                                _buildPasswordRequirement("Password should have an upper case letter"),
                                _buildPasswordRequirement("Password should have a lower case letter"),
                                _buildPasswordRequirement("Password should have a number or acceptable character \$ ! # & @ ? % = _"),
                              ],
                            ),
                            SizedBox(height: 30),
                            
                            // Sign Up Button
                            Container(
                              width: double.infinity,
                              margin: EdgeInsets.symmetric(horizontal: 45),
                              child: DefaultButton(
                                text: "Submit",
                                buttonColor: Colors.white,
                                onPressed:  resetPassword,
                              ),
                            ),
                            SizedBox(height: 15),
                            Align(
                              alignment: Alignment.center,
                              child: RichText(
                                text: TextSpan(
                                  style: TextStyle(
                                      color: ThemeColors.gray,
                                      fontSize: h3),
                                  children: [
                                    const TextSpan(text: 'Code not received? '),
                                    TextSpan(
                                      text: timeLeft != 0
                                          ? '00:${timeLeft.toString().padLeft(2, '0')}'
                                          : "Resend code",
                                      style: TextStyle(
                                        fontFamily: fontChosenFamily,
                                        fontSize: h3,
                                        color: ThemeColors.primaryDark),
                                      recognizer: TapGestureRecognizer()
                                        ..onTap = () {
                                          if (timeLeft == 0)
                                            requestResetPasswordCode();
                                        },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }
          return Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        });
  }
}