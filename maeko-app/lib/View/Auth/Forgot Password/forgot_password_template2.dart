import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_phonefield2.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class ForgotPasswordTemplate2 extends StatefulWidget {
  static String routeName = "/forgot_password/2";

  const ForgotPasswordTemplate2({Key? key}) : super(key: key);

  @override
  _ForgotPasswordTemplate2State createState() =>
      _ForgotPasswordTemplate2State();
}

class _ForgotPasswordTemplate2State extends State<ForgotPasswordTemplate2> {
  late Future future;
  late Box boxGlobal;
  String theme = forgotPassAsset.theme;

  String selectedCountryCode = "";
  List<String> countryCodeList = [];
  TextEditingController phoneController = TextEditingController();

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    countryCodeList = boxGlobal.get("countryCode") ?? [];
    selectedCountryCode = countryCodeList.first;

    return await Hive.openBox<Asset>('FORGOT_PASSWORD+MAIN+2');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                extendBodyBehindAppBar: true,
                appBar: AppBar(
                  title: Text(
                    list.values
                            .firstWhereOrNull(
                                (element) => element.name == "TITLE")
                            ?.data ??
                        "",
                    style: TextStyle(
                      color:
                          theme == "L" ? ThemeColors.dark : ThemeColors.light,
                    ),
                  ),
                  iconTheme: IconThemeData(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                ),
                body: Container(
                  height: phoneHeight,
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  child: SingleChildScrollView(
                      padding: EdgeInsets.fromLTRB(defaultPadding,
                          verticalPaddingLarge, defaultPadding, defaultPadding),
                      physics: const ClampingScrollPhysics(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: spacingHeightMedium),
                          Text(
                            list.values
                                    .firstWhereOrNull(
                                        (element) => element.name == "HEADLINE")
                                    ?.data ??
                                "",
                            style: TextStyle(
                              color: theme == "L"
                                  ? ThemeColors.dark
                                  : ThemeColors.light,
                              fontSize: h1,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: spacingHeightSmall),
                          Text(
                            list.values
                                    .firstWhereOrNull((element) =>
                                        element.name == "SUBHEADLINE")
                                    ?.data ??
                                "",
                            style: TextStyle(
                                color: theme == "L"
                                    ? ThemeColors.dark
                                    : ThemeColors.light,
                                fontSize: h3),
                          ),
                          SizedBox(height: spacingHeightMedium),
                          DefaultPhoneField2(
                            controller: phoneController,
                            countryCodeList: countryCodeList,
                            selectedCountryCode: selectedCountryCode,
                            onDropdownChanged: (value) {
                              setState(() {
                                setState(() {
                                  selectedCountryCode = value!;
                                });
                              });
                            },
                          ),
                          SizedBox(height: topPaddingWithoutBar),
                          DefaultButton(
                            text: list.values
                                    .firstWhereOrNull((element) =>
                                        element.name == "SUBMIT_BUTTON_TEXT")
                                    ?.data ??
                                "",
                            buttonColor: ThemeColors.primaryDark,
                            borderRadius: 15,
                            onPressed: () {
                              requestResetPasswordCode();
                            },
                          ),
                        ],
                      )),
                ),
              ),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }

  void requestResetPasswordCode() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    Map? data = await apiPost.requestResetPasswordCode(
        selectedCountryCode);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          Navigator.pushNamed(
              context, "/reset_password/${resetPassAsset.template}",
              arguments: {
                "selectedCountryCode": selectedCountryCode,
                "phoneNumber": phoneController.text,
              });
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }
}
