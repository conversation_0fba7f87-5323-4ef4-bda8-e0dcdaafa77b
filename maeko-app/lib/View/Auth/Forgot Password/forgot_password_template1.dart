import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Constant/theme.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_phonefield1.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;


class ForgotPasswordTemplate1 extends StatefulWidget {
  static String routeName = "/forgot_password/1";

  const ForgotPasswordTemplate1({Key? key}) : super(key: key);

  @override
  _ForgotPasswordTemplate1State createState() =>
      _ForgotPasswordTemplate1State();
}


class _ForgotPasswordTemplate1State extends State<ForgotPasswordTemplate1> {
  late Future future;
  late Box boxGlobal;
  String theme = "L"; // Default theme
  final TextEditingController emailController = TextEditingController();
  bool isSubmitting = false;

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  Future<Box> openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    return boxGlobal;
  }

   void requestResetPasswordCode() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    Map? data = await apiPost.requestResetPasswordCode(emailController.text);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          Navigator.pushNamed(
              context, "/reset_password/1",
              arguments: {
                "email": emailController.text,
              });
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  
  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                backgroundColor: Colors.transparent,
                body: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Header with logo
                      Container(
                        width: double.infinity,
                        height: 170, // Adjust height as needed
                        padding: EdgeInsets.fromLTRB(defaultPadding, 60, defaultPadding, 40),
                        decoration: const BoxDecoration(
                          image: DecorationImage(
                            image: AssetImage("assets/images/registration_background.png"),
                            fit: BoxFit.fill,
                          ),
                        ),
                        child: Column(
                          children: [
                            // Logo placeholder - replace with your actual logo
                            Container(
                              width: 280, // Adjust width as needed
                              height: 40, // Adjust height as needed
                              margin: EdgeInsets.only(top: 30),
                              child:   Positioned.fill(
                                child: Image.asset(
                                  important_variables.logoImage,
                                  fit: BoxFit.contain,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 40),
                      Text(
                        "Forgot Password",
                        style: TextStyle(fontSize: h1, fontWeight: FontWeight.bold, fontFamily: fontChosenFamily),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 40, vertical: 10),
                        child: Text(
                          "Please enter your email to receive a code to set a new password",
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: h3, fontFamily: fontChosenFamily, color: ThemeColors.gray),
                        ),
                      ),
                      const SizedBox(height: 20),
                      
                      // Form section
                      Padding(
                        padding: EdgeInsets.all(defaultPadding),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                                        // Email
                            Text(
                              "Email",
                              style: TextStyle(
                                color: ThemeColors.primaryDark,
                                fontSize: h2,
                                fontFamily: fontChosenFamily,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                           
                            DefaultTextField1(
                              controller: emailController,
                              keyBoardType: TextInputType.emailAddress,
                              hintText: "",
                            ),

                            SizedBox(height: 30),
                            
                            // Sign Up Button
                            Container(
                              width: double.infinity,
                              margin: EdgeInsets.symmetric(horizontal: 45),
                              child: DefaultButton(
                                text: "Submit",
                                buttonColor: Colors.white,
                                onPressed:  requestResetPasswordCode,
                              ),
                            ),
                            SizedBox(height: 15),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }
          return Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        });
  }
}