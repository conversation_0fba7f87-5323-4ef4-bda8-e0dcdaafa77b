import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_phonefield1.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class Register1Template1 extends StatefulWidget {
  static String routeName = "/register1/1";
  final dynamic args;

  const Register1Template1({Key? key, this.args}) : super(key: key);

  @override
  _Register1Template1State createState() => _Register1Template1State();
}

class _Register1Template1State extends State<Register1Template1> {
  late Future future;
  late Box boxGlobal;
  String theme = "L";

  String selectedCountryCode = "";
  List<String> countryCodeList = [];
  TextEditingController phoneController = TextEditingController();

  @override
  void initState() {
    super.initState();
    theme = widget.args["theme"];
    phoneController.text = widget.args["phoneNumber"] ?? "";
    future = openBox();
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    countryCodeList = boxGlobal.get("countryCode") ?? [];
    selectedCountryCode = countryCodeList.first;

    return await Hive.openBox<Asset>('REGISTER+STEP_1+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                extendBodyBehindAppBar: true,
                appBar: AppBar(
                  title: Text(
                    list.values
                            .firstWhereOrNull(
                                (element) => element.name == "TITLE")
                            ?.data ??
                        "",
                    style: TextStyle(
                      color:
                          theme == "L" ? ThemeColors.dark : ThemeColors.light,
                    ),
                  ),
                  iconTheme: IconThemeData(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                ),
                body: Container(
                  height: phoneHeight,
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  child: SingleChildScrollView(
                      padding: EdgeInsets.fromLTRB(defaultPadding,
                          verticalPaddingLarge, defaultPadding, defaultPadding),
                      physics: const ClampingScrollPhysics(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: phoneHeight * 0.03),
                          Text(
                            list.values
                                    .firstWhereOrNull(
                                        (element) => element.name == "SUBTITLE")
                                    ?.data ??
                                "",
                            style: TextStyle(
                              color: ThemeColors.primaryDark,
                              fontSize: h2,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: spacingHeightMedium),
                          Text(
                            list.values
                                    .firstWhereOrNull(
                                        (element) => element.name == "HEADLINE")
                                    ?.data ??
                                "",
                            style: TextStyle(
                                color: theme == "L"
                                    ? ThemeColors.dark
                                    : ThemeColors.light,
                                fontSize: h3,
                                fontWeight: FontWeight.bold),
                          ),
                          SizedBox(height: spacingHeightMedium),
                          Text(
                            list.values
                                    .firstWhereOrNull((element) =>
                                        element.name == "SUBHEADLINE")
                                    ?.data ??
                                "",
                            style: TextStyle(
                                color: theme == "L"
                                    ? ThemeColors.dark
                                    : ThemeColors.light,
                                fontSize: h4),
                          ),
                          SizedBox(height: spacingHeightMedium),
                          DefaultPhoneField1(
                            controller: phoneController,
                            countryCodeList: countryCodeList,
                            selectedCountryCode: selectedCountryCode,
                            onDropdownChanged: (value) {
                              setState(() {
                                setState(() {
                                  selectedCountryCode = value!;
                                });
                              });
                            },
                          ),
                        ],
                      )),
                ),
                bottomNavigationBar: Padding(
                  padding: EdgeInsets.fromLTRB(
                    defaultInnerPadding,
                    defaultInnerPadding,
                    defaultInnerPadding,
                    bottomPaddingWithoutBar,
                  ),
                  child: DefaultButton(
                    text: list.values
                            .firstWhereOrNull((element) =>
                                element.name == "CONTINUE_BUTTON_TEXT")
                            ?.data ??
                        "",
                    buttonColor: important_variables.projectName == "obriens"
                        ? ThemeColors.secondaryDark
                        : ThemeColors.primaryDark,
                    onPressed: () {
                      requestVerificationCode();
                    },
                  ),
                ),
              ),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }

  void requestVerificationCode() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    Map? data = await apiPost.requestVerificationCode(selectedCountryCode,
        phoneController.text, widget.args["sendVia"] ?? "SMS");
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          int index = registerAsset
              .indexWhere((element) => element.sectionName == "STEP_3");
          // .indexWhere((element) => element.sectionName == "STEP_2");
          if (index != -1) {
            Navigator.pushNamed(
                context, "/register2/${registerAsset[index].template}",
                arguments: {
                  "theme": registerAsset[index].theme,
                  "socialId": widget.args['socialId'],
                  "socialLogin": widget.args['socialLogin'],
                  "selectedCountryCode": selectedCountryCode,
                  "phoneNumber": phoneController.text,
                  "sendVia": widget.args['sendVia'],
                });
          }
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }
}
