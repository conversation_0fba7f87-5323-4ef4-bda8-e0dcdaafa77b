import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/View/Custom%20Widgets/default_datetimefield1.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_dropdown1.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';
import 'package:collection/collection.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class Register3Template1 extends StatefulWidget {
  static String routeName = "/register3/1";
  final dynamic args;

  const Register3Template1({Key? key, this.args}) : super(key: key);

  @override
  _Register3Template1State createState() => _Register3Template1State();
}

class _Register3Template1State extends State<Register3Template1> {
  late Future future;
  late Box boxGlobal;
  String theme = "L";
  bool socialLogin = false;

  List<Gender> genderList = [
    Gender("", "Gender (Optional)"),
    Gender("F", "Female"),
    Gender("M", "Male"),
    Gender("-", "Rather not say"),
  ];
  String selectedGender = "";
  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController dateController = TextEditingController();
  bool tickEmail = false;
  TextEditingController referralController = TextEditingController();

  //race
  List<Race> raceList = [
    Race("CHINESE", "CHINESE"),
    Race("INDIAN", "INDIAN"),
    Race("MALAY", "MALAY"),
    Race("OTHERS", "OTHERS"),
  ];
  String selectedRace = "OTHERS";

  @override
  void initState() {
    super.initState();
    theme = widget.args["theme"];
    socialLogin = widget.args["socialLogin"];
    future = openBox();
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('REGISTER+STEP_3+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                extendBodyBehindAppBar: true,
                appBar: AppBar(
                  title: Text(
                    list.values
                            .firstWhereOrNull(
                                (element) => element.name == "TITLE")
                            ?.data ??
                        "",
                    style: TextStyle(
                      color:
                          theme == "L" ? ThemeColors.dark : ThemeColors.light,
                    ),
                  ),
                  iconTheme: IconThemeData(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                ),
                body: Container(
                  height: phoneHeight,
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  child: SingleChildScrollView(
                      padding: EdgeInsets.fromLTRB(defaultPadding,
                          verticalPaddingLarge, defaultPadding, defaultPadding),
                      physics: const ClampingScrollPhysics(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: phoneHeight * 0.03),
                          Text(
                            list.values
                                    .firstWhereOrNull(
                                        (element) => element.name == "SUBTITLE")
                                    ?.data ??
                                "",
                            style: TextStyle(
                              color: ThemeColors.primaryDark,
                              fontSize: h2,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: spacingHeightMedium),
                          Text(
                            list.values
                                    .firstWhereOrNull(
                                        (element) => element.name == "HEADLINE")
                                    ?.data ??
                                "",
                            style: TextStyle(
                                color: theme == "L"
                                    ? ThemeColors.dark
                                    : ThemeColors.light,
                                fontSize: h3,
                                fontWeight: FontWeight.bold),
                          ),
                          SizedBox(height: spacingHeightMedium),
                          Text(
                            list.values
                                    .firstWhereOrNull((element) =>
                                        element.name == "SUBHEADLINE")
                                    ?.data ??
                                "",
                            style: TextStyle(
                                color: theme == "L"
                                    ? ThemeColors.dark
                                    : ThemeColors.light,
                                fontSize: h4),
                          ),
                          SizedBox(height: spacingHeightMedium),
                          DefaultTextField1(
                            controller: nameController,
                            hintText: "Name",
                          ),
                          if (!socialLogin)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (list.values
                                        .firstWhereOrNull((element) =>
                                            element.name == "EMAIL")
                                        .data ==
                                    "1")
                                  DefaultTextField1(
                                    controller: emailController,
                                    keyBoardType: TextInputType.emailAddress,
                                    hintText: "Email (Optional)",
                                  ),
                                if (list.values
                                        .firstWhereOrNull((element) =>
                                            element.name == "GENDER")
                                        .data ==
                                    "1")
                                  DefaultDropdown1(
                                    selectedValue: selectedGender,
                                    items: genderList.map((Gender value) {
                                      return DropdownMenuItem<String>(
                                        value: value.id,
                                        child: Text(
                                          value.value,
                                          style: TextStyle(
                                              color: value.id == ""
                                                  ? ThemeColors.gray
                                                  : ThemeColors.dark),
                                        ),
                                      );
                                    }).toList(),
                                    onChanged: (String? value) {
                                      setState(() {
                                        selectedGender = value!;
                                      });
                                    },
                                  ),
                                if (list.values
                                        .firstWhereOrNull(
                                            (element) => element.name == "RACE")
                                        .data ==
                                    "1") ...[
                                  Text(
                                    "Race",
                                    style: TextStyle(
                                        color: ThemeColors.primaryDark,
                                        fontSize: h4),
                                  ),
                                  DefaultDropdown1(
                                    selectedValue: selectedRace,
                                    items: raceList.map((Race value) {
                                      return DropdownMenuItem<String>(
                                        value: value.id,
                                        child: Text(
                                          value.value,
                                          style: TextStyle(
                                            color: ThemeColors.primaryDark,
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                    onChanged: (String? value) {
                                      setState(() {
                                        selectedRace = value!;
                                      });
                                    },
                                  ),
                                ],
                                if (list.values
                                        .firstWhereOrNull((element) =>
                                            element.name == "DATE_OF_BIRTH")
                                        .data ==
                                    "1")
                                  DefaultDateTimeField1(
                                    hintText: "Date of Birth",
                                    controller: dateController,
                                    onTap: () {
                                      selectDate();
                                    },
                                  ),
                                Text(
                                    "Once submitted, your date of birth cannot be changed.",
                                    style: TextStyle(
                                        color: ThemeColors.primaryDark,
                                        fontSize: h4)),
                                if (list.values
                                        .firstWhereOrNull((element) =>
                                            element.name == "DATE_OF_BIRTH")
                                        .data ==
                                    "1") ...[
                                  SizedBox(
                                    height: spacingHeightMedium,
                                  ),
                                  Text(
                                      "For exclusive promotions, please provide your email, gender and birthday",
                                      style: TextStyle(
                                          color: ThemeColors.primaryDark,
                                          fontSize: h4)),
                                ],
                                DefaultTextField1(
                                  controller: referralController,
                                  hintText: "Referral Code",
                                ),
                              ],
                            ),
                          SizedBox(height: spacingHeightMedium),
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  "I do not wish to receive marketing tips, insights, invitations, product updates, and more.",
                                  style: TextStyle(
                                      color: theme == "L"
                                          ? ThemeColors.dark
                                          : ThemeColors.light,
                                      fontSize: h4),
                                ),
                              ),
                              Checkbox(
                                activeColor: ThemeColors.primaryDark,
                                value: tickEmail,
                                onChanged: (value) {
                                  setState(() {
                                    tickEmail = value!;
                                  });
                                },
                              ),
                            ],
                          ),
                        ],
                      )),
                ),
                bottomNavigationBar: Padding(
                  padding: EdgeInsets.fromLTRB(
                    defaultInnerPadding,
                    defaultInnerPadding,
                    defaultInnerPadding,
                    bottomPaddingWithoutBar,
                  ),
                  child: DefaultButton(
                    text: list.values
                            .firstWhereOrNull((element) =>
                                element.name == "CONTINUE_BUTTON_TEXT")
                            ?.data ??
                        "",
                    buttonColor: important_variables.projectName == "obriens"
                        ? ThemeColors.secondaryDark
                        : ThemeColors.primaryDark,
                    onPressed: () {
                      if (socialLogin)
                        socialRegister();
                      else
                        register();
                    },
                  ),
                ),
              ),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }

  void register() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    String date = dateController.text;
    if (date == DateFormat('yyyy-MM-dd').format(DateTime.now())) {
      date = "";
    }
    String disableNotification = tickEmail ? "1" : "0";

    Map? data = await apiPost.register(
      nameController.text,
      selectedGender,
      selectedRace,
      date,
      emailController.text,
      referralController.text,
      widget.args["selectedCountryCode"],
      widget.args["phoneNumber"],
      disableNotification,
    );
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          Navigator.of(context)
            ..pop()
            ..pop()
            ..pop();
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  void socialRegister() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    String disableNotification = tickEmail ? "1" : "0";

    Map? data = await apiPost.socialRegister(
      widget.args['socialId'],
      widget.args["selectedCountryCode"],
      widget.args["phoneNumber"],
      nameController.text,
      disableNotification,
    );
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          Navigator.of(context)
            ..pop()
            ..pop()
            ..pop();
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  void selectDate() async {
    if (dateController.text.isEmpty)
      dateController.text = DateFormat('yyyy-MM-dd').format(DateTime.now());

    final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: DateTime.parse(dateController.text),
        firstDate: DateTime(DateTime.now().year - 100),
        lastDate: DateTime.now());
    if (picked != null) {
      setState(() {
        dateController.text = DateFormat('yyyy-MM-dd').format(picked);
      });
    }
  }
}

class Gender {
  String id;
  String value;

  Gender(this.id, this.value);
}

class Race {
  String id;
  String value;

  Race(this.id, this.value);
}
