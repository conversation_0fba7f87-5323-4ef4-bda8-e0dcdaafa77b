// import 'dart:async';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:flutter/gestures.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_easyloading/flutter_easyloading.dart';
// import 'package:amverton/Constant/theme_colors.dart';
// import 'package:amverton/Constant/theme_size.dart';
// import 'package:amverton/Model/pages.dart';
// import 'package:amverton/Repository/api_post.dart';
// import 'package:amverton/Repository/modify_assets.dart';
// import 'package:amverton/View/Custom%20Widgets/default_button.dart';
// import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
// import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
// import 'package:hive/hive.dart';
// import 'package:collection/collection.dart';
// import 'package:amverton/Constant/important_variables.dart'
//     as important_variables;

// class Register2Template1 extends StatefulWidget {
//   static String routeName = "/register2/1";
//   final dynamic args;

//   const Register2Template1({Key? key, this.args}) : super(key: key);

//   @override
//   _Register2Template1State createState() => _Register2Template1State();
// }

// class _Register2Template1State extends State<Register2Template1> {
//   late Future future;
//   late Box boxGlobal;
//   String theme = "L";

//   late Timer timer;
//   int timeLeft = 59;
//   TextEditingController codeController = TextEditingController();

//   @override
//   void initState() {
//     super.initState();
//     theme = widget.args["theme"];
//     future = openBox();
//     startTimer();
//   }

//   openBox() async {
//     boxGlobal = await Hive.openBox('boxGlobal');
//     return await Hive.openBox<Asset>('REGISTER+STEP_2+1');
//   }

//   @override
//   void dispose() {
//     super.dispose();
//     try {
//       timer.cancel();
//     } catch (e) {}
//   }

//   @override
//   Widget build(BuildContext context) {
//     return FutureBuilder(
//         future: future,
//         builder: (BuildContext context, AsyncSnapshot snapshot) {
//           if (snapshot.connectionState == ConnectionState.done) {
//             final Box list = snapshot.data;
//             list.watch();

//             String? backgroundType = list.values
//                     .firstWhereOrNull(
//                         (element) => element.name == "BACKGROUND_TYPE")
//                     ?.data ??
//                 "";
//             Asset? background = list.values.firstWhereOrNull(
//                 (element) => element.name == "BACKGROUND_$backgroundType");

//             return GestureDetector(
//               onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
//               behavior: HitTestBehavior.translucent,
//               child: Scaffold(
//                 extendBodyBehindAppBar: true,
//                 appBar: AppBar(
//                   title: Text(
//                     list.values
//                             .firstWhereOrNull(
//                                 (element) => element.name == "TITLE")
//                             ?.data ??
//                         "",
//                     style: TextStyle(
//                       color:
//                           theme == "L" ? ThemeColors.dark : ThemeColors.light,
//                     ),
//                   ),
//                   iconTheme: IconThemeData(
//                     color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
//                   ),
//                 ),
//                 body: Container(
//                   height: phoneHeight,
//                   decoration: background == null
//                       ? null
//                       : BoxDecoration(
//                           image: backgroundType == "IMAGE"
//                               ? DecorationImage(
//                                   image: CachedNetworkImageProvider(
//                                       background.data ?? ""),
//                                   fit: BoxFit.cover,
//                                 )
//                               : null,
//                           color: backgroundType == "COLOR"
//                               ? Color(int.parse(
//                                   "0xff${background.data ?? "FFFFFF"}"))
//                               : null,
//                         ),
//                   child: SingleChildScrollView(
//                       padding: EdgeInsets.fromLTRB(defaultPadding,
//                           verticalPaddingLarge, defaultPadding, defaultPadding),
//                       physics: const ClampingScrollPhysics(),
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           SizedBox(height: phoneHeight * 0.03),
//                           Text(
//                             list.values
//                                     .firstWhereOrNull(
//                                         (element) => element.name == "SUBTITLE")
//                                     ?.data ??
//                                 "",
//                             style: TextStyle(
//                               color: ThemeColors.primaryDark,
//                               fontSize: h2,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ),
//                           SizedBox(height: spacingHeightMedium),
//                           Text(
//                             list.values
//                                     .firstWhereOrNull(
//                                         (element) => element.name == "HEADLINE")
//                                     ?.data ??
//                                 "",
//                             style: TextStyle(
//                                 color: theme == "L"
//                                     ? ThemeColors.dark
//                                     : ThemeColors.light,
//                                 fontSize: h3,
//                                 fontWeight: FontWeight.bold),
//                           ),
//                           SizedBox(height: spacingHeightMedium),
//                           Text(
//                             "An OTP code has been sent to ${widget.args['selectedCountryCode']}${widget.args['phoneNumber']}",
//                             style: TextStyle(
//                                 color: theme == "L"
//                                     ? ThemeColors.dark
//                                     : ThemeColors.light,
//                                 fontSize: h4),
//                           ),
//                           SizedBox(height: spacingHeightMedium),
//                           DefaultTextField1(
//                             controller: codeController,
//                             keyBoardType: TextInputType.number,
//                             hintText: "000000",
//                           ),
//                           SizedBox(height: spacingHeightSmall),
//                           Align(
//                             alignment: Alignment.center,
//                             child: RichText(
//                               textAlign: TextAlign.center,
//                               text: TextSpan(
//                                 style: TextStyle(
//                                     color: theme == "L"
//                                         ? ThemeColors.gray
//                                         : ThemeColors.disabled,
//                                     fontSize: h3),
//                                 children: [
//                                   TextSpan(
//                                     text:
//                                         "${list.values.firstWhereOrNull((element) => element.name == "UNDELIVERED_TEXT")?.data ?? ""} ",
//                                   ),
//                                   TextSpan(
//                                     text: timeLeft == 0
//                                         ? list.values
//                                                 .firstWhereOrNull((element) =>
//                                                     element.name ==
//                                                     "RESEND_BUTTON_TEXT")
//                                                 ?.data ??
//                                             ""
//                                         : '00:${timeLeft.toString().padLeft(2, '0')}',
//                                     style: TextStyle(
//                                         color: ThemeColors.primaryDark),
//                                     recognizer: TapGestureRecognizer()
//                                       ..onTap = () {
//                                         if (timeLeft == 0)
//                                           requestVerificationCode();
//                                       },
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           ),
//                         ],
//                       )),
//                 ),
//                 bottomNavigationBar: Padding(
//                   padding: EdgeInsets.fromLTRB(
//                     defaultInnerPadding,
//                     defaultInnerPadding,
//                     defaultInnerPadding,
//                     bottomPaddingWithoutBar,
//                   ),
//                   child: DefaultButton(
//                     text: "Submit",
//                     buttonColor: important_variables.projectName == "obriens"
//                         ? ThemeColors.secondaryDark
//                         : ThemeColors.primaryDark,
//                     onPressed: () {
//                       verifyPhone();
//                     },
//                   ),
//                 ),
//               ),
//             );
//           }
//           return Scaffold(appBar: AppBar());
//         });
//   }

//   void verifyPhone() async {
//     EasyLoading.show();
//     ApiPost apiPost = ApiPost();
//     Map? data = await apiPost.verifyPhone(widget.args['selectedCountryCode'],
//         widget.args['phoneNumber'], codeController.text);
//     EasyLoading.dismiss();

//     if (data != null) {
//       if (data['code'] == 200) {
//         defaultDialog(context, data['code'], data['message'], () {
//           int index = registerAsset
//               .indexWhere((element) => element.sectionName == "STEP_1");
//           // .indexWhere((element) => element.sectionName == "STEP_3");
//           if (index != -1) {
//             Navigator.pushNamed(
//                 context, "/register3/${registerAsset[index].template}",
//                 arguments: {
//                   "theme": registerAsset[index].theme,
//                   "socialId": widget.args['socialId'],
//                   "socialLogin": widget.args['socialLogin'],
//                   "selectedCountryCode": widget.args['selectedCountryCode'],
//                   "phoneNumber": widget.args['phoneNumber'],
//                 });
//           }
//         }).show();
//       } else {
//         defaultDialog(context, data['code'], data['message'], () {}).show();
//       }
//     } else {
//       defaultErrorDialog(context).show();
//     }
//   }

//   void requestVerificationCode() async {
//     EasyLoading.show();
//     ApiPost apiPost = ApiPost();

//     Map? data = await apiPost.requestVerificationCode(
//         widget.args["selectedCountryCode"],
//         widget.args["phoneNumber"],
//         widget.args["sendVia"] ?? "SMS");
//     EasyLoading.dismiss();

//     if (data != null) {
//       if (data['code'] == 200) {
//         defaultDialog(context, data['code'], data['message'], () {
//           setState(() {
//             startTimer();
//           });
//         }).show();
//       } else {
//         defaultDialog(context, data['code'], data['message'], () {}).show();
//       }
//     } else {
//       defaultErrorDialog(context).show();
//     }
//   }

//   void startTimer() {
//     timeLeft = 59;
//     timer = Timer.periodic(
//       const Duration(seconds: 1),
//       (Timer timer) {
//         if (timeLeft == 0) {
//           setState(() {
//             timer.cancel();
//           });
//         } else {
//           setState(() {
//             timeLeft--;
//           });
//         }
//       },
//     );
//   }
// }

import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/Constant/important_variables.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:intl/intl.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class Register2Template1 extends StatefulWidget {
  static String routeName = "/register2/1";
  final dynamic args;

  const Register2Template1({Key? key, this.args}) : super(key: key);

  @override
  _Register2Template1State createState() => _Register2Template1State();
}

class _Register2Template1State extends State<Register2Template1> {
  late Future future;
  late Box boxGlobal;
  String theme = "L";
  bool _agreedToTerms = false;

  // Form controllers
  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController usernameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController dobController = TextEditingController();

  @override
  void initState() {
    super.initState();
    theme = widget.args["theme"];
    future = openBox();
    // Pre-fill phone number if available
    if (widget.args != null && widget.args['phoneNumber'] != null) {
      phoneController.text = "${widget.args['selectedCountryCode'] ?? '+60'}${widget.args['phoneNumber']}";
    } else {
      phoneController.text = "+60";
    }
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('REGISTER+STEP_2+1');
  }

  @override
  void dispose() {
    firstNameController.dispose();
    lastNameController.dispose();
    phoneController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    emailController.dispose();
    dobController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                backgroundColor: Colors.transparent,
                body: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Header with logo
                      Container(
                        width: double.infinity,
                        height: 170, // Adjust height as needed
                        padding: EdgeInsets.fromLTRB(defaultPadding, 60, defaultPadding, 40),
                        decoration: const BoxDecoration(
                          image: DecorationImage(
                            image: AssetImage("assets/images/registration_background.png"),
                            fit: BoxFit.fill,
                          ),
                        ),
                        child: Column(
                          children: [
                            // Logo placeholder - replace with your actual logo
                            Container(
                              width: 280, // Adjust width as needed
                              height: 40, // Adjust height as needed
                              margin: EdgeInsets.only(top: 30),
                              child:   Positioned.fill(
                                child: Image.asset(
                                  logoImage,
                                  fit: BoxFit.contain,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Form section
                      Padding(
                        padding: EdgeInsets.all(defaultPadding),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // First Name
                            Text(
                              "First Name",
                              style: TextStyle(
                                color: ThemeColors.primaryDark,
                                fontSize: h2,
                                fontFamily: fontChosenFamily,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
          
                            DefaultTextField1(
                              controller: firstNameController,
                              hintText: "",
                            ),
                            SizedBox(height: 5),
                            
                            // Last Name
                            Text(
                              "Last Name",
                              style: TextStyle(
                                color: ThemeColors.primaryDark,
                                fontSize: h2,
                                fontFamily: fontChosenFamily,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                         
                            DefaultTextField1(
                              controller: lastNameController,
                              hintText: "",
                            ),
                            SizedBox(height: 5),
                            
                            // Phone Number
                            Text(
                              "Phone Number",
                              style: TextStyle(
                                color: ThemeColors.primaryDark,
                                fontSize: h2,
                                fontFamily: fontChosenFamily,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                        
                            DefaultTextField1(
                              controller: phoneController,
                              keyBoardType: TextInputType.phone,
                              hintText: "+60",
                            ),
                            SizedBox(height: 5),
                            
                            // Date of Birth
                            Text(
                              "Date Of Birth",
                              style: TextStyle(
                                color: ThemeColors.primaryDark,
                                fontSize: h2,
                                fontFamily: fontChosenFamily,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                         
                            GestureDetector(
                              onTap: () => _selectDate(context),
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                                decoration: BoxDecoration(
                                  border: Border.all(color: ThemeColors.disabled),
                                  borderRadius: BorderRadius.circular(8),
                                  color: Colors.white,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      dobController.text.isEmpty ? "Select Date" : dobController.text,
                                      style: TextStyle(
                                        color: dobController.text.isEmpty ? ThemeColors.primaryDark : Colors.black,
                                        fontSize: h2,
                                        fontFamily: fontChosenFamily,
                                      ),
                                    ),
                                    Icon(Icons.keyboard_arrow_down, color: ThemeColors.primaryDark),
                                  ],
                                ),
                              ),
                            ),
                            SizedBox(height: 5),

                                        // Email
                            Text(
                              "Email",
                              style: TextStyle(
                                color: ThemeColors.primaryDark,
                                fontSize: h2,
                                fontFamily: fontChosenFamily,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                           
                            DefaultTextField1(
                              controller: emailController,
                              keyBoardType: TextInputType.emailAddress,
                              hintText: "",
                            ),
                            SizedBox(height: 5),

                            Text(
                              "Username",
                              style: TextStyle(
                                color: ThemeColors.primaryDark,
                                fontSize: h2,
                                fontFamily: fontChosenFamily,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                         
                            DefaultTextField1(
                              controller: usernameController,
                              hintText: "",
                            ),
                            SizedBox(height: 5),
                            
                            // Password
                            Text(
                              "Password",
                              style: TextStyle(
                                color: ThemeColors.primaryDark,
                                fontSize: h2,
                                fontFamily: fontChosenFamily,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          
                            DefaultTextField1(
                              controller: passwordController,
                              obscureText: true,
                              hintText: "",
                            ),
                            SizedBox(height: 5),
                            
                            // Confirm Password
                            Text(
                              "Confirm Password",
                              style: TextStyle(
                                color: ThemeColors.primaryDark,
                                fontSize: h2,
                                fontFamily: fontChosenFamily,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                       
                            DefaultTextField1(
                              controller: confirmPasswordController,
                              obscureText: true,
                              hintText: "",
                            ),
                            SizedBox(height: 5),
                            
                
                            
                            // Password requirements
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildPasswordRequirement("Password should be 8-20 characters"),
                                _buildPasswordRequirement("Password should have an upper case letter"),
                                _buildPasswordRequirement("Password should have a lower case letter"),
                                _buildPasswordRequirement("Password should have a number or acceptable character \$ ! # & @ ? % = _"),
                              ],
                            ),
                            SizedBox(height: 15),
                            
                            // Terms checkbox
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Checkbox(
                                  value: _agreedToTerms,
                                  onChanged: (value) {
                                    setState(() {
                                      _agreedToTerms = value ?? false;
                                    });
                                  },
                                  activeColor:  Color(0xFF475467)
                                ),
                                Expanded(
                                  child: RichText(
                                    text: TextSpan(
                                      style: TextStyle(
                                        color: Color(0xFF475467),
                                        fontWeight: FontWeight.w300,
                                        fontSize: h3,
                                        fontFamily: fontChosenFamily,
                                      ),
                                      children: [
                                        TextSpan(text: "I have read the "),
                                        TextSpan(
                                          text: "Terms & conditions",
                                          style: TextStyle(
                                            color:  Color(0xFF475467),
                                            fontWeight: FontWeight.w300,
                                            fontSize: h3,
                                            fontFamily: fontChosenFamily,
                                            decoration: TextDecoration.underline,
                                          ),
                                          recognizer: TapGestureRecognizer()
                                            ..onTap = () {
                                              // Handle terms & conditions tap
                                            },
                                        ),
                                        TextSpan(text: " of becoming a member"),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 20),
                            
                            // Sign Up Button
                            Container(
                              width: double.infinity,
                              margin: EdgeInsets.symmetric(horizontal: 45),
                              child: DefaultButton(
                                text: "Sign Up",
                                buttonColor: Colors.white,
                                onPressed: _agreedToTerms ? () {
                                  _submitRegistration();
                                } : null,
                              ),
                            ),
                            SizedBox(height: 15),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }
          return Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        });
  }

  Widget _buildPasswordRequirement(String text) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("• ", style: TextStyle(color: Colors.grey[600])),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: h3,
                fontFamily: fontChosenFamily,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(Duration(days: 6570)), // 18 years ago
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        dobController.text = DateFormat('yyyy-MM-dd').format(picked);
      });
    }
  }

  void _submitRegistration() async {
    // Validate form
    if (firstNameController.text.isEmpty ||
        lastNameController.text.isEmpty ||
        phoneController.text.isEmpty ||
        passwordController.text.isEmpty ||
        confirmPasswordController.text.isEmpty ||
        emailController.text.isEmpty ||
        dobController.text.isEmpty) {
      defaultDialog(context, 400, "Please fill all fields", () {}).show();
      return;
    }

    if (passwordController.text != confirmPasswordController.text) {
      defaultDialog(context, 400, "Passwords do not match", () {}).show();
      return;
    }

    if (!_agreedToTerms) {
      defaultDialog(context, 400, "Please agree to Terms & Conditions", () {}).show();
      return;
    }

    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    
    // You'll need to implement this API call based on your backend
    Map? data = await apiPost.register(
      firstNameController.text,
      lastNameController.text,
      phoneController.text,
      dobController.text,
      usernameController.text,
      passwordController.text,
      confirmPasswordController.text,
      emailController.text,
      _agreedToTerms ? "1" : "0",
   
    );
    
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          // Navigate to next step or login
          print("Registration successful: ${data['message']}");
          Navigator.pushNamedAndRemoveUntil(
            context,
            "/my_home_page",
            (Route<dynamic> route) => false,
            arguments: {'dailyPopUp': true},
          );
           print("Registration successful2: ${data['message']}");
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }
}