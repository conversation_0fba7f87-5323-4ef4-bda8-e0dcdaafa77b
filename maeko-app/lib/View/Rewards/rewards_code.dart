import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/voucher.dart';
import 'package:hive/hive.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class RewardsCode extends StatefulWidget {
  final Voucher voucher;
  final String qrCode;

  const RewardsCode({
    Key? key,
    required this.voucher,
    required this.qrCode,
  }) : super(key: key);

  @override
  _RewardsCodeState createState() => _RewardsCodeState();
}

class _RewardsCodeState extends State<RewardsCode> {
  Box boxGlobal = Hive.box('boxGlobal');

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: Text(
          "Scan Code",
          style: important_variables.projectName == "steven_curry"
              ? TextStyle(color: ThemeColors.light)
              : TextStyle(color: ThemeColors.dark),
        ),
        iconTheme: IconThemeData(
            color: important_variables.projectName == "steven_curry"
                ? ThemeColors.light
                : ThemeColors.dark),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: important_variables.projectName == "steven_curry"
              ? const Color(0xff20222B)
              : null,
        ),
        height: phoneHeight,
        width: phoneWidth,
        padding: EdgeInsets.only(
          top: verticalPaddingLarge,
          left: defaultPadding,
          right: defaultPadding,
          bottom: bottomPaddingWithoutBar + defaultPadding,
        ),
        child: Column(
          children: [
            SizedBox(height: spacingHeightLarge),
            Text(
              "Show this code to our cashier to\nredeem your awesome reward.",
              textAlign: TextAlign.center,
              style: TextStyle(
                color: important_variables.projectName == "steven_curry"
                    ? ThemeColors.light
                    : ThemeColors.dark,
                fontSize: h1,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: spacingHeightMedium),
            Text(
              "Expired on: ${widget.voucher.endOn}",
              style: TextStyle(
                color: important_variables.projectName == "steven_curry"
                    ? ThemeColors.light
                    : ThemeColors.dark,
                fontSize: h3,
              ),
            ),
            Container(
              margin: EdgeInsets.symmetric(vertical: topPaddingWithoutBar),
              padding: EdgeInsets.all(defaultPadding),
              decoration: BoxDecoration(
                color: ThemeColors.light,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: important_variables.projectName == "steven_curry"
                      ? ThemeColors.disabled
                      : ThemeColors.gray,
                ),
              ),
              child: Image.memory(base64Decode(widget.qrCode)),
            ),
          ],
        ),
      ),
    );
  }
}
