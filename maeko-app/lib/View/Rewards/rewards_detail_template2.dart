import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/voucher.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Rewards/rewards_code.dart';
import 'package:hive/hive.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class RewardsDetailTemplate2 extends StatefulWidget {
  final int id;
  final Object tag;

  const RewardsDetailTemplate2({
    Key? key,
    required this.id,
    required this.tag,
  }) : super(key: key);

  @override
  _RewardsDetailTemplate2State createState() => _RewardsDetailTemplate2State();
}

class _RewardsDetailTemplate2State extends State<RewardsDetailTemplate2> {
  Box boxGlobal = Hive.box("boxGlobal");
  late Future future;
  Voucher? voucher;
  String qrCode = "";

  @override
  void initState() {
    super.initState();
    future = getVoucherDetail();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        leading: GestureDetector(
          onTap: () {
            Navigator.of(context).pop();
          },
          child: Container(
            margin: EdgeInsets.all(10),
            decoration:
                BoxDecoration(shape: BoxShape.circle, color: ThemeColors.gray),
            child: Icon(
              Icons.arrow_back,
              color: ThemeColors.light,
            ),
          ),
        ),
      ),
      body: FutureBuilder(
          future: future,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            if (snapshot.connectionState == ConnectionState.done) {
              if (snapshot.hasData) {
                voucher = snapshot.data['data']['voucher'] == null
                    ? null
                    : Voucher.fromJson(snapshot.data['data']['voucher']);
                qrCode = snapshot.data['data']['voucher']['qr_code'] ?? "";

                if (voucher != null) {
                  return SingleChildScrollView(
                    padding: EdgeInsets.only(bottom: buttonHeight * 2),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: phoneHeight / 2,
                          child: Stack(
                            children: [
                              Hero(
                                tag: widget.tag,
                                child: CachedNetworkImage(
                                  imageUrl: voucher!.image ?? "",
                                  height: phoneHeight / 3,
                                  width: phoneWidth,
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) {
                                    return Center(
                                      child: CircularProgressIndicator(
                                        color: ThemeColors.primaryDark,
                                      ),
                                    );
                                  },
                                  errorWidget: (context, url, error) {
                                    return Icon(
                                      Icons.error_outline_outlined,
                                      color: ThemeColors.primaryDark,
                                    );
                                  },
                                ),
                              ),
                              Positioned(
                                top: phoneHeight / 3.75,
                                left: defaultPadding,
                                right: defaultPadding,
                                child: Container(
                                  padding: EdgeInsets.all(defaultPadding),
                                  height: phoneHeight / 4.75,
                                  decoration: BoxDecoration(
                                    color: ThemeColors.light,
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Column(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        voucher!.title,
                                        style: TextStyle(
                                          color: ThemeColors.dark,
                                          fontSize: h2,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      Text(
                                        "Redeem at any counter",
                                        style: TextStyle(
                                            color: ThemeColors.dark,
                                            fontSize: h3),
                                      ),
                                      Text(
                                        "Expired On " + voucher!.endOn,
                                        style: TextStyle(
                                            color: ThemeColors.gray,
                                            fontSize: h4),
                                      ),
                                      DefaultButton(
                                        text: "Use Voucher",
                                        buttonColor:
                                            important_variables.projectName ==
                                                    "obriens"
                                                ? ThemeColors.secondaryDark
                                                : ThemeColors.primaryDark,
                                        onPressed: () {
                                          Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                  builder:
                                                      (BuildContext context) =>
                                                          RewardsCode(
                                                            voucher: voucher!,
                                                            qrCode: qrCode,
                                                          )));
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: defaultPadding,
                                vertical: defaultInnerPadding),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Voucher details",
                                  style: TextStyle(
                                    color: ThemeColors.dark,
                                    fontSize: h3,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: spacingHeightSmall),
                                Text(
                                  voucher!.description,
                                  style: TextStyle(
                                    color: ThemeColors.dark,
                                    fontSize: h4,
                                  ),
                                ),
                              ],
                            )),
                      ],
                    ),
                  );
                }
              }
            }
            return Container();
          }),
    );
  }

  Future<Map?> getVoucherDetail() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.voucherDetail(widget.id);
    return data;
  }
}
