import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/voucher.dart';
import 'package:amverton/Repository/api.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Custom%20Widgets/empty_list.dart';
import 'package:amverton/View/Custom%20Widgets/voucher_card.dart';
import 'package:amverton/View/Rewards/rewards_detail_template1.dart';
import 'package:amverton/View/Rewards/rewards_detail_template2.dart';
import 'package:amverton/View/Rewards/rewards_detail_template3.dart';
import 'package:hive/hive.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;
import 'package:intl/intl.dart';
import 'package:rxdart/rxdart.dart';
import 'package:collection/collection.dart';

class RewardsListTemplate3 extends StatefulWidget {
  static String routeName = "/rewards_list/3";
  final dynamic args;

  const RewardsListTemplate3({Key? key, this.args}) : super(key: key);

  @override
  _RewardsListTemplate3State createState() => _RewardsListTemplate3State();
}

class _RewardsListTemplate3State extends State<RewardsListTemplate3> {
  late Future future;
  late Box boxGlobal;
  String theme = "L";

  final ScrollController activeScrollController = ScrollController();
  bool activeHasMorePages = false;
  int activeNextPage = 0;
  StreamController activeStreamController = BehaviorSubject();
  List<Voucher> activeList = [];

  final ScrollController inactiveScrollController = ScrollController();
  bool inactiveHasMorePages = false;
  int inactiveNextPage = 0;
  final StreamController inactiveStreamController = BehaviorSubject();
  List<Voucher> inactiveList = [];

  @override
  void initState() {
    super.initState();
    theme = widget.args["theme"];
    future = openBox();

    getVoucher("active", activeNextPage);
    getVoucher("inactive", inactiveNextPage);

    activeScrollController.addListener(() {
      if (activeScrollController.position.pixels ==
          activeScrollController.position.maxScrollExtent) {
        if (activeHasMorePages) getVoucher("active", activeNextPage);
      }
    });

    inactiveScrollController.addListener(() {
      if (inactiveScrollController.position.pixels ==
          inactiveScrollController.position.maxScrollExtent) {
        if (inactiveHasMorePages) getVoucher("inactive", inactiveNextPage);
      }
    });
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('ACCOUNT+VOUCHERS+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return DefaultTabController(
              length: 2,
              child: Scaffold(
                appBar: AppBar(
                  title: Text(
                    list.values
                            .firstWhereOrNull(
                                (element) => element.name == "TITLE")
                            ?.data ??
                        "",
                    style: TextStyle(
                      color:
                          theme == "L" ? ThemeColors.dark : ThemeColors.light,
                    ),
                  ),
                  iconTheme: IconThemeData(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                ),
                body: Container(
                  padding: EdgeInsets.all(defaultPadding),
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  child: Column(
                    children: [
                      TabBar(
                        padding: EdgeInsets.symmetric(
                            horizontal: defaultPadding + 10),
                        labelColor: ThemeColors.primaryDark,
                        indicator: BoxDecoration(
                          color: ThemeColors.primaryDark,
                        ),
                        indicatorWeight: 1,
                        indicatorPadding: EdgeInsets.only(top: 40),
                        // indicator: BoxDecoration(
                        //   borderRadius: BorderRadius.circular(30),
                        //   color: ThemeColors.primaryDark,
                        // ),
                        unselectedLabelColor: ThemeColors.gray,
                        tabs: const [
                          Tab(text: "Active"),
                          Tab(text: "Past"),
                        ],
                      ),
                      SizedBox(
                        height: defaultPadding,
                      ),
                      Expanded(
                        child: TabBarView(
                          children: [
                            active(),
                            inactive(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }

  Widget active() {
    return StreamBuilder(
      stream: activeStreamController.stream,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.hasError) {
          return Container();
        } else {
          if (activeList.isEmpty) {
            return EmptyList(theme: "L");
          } else {
            return ListView.builder(
              controller: activeScrollController,
              padding: EdgeInsets.only(bottom: defaultPadding),
              itemCount: activeList.length,
              itemBuilder: (BuildContext context, int index) {
                Voucher voucher = activeList[index];
                return GestureDetector(
                  onTap: () {
                    // todo temporary hardcode, cz merchant portal dont hv a section for it.
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (BuildContext context) =>
                            RewardsDetailTemplate3(
                          voucher: voucher,
                          tag: "reward/active/$index",
                        ),
                      ),
                    );
                  },
                  child: VoucherCard(
                    imageUrl: voucher.image ?? "",
                    title: voucher.title,
                    endon: voucher.endOn.toString(),
                    tag: "reward/active/$index",
                  ),
                );
              },
            );
          }
        }
      },
    );
  }

  Widget inactive() {
    return StreamBuilder(
      stream: inactiveStreamController.stream,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.hasError) {
          return Container();
        } else {
          if (inactiveList.isEmpty) {
            return EmptyList(theme: "L");
          } else {
            return ListView.builder(
              controller: inactiveScrollController,
              padding: EdgeInsets.only(bottom: defaultPadding),
              itemCount: inactiveList.length,
              itemBuilder: (BuildContext context, int index) {
                Voucher voucher = inactiveList[index];
                return GestureDetector(
                  onTap: () {
                    // Navigator.push(
                    //     context,
                    //     MaterialPageRoute(
                    //         builder: (BuildContext context) => RewardsDetail(
                    //               voucher: inactiveList[index],
                    //               tag: "reward/inactive/$index",
                    //             )));
                  },
                  child: VoucherCard(
                    imageUrl: voucher.image ?? "",
                    title: voucher.title,
                    endon: voucher.endOn.toString(),
                    tag: "reward/inactive/$index",
                    used: voucher.pivot!.usedAt != null ? true : false,
                    active: false,
                  ),
                );
              },
            );
          }
        }
      },
    );
  }

  Future<Map?> getVoucher(String filter, int nextPage) async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.voucher(filter, nextPage);
    if (data != null) {
      List<Voucher> newList = (data['data']['vouchers'] as List)
          .map((data) => Voucher.fromJson(data))
          .toList();
      if (filter == "active") {
        activeHasMorePages = data["data"]["has_more_pages"];
        if (activeHasMorePages) {
          activeNextPage = data["data"]["next_page"] ?? 0;
        }
        activeList.addAll(newList);
        activeStreamController.add(data);
      } else {
        inactiveHasMorePages = data["data"]["has_more_pages"];
        if (inactiveHasMorePages) {
          inactiveNextPage = data["data"]["next_page"] ?? 0;
        }
        inactiveList.addAll(newList);
        inactiveStreamController.add(data);
      }
    }
    return data;
  }
}
