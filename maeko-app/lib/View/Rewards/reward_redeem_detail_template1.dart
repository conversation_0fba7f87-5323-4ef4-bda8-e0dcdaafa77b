import 'dart:convert';
import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/reward.dart';
import 'package:amverton/Model/voucher.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Rewards/rewards_code.dart';
import 'package:hive/hive.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class RewardsRedeemDetailTemplate1 extends StatefulWidget {
  final PointsReward pointsReward;
  final Object tag;
  final Function buttonFunction;

  const RewardsRedeemDetailTemplate1({
    Key? key,
    required this.pointsReward,
    required this.tag,
    required this.buttonFunction,
  }) : super(key: key);

  @override
  _RewardsRedeemDetailTemplate1State createState() =>
      _RewardsRedeemDetailTemplate1State();
}

class _RewardsRedeemDetailTemplate1State
    extends State<RewardsRedeemDetailTemplate1> {
  Box boxGlobal = Hive.box("boxGlobal");
  Voucher? voucher;
  PointsReward? pointsReward;
  String qrCode = "";

  @override
  void initState() {
    super.initState();
    pointsReward = widget.pointsReward;
    voucher = widget.pointsReward.voucher;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          leading: GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Container(
              margin: EdgeInsets.all(10),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: ThemeColors.gray,
              ),
              child: Icon(
                Icons.arrow_back,
                color: ThemeColors.light,
              ),
            ),
          ),
        ),
        body: Column(
          children: [
            Expanded(
              flex: 7,
              child: SingleChildScrollView(
                child: Container(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Hero(
                        tag: widget.tag,
                        child: CachedNetworkImage(
                          imageUrl: voucher!.image ?? "",
                          height: phoneHeight * 0.3,
                          width: phoneWidth,
                          fit: BoxFit.cover,
                          placeholder: (context, url) {
                            return Center(
                              child: CircularProgressIndicator(
                                color: ThemeColors.primaryDark,
                              ),
                            );
                          },
                          errorWidget: (context, url, error) {
                            return Icon(
                              Icons.error_outline_outlined,
                              color: ThemeColors.primaryDark,
                            );
                          },
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.all(defaultPadding),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              voucher!.title,
                              style: TextStyle(
                                color: ThemeColors.dark,
                                fontSize: h2,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(
                              height: spacingHeightSmall,
                            ),
                            Text(
                              "${pointsReward!.point} Points",
                              style: TextStyle(
                                  color: ThemeColors.secondaryDark,
                                  fontSize: h1,
                                  fontWeight: FontWeight.bold),
                            ),
                            SizedBox(
                              height: spacingHeightMedium,
                            ),
                            Text(
                              "Expired Date ",
                              style: TextStyle(
                                  color: ThemeColors.dark,
                                  fontSize: h4,
                                  fontWeight: FontWeight.bold),
                            ),
                            Text(
                              voucher!.endOn,
                              style: TextStyle(
                                  color: ThemeColors.gray, fontSize: h4),
                            ),
                            SizedBox(
                              height: spacingHeightMedium,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Descriptions",
                                  style: TextStyle(
                                    color: ThemeColors.dark,
                                    fontSize: h4,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: spacingHeightSmall),
                                Text(
                                  voucher!.description,
                                  style: TextStyle(
                                    color: ThemeColors.gray,
                                    fontSize: h4,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Expanded(
              child: Container(
                margin: EdgeInsets.all(spacingHeightLarge),
                child: DefaultButton(
                  text: "Redeem",
                  buttonColor: important_variables.projectName == "obriens"
                      ? ThemeColors.secondaryDark
                      : ThemeColors.primaryDark,
                  onPressed: () {
                    widget.buttonFunction(pointsReward!.id, context);
                  },
                ),
              ),
            ),
          ],
        ));
  }
}
