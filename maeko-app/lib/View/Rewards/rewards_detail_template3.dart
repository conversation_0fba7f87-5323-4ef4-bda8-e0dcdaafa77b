import 'dart:convert';
import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/reward.dart';
import 'package:amverton/Model/voucher.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Rewards/rewards_code.dart';
import 'package:hive/hive.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class RewardsDetailTemplate3 extends StatefulWidget {
  final Voucher voucher;
  final Object tag;

  const RewardsDetailTemplate3({
    Key? key,
    required this.voucher,
    required this.tag,
  }) : super(key: key);

  @override
  _RewardsDetailTemplate3State createState() => _RewardsDetailTemplate3State();
}

class _RewardsDetailTemplate3State extends State<RewardsDetailTemplate3> {
  Box boxGlobal = Hive.box("boxGlobal");
  Voucher? voucher;
  String qrCode = "";

  @override
  void initState() {
    super.initState();
    voucher = widget.voucher;
    getVoucherDetail();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          leading: GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Container(
              margin: EdgeInsets.all(10),
              decoration: BoxDecoration(
                  shape: BoxShape.circle, color: ThemeColors.gray),
              child: Icon(
                Icons.arrow_back,
                color: ThemeColors.light,
              ),
            ),
          ),
        ),
        body: Column(
          children: [
            Expanded(
              flex: 7,
              child: SingleChildScrollView(
                child: Container(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Hero(
                        tag: widget.tag,
                        child: CachedNetworkImage(
                          imageUrl: voucher!.image ?? "",
                          height: phoneHeight * 0.3,
                          width: phoneWidth,
                          fit: BoxFit.cover,
                          placeholder: (context, url) {
                            return Center(
                              child: CircularProgressIndicator(
                                color: ThemeColors.primaryDark,
                              ),
                            );
                          },
                          errorWidget: (context, url, error) {
                            return Icon(
                              Icons.error_outline_outlined,
                              color: ThemeColors.primaryDark,
                            );
                          },
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.all(defaultPadding),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              voucher!.title,
                              style: TextStyle(
                                color: ThemeColors.dark,
                                fontSize: h2,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(
                              height: spacingHeightSmall,
                            ),
                            GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                Clipboard.setData(
                                  ClipboardData(
                                    text: voucher!.pivot!.code!,
                                  ),
                                );

                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                        'Voucher code copied to clipboard'),
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                              },
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    voucher!.pivot!.code!,
                                    style: TextStyle(
                                        color: ThemeColors.secondaryDark,
                                        fontSize: h1,
                                        fontWeight: FontWeight.bold),
                                  ),
                                  Text(
                                    "Copy Code",
                                    style: TextStyle(
                                        color: ThemeColors.gray, fontSize: h4),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: spacingHeightMedium,
                            ),
                            Text(
                              "Expired Date ",
                              style: TextStyle(
                                  color: ThemeColors.dark,
                                  fontSize: h4,
                                  fontWeight: FontWeight.bold),
                            ),
                            Text(
                              voucher!.endOn,
                              style: TextStyle(
                                  color: ThemeColors.gray, fontSize: h4),
                            ),
                            SizedBox(
                              height: spacingHeightMedium,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Descriptions",
                                  style: TextStyle(
                                    color: ThemeColors.dark,
                                    fontSize: h4,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: spacingHeightSmall),
                                Text(
                                  voucher!.description,
                                  style: TextStyle(
                                    color: ThemeColors.gray,
                                    fontSize: h4,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Expanded(
              child: Container(
                margin: EdgeInsets.all(spacingHeightLarge),
                child: DefaultButton(
                  text: "Use it Now",
                  buttonColor: important_variables.projectName == "obriens"
                      ? ThemeColors.secondaryDark
                      : ThemeColors.primaryDark,
                  onPressed: () {
                    showQrPage();
                  },
                ),
              ),
            ),
          ],
        ));
  }

  Future<void> showQrPage() async {
    if (qrCode == "") {
      EasyLoading.show();
      await Future.delayed(Duration(seconds: 1));
      EasyLoading.dismiss();
    }
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) => RewardsCode(
          voucher: voucher!,
          qrCode: qrCode,
        ),
      ),
    );
  }

  Future<Map?> getVoucherDetail() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.voucherDetail(widget.voucher.id);
    if (data != null) {
      qrCode = data['data']['voucher']['qr_code'] ?? "";
    }
    return data;
  }
}
