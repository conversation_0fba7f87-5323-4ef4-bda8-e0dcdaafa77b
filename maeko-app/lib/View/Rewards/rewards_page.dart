import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/reward.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/loading1.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

Reward? reward;

class RewardsPage extends StatefulWidget {
  const RewardsPage({Key? key}) : super(key: key);

  @override
  _RewardsPageState createState() => _RewardsPageState();

  static void onRefresh(BuildContext context) {
    _RewardsPageState? state =
        context.findAncestorStateOfType<_RewardsPageState>();
    state?.onRefresh();
  }
}

class _RewardsPageState extends State<RewardsPage> {
  Box boxGlobal = Hive.box("boxGlobal");
  late Future future;

  @override
  void initState() {
    super.initState();
    future = getRewards();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        FutureBuilder(
            future: future,
            builder: (BuildContext context, AsyncSnapshot snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Loading1();
              } else if (snapshot.connectionState == ConnectionState.done) {
                if (snapshot.hasData) {
                  return Scaffold(
                    appBar: AppBar(
                      title: Text(() {
                        List bottomBarList = boxGlobal.get("bottomBar") ?? [];
                        if (bottomBarList.isNotEmpty) {
                          return bottomBarList
                                  .firstWhereOrNull(
                                      (element) => element.name == "REWARDS")
                                  ?.title ??
                              "";
                        }
                        return "";
                      }()),
                    ),
                    body: RefreshIndicator(
                      color: ThemeColors.primaryDark,
                      onRefresh: () {
                        return Future.delayed(const Duration(seconds: 1), () {
                          setState(() {
                            future = getRewards();
                          });
                        });
                      },
                      child: SizedBox(
                        height: phoneHeight,
                        child: SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: Column(children: rewardsWidget),
                        ),
                      ),
                    ),
                  );
                }
              }
              return Scaffold(
                appBar: AppBar(
                  title: Text(() {
                    List bottomBarList = boxGlobal.get("bottomBar") ?? [];
                    if (bottomBarList.isNotEmpty) {
                      return bottomBarList
                              .firstWhereOrNull(
                                  (element) => element.name == "REWARDS")
                              ?.title ??
                          "";
                    }
                    return "";
                  }()),
                ),
                body: RefreshIndicator(
                  color: ThemeColors.primaryDark,
                  onRefresh: () {
                    return Future.delayed(const Duration(seconds: 1), () {
                      setState(() {
                        future = getRewards();
                      });
                    });
                  },
                  child: SizedBox(
                    height: phoneHeight,
                    child: SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: Column(children: rewardsWidget),
                    ),
                  ),
                ),
              );
            }),
      ],
    );
  }

  Future<Map?> getRewards() async {
    Box box = await Hive.openBox('box');

    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.rewards();
    if (data != null && data['data'] != null) {
      setState(() {
        reward = Reward.fromJson(data['data']);

        int totalDaysCheckIn = reward?.checkIn?.totalDaysCheckedIn ?? 0;
        box.put("total_days_checked_in", totalDaysCheckIn);
      });
    }
    return data;
  }

  onRefresh() {
    setState(() {
      future = getRewards();
    });
  }
}
