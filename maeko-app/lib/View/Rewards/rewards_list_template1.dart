import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/voucher.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Custom%20Widgets/empty_list.dart';
import 'package:amverton/View/Rewards/rewards_detail_template1.dart';
import 'package:amverton/View/Rewards/rewards_detail_template2.dart';
import 'package:hive/hive.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;
import 'package:intl/intl.dart';
import 'package:rxdart/rxdart.dart';
import 'package:collection/collection.dart';

class RewardsListTemplate1 extends StatefulWidget {
  static String routeName = "/rewards_list/1";
  final dynamic args;

  const RewardsListTemplate1({Key? key, this.args}) : super(key: key);

  @override
  _RewardsListTemplate1State createState() => _RewardsListTemplate1State();
}

class _RewardsListTemplate1State extends State<RewardsListTemplate1> {
  late Future future;
  late Box boxGlobal;
  String theme = "L";

  final ScrollController activeScrollController = ScrollController();
  bool activeHasMorePages = false;
  int activeNextPage = 0;
  StreamController activeStreamController = BehaviorSubject();
  List<Voucher> activeList = [];

  final ScrollController inactiveScrollController = ScrollController();
  bool inactiveHasMorePages = false;
  int inactiveNextPage = 0;
  final StreamController inactiveStreamController = BehaviorSubject();
  List<Voucher> inactiveList = [];

  @override
  void initState() {
    super.initState();
    theme = widget.args["theme"];
    future = openBox();

    getVoucher("active", activeNextPage);
    getVoucher("inactive", inactiveNextPage);

    activeScrollController.addListener(() {
      if (activeScrollController.position.pixels ==
          activeScrollController.position.maxScrollExtent) {
        if (activeHasMorePages) getVoucher("active", activeNextPage);
      }
    });

    inactiveScrollController.addListener(() {
      if (inactiveScrollController.position.pixels ==
          inactiveScrollController.position.maxScrollExtent) {
        if (inactiveHasMorePages) getVoucher("inactive", inactiveNextPage);
      }
    });
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('ACCOUNT+VOUCHERS+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return DefaultTabController(
              length: 2,
              child: Scaffold(
                appBar: AppBar(
                  title: Text(
                    list.values
                            .firstWhereOrNull(
                                (element) => element.name == "TITLE")
                            ?.data ??
                        "",
                    style: TextStyle(
                      color:
                          theme == "L" ? ThemeColors.dark : ThemeColors.light,
                    ),
                  ),
                  iconTheme: IconThemeData(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                ),
                body: Container(
                  padding: EdgeInsets.all(defaultPadding),
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  child: Column(
                    children: [
                      TabBar(
                        padding:
                            EdgeInsets.symmetric(horizontal: defaultPadding),
                        labelColor: ThemeColors.light,
                        indicator: BoxDecoration(
                          borderRadius: BorderRadius.circular(30),
                          color: ThemeColors.primaryDark,
                        ),
                        unselectedLabelColor: ThemeColors.dark,
                        unselectedLabelStyle:
                            TextStyle(color: ThemeColors.light),
                        tabs: const [
                          Tab(text: "Active"),
                          Tab(text: "Past"),
                        ],
                      ),
                      Expanded(
                        child: TabBarView(
                          children: [
                            active(),
                            inactive(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }

  Widget active() {
    return StreamBuilder(
      stream: activeStreamController.stream,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.hasError) {
          return Container();
        } else {
          if (activeList.isEmpty) {
            return EmptyList(theme: "L");
          } else {
            return ListView.builder(
              controller: activeScrollController,
              padding: EdgeInsets.symmetric(vertical: defaultPadding),
              itemCount: activeList.length,
              itemBuilder: (BuildContext context, int index) {
                return GestureDetector(
                  onTap: () {
                    // todo temporary hardcode, cz merchant portal dont hv a section for it.
                    if (important_variables.projectName == "steven_curry") {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (BuildContext context) =>
                                  RewardsDetailTemplate1(
                                    id: activeList[index].id,
                                    tag: "reward/active/$index",
                                  )));
                    } else {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (BuildContext context) =>
                                  RewardsDetailTemplate2(
                                    id: activeList[index].id,
                                    tag: "reward/active/$index",
                                  )));
                    }
                  },
                  child: Container(
                    margin: EdgeInsets.only(bottom: defaultInnerPadding),
                    padding: EdgeInsets.only(right: defaultInnerPadding),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: ThemeColors.disabled),
                    ),
                    child: Row(
                      children: [
                        Container(
                            margin: EdgeInsets.only(right: spacingHeightSmall),
                            padding: EdgeInsets.all(spacingHeightSmall),
                            width: phoneWidth / 2,
                            height: phoneWidth / 4,
                            child: Hero(
                              tag: "reward/active/$index",
                              child: ClipRRect(
                                borderRadius:
                                    const BorderRadius.all(Radius.circular(20)),
                                child: CachedNetworkImage(
                                  imageUrl: activeList[index].image ?? "",
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) {
                                    return Center(
                                      child: CircularProgressIndicator(
                                        color: ThemeColors.primaryDark,
                                      ),
                                    );
                                  },
                                  errorWidget: (context, url, error) {
                                    return Icon(
                                      Icons.error_outline_outlined,
                                      color: ThemeColors.primaryDark,
                                    );
                                  },
                                ),
                              ),
                            )),
                        Expanded(
                          child: Container(
                            padding: EdgeInsets.symmetric(
                                vertical: defaultInnerPadding),
                            height: phoneHeight / 8,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                Text(
                                  activeList[index].title,
                                  style: TextStyle(
                                    color: ThemeColors.dark,
                                    fontSize: h3,
                                    height: 1.25,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                Text(
                                  "Expired on ${activeList[index].endOn}",
                                  style: TextStyle(
                                    color: ThemeColors.gray,
                                    fontSize: h4,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          }
        }
      },
    );
  }

  Widget inactive() {
    return StreamBuilder(
      stream: inactiveStreamController.stream,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.hasError) {
          return Container();
        } else {
          if (inactiveList.isEmpty) {
            return EmptyList(theme: "L");
          } else {
            return ListView.builder(
              controller: inactiveScrollController,
              padding: EdgeInsets.all(defaultPadding),
              itemCount: inactiveList.length,
              itemBuilder: (BuildContext context, int index) {
                return GestureDetector(
                  onTap: () {
                    // Navigator.push(
                    //     context,
                    //     MaterialPageRoute(
                    //         builder: (BuildContext context) => RewardsDetail(
                    //               voucher: inactiveList[index],
                    //               tag: "reward/inactive/$index",
                    //             )));
                  },
                  child: Container(
                    margin: EdgeInsets.only(bottom: defaultInnerPadding),
                    padding: EdgeInsets.only(right: defaultInnerPadding),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: ThemeColors.disabled),
                    ),
                    child: Row(
                      children: [
                        Stack(
                          children: [
                            Container(
                                margin:
                                    EdgeInsets.only(right: spacingHeightSmall),
                                padding: EdgeInsets.all(spacingHeightSmall),
                                width: phoneWidth / 2,
                                height: phoneWidth / 4,
                                child: Hero(
                                  tag: "reward/inactive/$index",
                                  child: ClipRRect(
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(20)),
                                    child: CachedNetworkImage(
                                      imageUrl: inactiveList[index].image ?? "",
                                      width: phoneWidth,
                                      fit: BoxFit.cover,
                                      placeholder: (context, url) {
                                        return Center(
                                          child: CircularProgressIndicator(
                                            color: ThemeColors.primaryDark,
                                          ),
                                        );
                                      },
                                      errorWidget: (context, url, error) {
                                        return Icon(
                                          Icons.error_outline_outlined,
                                          color: ThemeColors.primaryDark,
                                        );
                                      },
                                    ),
                                  ),
                                )),
                            Positioned.fill(
                              child: Container(
                                margin: EdgeInsets.fromLTRB(
                                  spacingHeightSmall,
                                  spacingHeightSmall,
                                  spacingHeightSmall + spacingHeightSmall,
                                  spacingHeightSmall,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(20),
                                  color: ThemeColors.disabled.withOpacity(0.5),
                                ),
                              ),
                            )
                          ],
                        ),
                        Expanded(
                          child: Container(
                            padding: EdgeInsets.symmetric(
                                vertical: defaultInnerPadding),
                            height: phoneHeight / 8,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                Text(
                                  inactiveList[index].title,
                                  style: TextStyle(
                                    color: ThemeColors.dark,
                                    fontSize: h3,
                                    height: 1.25,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                Text(
                                  "Used/Expired on ${inactiveList[index].pivot!.usedAt == null ? inactiveList[index].endOn : DateFormat('yyyy-MM-d').format(inactiveList[index].pivot!.usedAt!)}",
                                  style: TextStyle(
                                    color: ThemeColors.gray,
                                    fontSize: h4,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          }
        }
      },
    );
  }

  Future<Map?> getVoucher(String filter, int nextPage) async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.voucher(filter, nextPage);

    if (data != null) {
      List<Voucher> newList = (data['data']['vouchers'] as List)
          .map((data) => Voucher.fromJson(data))
          .toList();
      if (filter == "active") {
        activeHasMorePages = data["data"]["has_more_pages"];
        if (activeHasMorePages) {
          activeNextPage = data["data"]["next_page"] ?? 0;
        }
        activeList.addAll(newList);
        activeStreamController.add(data);
      } else {
        inactiveHasMorePages = data["data"]["has_more_pages"];
        if (inactiveHasMorePages) {
          inactiveNextPage = data["data"]["next_page"] ?? 0;
        }
        inactiveList.addAll(newList);
        inactiveStreamController.add(data);
      }
    }
    return data;
  }
}
