import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Home/check_in.dart';
import 'package:amverton/View/Rewards/rewards_page.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class CheckInTemplate2 extends StatefulWidget {
  final String theme;

  const CheckInTemplate2({Key? key, required this.theme}) : super(key: key);

  @override
  State<CheckInTemplate2> createState() => _CheckInTemplate2State();
}

class _CheckInTemplate2State extends State<CheckInTemplate2> {
  late Future boxFuture;
  late Box box;
  late Box boxGlobal;

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');

    // todo temp for obriens
    return await Hive.openBox<Asset>('REWARDS+CHECK_IN+1');
    // return await Hive.openBox<Asset>('REWARDS+CHECK_IN+2');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            bool isLogin = box.get("is_login") ?? false;
            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            String? innerBackgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "INNER_BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? innerBackground = list.values.firstWhereOrNull((element) =>
                element.name == "INNER_BACKGROUND_$innerBackgroundType");

            if (list.isNotEmpty) {
              return Container(
                decoration: background == null
                    ? null
                    : BoxDecoration(
                        image: backgroundType == "IMAGE"
                            ? DecorationImage(
                                image: CachedNetworkImageProvider(
                                    background.data ?? ""),
                                fit: BoxFit.cover,
                              )
                            : null,
                        color: backgroundType == "COLOR"
                            ? Color(
                                int.parse("0xff${background.data ?? "FFFFFF"}"))
                            : null,
                      ),
                child: Container(
                  margin: EdgeInsets.symmetric(vertical: defaultPadding),
                  width: phoneWidth,
                  height: phoneHeight / 2.1,
                  child: Stack(
                    alignment: AlignmentDirectional.topCenter,
                    children: [
                      Container(
                        padding: EdgeInsets.all(phoneHeight * 0.045),
                        width: phoneWidth / 1.2,
                        height: phoneHeight / 2.6,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          image: DecorationImage(
                            image: AssetImage(
                              "assets/images/temp-reward-checkin-T2.png",
                            ),
                            fit: BoxFit.cover,
                          ),
                        ),
                        child: SfRadialGauge(
                          axes: <RadialAxis>[
                            RadialAxis(
                              minimum: 0,
                              maximum: 7,
                              showLabels: false,
                              showTicks: false,
                              startAngle: 150,
                              endAngle: 390,
                              radiusFactor: 0.85,
                              axisLineStyle: AxisLineStyle(
                                thickness: 0.1,
                                thicknessUnit: GaugeSizeUnit.factor,
                                color:
                                    ThemeColors.primaryLight.withOpacity(0.5),
                              ),
                              pointers: <GaugePointer>[
                                RangePointer(
                                  value: reward != null
                                      ? reward!.checkIn!.totalDaysCheckedIn
                                          .toDouble()
                                      : 0,
                                  width: 0.1,
                                  sizeUnit: GaugeSizeUnit.factor,
                                  color: ThemeColors.secondaryLight,
                                ),
                              ],
                              annotations: <GaugeAnnotation>[
                                GaugeAnnotation(
                                  angle: 90,
                                  widget: Text(
                                    () {
                                      try {
                                        double point =
                                            double.parse(reward!.me.point);
                                        return "${point.toStringAsFixed(0)}\nPoints";
                                      } catch (e) {
                                        return "0\nPoints";
                                      }
                                    }(),
                                    style: TextStyle(
                                      color: ThemeColors.light,
                                      fontSize: h1,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                )
                              ],
                            ),
                            RadialAxis(
                              minimum: 0,
                              interval: 1,
                              maximum: 7,
                              showLabels: false,
                              showTicks: true,
                              showAxisLine: false,
                              tickOffset: -0.15,
                              offsetUnit: GaugeSizeUnit.factor,
                              minorTicksPerInterval: 0,
                              startAngle: 150,
                              endAngle: 390,
                              radiusFactor: 0.74,
                              majorTickStyle: MajorTickStyle(
                                  length: 0.105,
                                  thickness: 5,
                                  lengthUnit: GaugeSizeUnit.factor,
                                  color: ThemeColors.secondaryLight),
                            )
                          ],
                        ),
                      ),
                      Positioned(
                        top: phoneHeight / 4,
                        left: defaultPadding,
                        right: defaultPadding,
                        child: reward != null &&
                                reward?.checkIn?.checkInEvent != null
                            ? Container(
                                width: phoneWidth,
                                decoration: innerBackground == null
                                    ? BoxDecoration(
                                        borderRadius: BorderRadius.circular(20))
                                    : BoxDecoration(
                                        image: innerBackgroundType == "IMAGE"
                                            ? DecorationImage(
                                                image:
                                                    CachedNetworkImageProvider(
                                                        innerBackground.data ??
                                                            ""),
                                                fit: BoxFit.cover,
                                              )
                                            : null,
                                        color: innerBackgroundType == "COLOR"
                                            ? Color(int.parse(
                                                "0xff${innerBackground.data ?? "FFFFFF"}"))
                                            : null,
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                child: Container(
                                  padding: EdgeInsets.all(defaultInnerPadding),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "${reward!.checkIn!.totalDaysCheckedIn} Day(s) of check in",
                                        style: TextStyle(
                                          color: widget.theme == "L"
                                              ? ThemeColors.dark
                                              : ThemeColors.light,
                                          fontSize: h3,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      SizedBox(height: spacingHeightSmall),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: List.generate(
                                            reward!.checkIn!.checkInEvent!
                                                .rewards!.length, (index) {
                                          return Container(
                                            padding: EdgeInsets.all(5),
                                            width: phoneWidth / 9,
                                            child: Column(
                                              children: [
                                                Stack(
                                                  children: [
                                                    CircleAvatar(
                                                      backgroundColor:
                                                          ThemeColors.disabled,
                                                      radius: 18,
                                                      backgroundImage:
                                                          CachedNetworkImageProvider(
                                                        reward!
                                                                .checkIn!
                                                                .checkInEvent!
                                                                .rewards![index]
                                                                .image ??
                                                            "",
                                                      ),
                                                    ),
                                                    if (reward!
                                                            .checkIn!
                                                            .checkInEvent!
                                                            .rewards![index]
                                                            .isClaimed !=
                                                        1)
                                                      CircleAvatar(
                                                        backgroundColor:
                                                            ThemeColors.disabled
                                                                .withOpacity(
                                                                    0.8),
                                                        radius: 18,
                                                      ),
                                                  ],
                                                ),
                                                SizedBox(height: 3),
                                                Text(
                                                  reward!.checkIn!.checkInEvent!
                                                      .rewards![index].title,
                                                  style: TextStyle(
                                                      color: ThemeColors.dark,
                                                      fontSize: h5),
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ],
                                            ),
                                          );
                                        }),
                                      ),
                                      SizedBox(height: spacingHeightMedium),
                                      DefaultButton(
                                        text: () {
                                          if (isLogin) {
                                            if (reward?.checkIn
                                                    ?.hasCheckedInToday !=
                                                0) {
                                              // todo temp hardcode for obriens (should take from assets, but merchant portal dont hv this template yet)
                                              return "See you tomorrow!";
                                            } else {
                                              return list.values
                                                      .firstWhereOrNull(
                                                          (element) =>
                                                              element.name ==
                                                              "MEMBER_BUTTON_TEXT")
                                                      ?.data ??
                                                  "";
                                            }
                                          } else {
                                            return list.values
                                                    .firstWhereOrNull(
                                                        (element) =>
                                                            element.name ==
                                                            "GUEST_BUTTON_TEXT")
                                                    ?.data ??
                                                "";
                                          }
                                        }(),
                                        textColor: () {
                                          if (isLogin &&
                                              reward!.checkIn!
                                                      .hasCheckedInToday !=
                                                  0) {
                                            return ThemeColors.gray;
                                          }
                                          return null;
                                        }(),
                                        buttonColor: () {
                                          if (isLogin) {
                                            if (reward!.checkIn!.checkInEvent!
                                                        .type ==
                                                    "WEEKLY" &&
                                                reward!.checkIn!
                                                        .hasCheckedInToday !=
                                                    0) {
                                              return ThemeColors.disabled;
                                            }
                                          }
                                          return important_variables
                                                      .projectName ==
                                                  "obriens"
                                              ? ThemeColors.secondaryDark
                                              : ThemeColors.primaryDark;
                                        }(),
                                        onPressed: () {
                                          if (isLogin) {
                                            if (reward!.checkIn!.checkInEvent!
                                                    .type ==
                                                "WEEKLY") {
                                              if (reward!.checkIn!
                                                      .hasCheckedInToday ==
                                                  0) {
                                                checkIn();
                                              }
                                            } else {
                                              Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                      builder: (BuildContext
                                                              context) =>
                                                          CheckIn(
                                                            checkInEvent: reward!
                                                                .checkIn!
                                                                .checkInEvent!,
                                                          )));
                                            }
                                          } else {
                                            Navigator.of(context,
                                                    rootNavigator: true)
                                                .pushNamed(
                                                    "/login/${loginAsset.template}");
                                          }
                                        },
                                      )
                                    ],
                                  ),
                                ),
                              )
                            : Container(
                                width: phoneWidth,
                                child: ClipRRect(
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(20)),
                                  child: CachedNetworkImage(
                                    imageUrl: list.values
                                            .firstWhereOrNull((element) =>
                                                element.name ==
                                                "COMING_SOON_IMAGE")
                                            ?.data ??
                                        "",
                                    fit: BoxFit.cover,
                                    height: phoneHeight / 4.5,
                                  ),
                                ),
                              ),
                      ),
                    ],
                  ),
                ),
              );
            }
          }
          return Container();
        });
  }

  void checkIn() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    Map? data = await apiPost.checkIn();
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          RewardsPage.onRefresh(context);
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }
}
