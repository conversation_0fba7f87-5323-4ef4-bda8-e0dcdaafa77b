import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Rewards/rewards_page.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:intl/intl.dart';

class BalanceTemplate1 extends StatefulWidget {
  final String theme;

  const BalanceTemplate1({Key? key, required this.theme}) : super(key: key);

  @override
  State<BalanceTemplate1> createState() => _BalanceTemplate1State();
}

class _BalanceTemplate1State extends State<BalanceTemplate1> {
  late Future future;
  late Box box;
  late Box boxGlobal;

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('REWARDS+BALANCE+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            bool isLogin = box.get("is_login") ?? false;

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            String? innerBackgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "INNER_BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? innerBackground = list.values.firstWhereOrNull((element) =>
                element.name == "INNER_BACKGROUND_$innerBackgroundType");

            if (list.isNotEmpty) {
              return Container(
                width: phoneWidth,
                padding: EdgeInsets.symmetric(vertical: defaultPadding),
                decoration: background == null
                    ? null
                    : BoxDecoration(
                        image: backgroundType == "IMAGE"
                            ? DecorationImage(
                                image: CachedNetworkImageProvider(
                                    background.data ?? ""),
                                fit: BoxFit.cover,
                              )
                            : null,
                        color: backgroundType == "COLOR"
                            ? Color(
                                int.parse("0xff${background.data ?? "FFFFFF"}"))
                            : null,
                      ),
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: defaultPadding,
                    vertical: verticalPaddingSmall,
                  ),
                  decoration: innerBackground == null
                      ? null
                      : BoxDecoration(
                          image: innerBackgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      innerBackground.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: innerBackgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${innerBackground.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            list.values
                                    .firstWhereOrNull((element) =>
                                        element.name == "POINT_LABEL")
                                    ?.data ??
                                "",
                            style: TextStyle(
                                color: widget.theme == "L"
                                    ? ThemeColors.dark
                                    : ThemeColors.light,
                                fontSize: h3),
                          ),
                          SizedBox(height: spacingHeightSmall),
                          Text(
                            "${reward?.me.point ?? "0.00"} ${list.values.firstWhereOrNull((element) => element.name == "POINT_UNIT")?.data ?? ""}",
                            style: TextStyle(
                              color: widget.theme == "L"
                                  ? ThemeColors.dark
                                  : ThemeColors.light,
                              fontSize: h1,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (isLogin && reward?.me.pointToBeExpired != null)
                            Text(
                              "${reward!.me.pointToBeExpired ?? "0.00"} ${list.values.firstWhereOrNull((element) => element.name == "POINT_UNIT")?.data ?? ""} will be expired on ${DateFormat('d MMM yyyy').format(reward!.me.pointExpiredOn!)}",
                              style: TextStyle(
                                  color: widget.theme == "L"
                                      ? ThemeColors.dark
                                      : ThemeColors.light,
                                  fontSize: h5),
                            ),
                        ],
                      ),
                      SizedBox(
                        width: phoneWidth / 3.5,
                        child: DefaultButton(
                          text: list.values
                                  .firstWhereOrNull((element) =>
                                      element.name ==
                                      "VIEW_USER_VOUCHERS_BUTTON_TEXT")
                                  ?.data ??
                              "",
                          fontSize: h3,
                          buttonColor: ThemeColors.primaryDark,
                          onPressed: () {
                            if (isLogin) {
                              Navigator.pushNamed(context,
                                  "/rewards_list/${accountAsset.firstWhereOrNull((element) => element.sectionName == "VOUCHERS")?.template ?? ""}",
                                  arguments: {
                                    "theme": accountAsset
                                            .firstWhereOrNull((element) =>
                                                element.sectionName ==
                                                "VOUCHERS")
                                            ?.theme ??
                                        "L",
                                  });
                            } else {
                              Navigator.of(context, rootNavigator: true)
                                  .pushNamed("/login/${loginAsset.template}");
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }
          }
          return Container();
        });
  }
}
