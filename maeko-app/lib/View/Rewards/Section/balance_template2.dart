import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/View/Rewards/rewards_page.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:intl/intl.dart';

class BalanceTemplate2 extends StatefulWidget {
  final String theme;

  const BalanceTemplate2({Key? key, required this.theme}) : super(key: key);

  @override
  State<BalanceTemplate2> createState() => _BalanceTemplate2State();
}

class _BalanceTemplate2State extends State<BalanceTemplate2> {
  late Future future;
  late Box box;
  late Box boxGlobal;

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('REWARDS+BALANCE+2');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            bool isLogin = box.get("is_login") ?? false;

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            String? innerBackgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "INNER_BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? innerBackground = list.values.firstWhereOrNull((element) =>
                element.name == "INNER_BACKGROUND_$backgroundType");

            if (list.isNotEmpty) {
              return Container(
                width: phoneWidth,
                decoration: background == null
                    ? null
                    : BoxDecoration(
                        image: backgroundType == "IMAGE"
                            ? DecorationImage(
                                image: CachedNetworkImageProvider(
                                    background.data ?? ""),
                                fit: BoxFit.cover,
                              )
                            : null,
                        color: backgroundType == "COLOR"
                            ? Color(
                                int.parse("0xff${background.data ?? "FFFFFF"}"))
                            : null,
                      ),
                child: Container(
                  margin: EdgeInsets.all(defaultPadding),
                  padding: EdgeInsets.all(defaultPadding),
                  decoration: innerBackground == null
                      ? BoxDecoration(borderRadius: BorderRadius.circular(20))
                      : BoxDecoration(
                          image: innerBackgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      innerBackground.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: innerBackgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${innerBackground.data ?? "FFFFFF"}"))
                              : null,
                          borderRadius: BorderRadius.circular(20),
                        ),
                  child: Column(
                    children: [
                      Text(
                        list.values
                                .firstWhereOrNull(
                                    (element) => element.name == "POINT_LABEL")
                                ?.data ??
                            "",
                        style: TextStyle(
                            color: widget.theme == "L"
                                ? ThemeColors.dark
                                : ThemeColors.light,
                            fontSize: h3),
                      ),
                      SizedBox(height: spacingHeightSmall),
                      Text(
                        "${reward?.me.point ?? "0.00"} ${list.values.firstWhereOrNull((element) => element.name == "POINT_UNIT")?.data ?? ""}",
                        style: TextStyle(
                          color: ThemeColors.primaryDark,
                          fontSize: h1,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: spacingHeightSmall),
                      if (isLogin && reward?.me.pointToBeExpired != null)
                        Text(
                          "${reward!.me.pointToBeExpired ?? "0.00"} ${list.values.firstWhereOrNull((element) => element.name == "POINT_UNIT")?.data ?? ""} will be expired on ${DateFormat('d MMM yyyy').format(reward!.me.pointExpiredOn!)}",
                          style: TextStyle(
                              color: widget.theme == "L"
                                  ? ThemeColors.dark
                                  : ThemeColors.light,
                              fontSize: h5),
                        ),
                    ],
                  ),
                ),
              );
            }
          }
          return Container();
        });
  }
}
