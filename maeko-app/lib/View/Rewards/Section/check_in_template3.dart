import 'package:amverton/Constant/theme.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Home/check_in.dart';
import 'package:amverton/View/Rewards/rewards_page.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class CheckInTemplate3 extends StatefulWidget {
  final String theme;

  const CheckInTemplate3({Key? key, required this.theme}) : super(key: key);

  @override
  State<CheckInTemplate3> createState() => _CheckInTemplate3State();
}

class _CheckInTemplate3State extends State<CheckInTemplate3> {
  late Future boxFuture;
  late Box box;
  late Box boxGlobal;

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');

    return await Hive.openBox<Asset>('REWARDS+CHECK_IN+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            bool isLogin = box.get("is_login") ?? false;
            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            String? innerBackgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "INNER_BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? innerBackground = list.values.firstWhereOrNull((element) =>
                element.name == "INNER_BACKGROUND_$innerBackgroundType");

            if (list.isNotEmpty) {
              //todo gif possible can take from asset
              return Container(
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(
                      "assets/images/checkin_background.png",
                    ),
                    fit: BoxFit.cover,
                  ),
                ),
                child: Container(
                  margin: EdgeInsets.symmetric(vertical: defaultPadding),
                  width: phoneWidth,
                  height: phoneHeight / 2.4,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        () {
                          try {
                            double point = double.parse(reward!.me.point);
                            return "${point.toStringAsFixed(0)}";
                          } catch (e) {
                            return "0";
                          }
                        }(),
                        style: TextStyle(
                          fontSize: 40,
                          fontWeight: FontWeight.bold,
                          fontFamily: fontFamily1,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      Text(
                        "Points",
                        style: TextStyle(
                          fontSize: h1,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(
                          left: spacingHeightLarge,
                          right: spacingHeightLarge,
                          top: spacingHeightLarge,
                          bottom: spacingHeightLarge + 16,
                        ),
                        child: reward != null &&
                                reward?.checkIn?.checkInEvent != null
                            ? Container(
                                width: phoneWidth,
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(20)),
                                child: Container(
                                  padding: EdgeInsets.all(defaultInnerPadding),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "${reward!.checkIn!.totalDaysCheckedIn} Day(s) of check in",
                                        style: TextStyle(
                                          color: widget.theme == "L"
                                              ? ThemeColors.dark
                                              : ThemeColors.light,
                                          fontSize: h3,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      SizedBox(height: spacingHeightSmall),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: List.generate(
                                            reward!.checkIn!.checkInEvent!
                                                .rewards!.length, (index) {
                                          return Container(
                                            padding: EdgeInsets.all(5),
                                            width: phoneWidth / 9,
                                            child: Column(
                                              children: [
                                                Stack(
                                                  children: [
                                                    CircleAvatar(
                                                      backgroundColor:
                                                          ThemeColors.disabled,
                                                      radius: 18,
                                                      backgroundImage:
                                                          CachedNetworkImageProvider(
                                                        reward!
                                                                .checkIn!
                                                                .checkInEvent!
                                                                .rewards![index]
                                                                .image ??
                                                            "",
                                                      ),
                                                    ),
                                                    if (reward!
                                                            .checkIn!
                                                            .checkInEvent!
                                                            .rewards![index]
                                                            .isClaimed !=
                                                        1)
                                                      CircleAvatar(
                                                        backgroundColor:
                                                            ThemeColors.disabled
                                                                .withOpacity(
                                                                    0.8),
                                                        radius: 18,
                                                      ),
                                                  ],
                                                ),
                                                SizedBox(height: 3),
                                                Text(
                                                  reward!.checkIn!.checkInEvent!
                                                      .rewards![index].title,
                                                  style: TextStyle(
                                                      color: ThemeColors.dark,
                                                      fontSize: h5),
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ],
                                            ),
                                          );
                                        }),
                                      ),
                                      SizedBox(height: spacingHeightMedium),
                                      DefaultButton(
                                        text: () {
                                          if (isLogin) {
                                            if (reward?.checkIn
                                                    ?.hasCheckedInToday !=
                                                0) {
                                              // todo temp hardcode for obriens (should take from assets, but merchant portal dont hv this template yet)
                                              return "See you tomorrow!";
                                            } else {
                                              return list.values
                                                      .firstWhereOrNull(
                                                          (element) =>
                                                              element.name ==
                                                              "MEMBER_BUTTON_TEXT")
                                                      ?.data ??
                                                  "";
                                            }
                                          } else {
                                            return list.values
                                                    .firstWhereOrNull(
                                                        (element) =>
                                                            element.name ==
                                                            "GUEST_BUTTON_TEXT")
                                                    ?.data ??
                                                "";
                                          }
                                        }(),
                                        textColor: () {
                                          if (isLogin &&
                                              reward!.checkIn!
                                                      .hasCheckedInToday !=
                                                  0) {
                                            return ThemeColors.gray;
                                          }
                                          return null;
                                        }(),
                                        buttonColor: () {
                                          if (isLogin) {
                                            if (reward!.checkIn!.checkInEvent!
                                                        .type ==
                                                    "WEEKLY" &&
                                                reward!.checkIn!
                                                        .hasCheckedInToday !=
                                                    0) {
                                              return ThemeColors.disabled;
                                            }
                                          }
                                          return important_variables
                                                      .projectName ==
                                                  "obriens"
                                              ? ThemeColors.secondaryDark
                                              : ThemeColors.primaryDark;
                                        }(),
                                        onPressed: () {
                                          if (isLogin) {
                                            if (reward!.checkIn!.checkInEvent!
                                                    .type ==
                                                "WEEKLY") {
                                              if (reward!.checkIn!
                                                      .hasCheckedInToday ==
                                                  0) {
                                                checkIn();
                                              }
                                            } else {
                                              Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                      builder: (BuildContext
                                                              context) =>
                                                          CheckIn(
                                                            checkInEvent: reward!
                                                                .checkIn!
                                                                .checkInEvent!,
                                                          )));
                                            }
                                          } else {
                                            Navigator.of(context,
                                                    rootNavigator: true)
                                                .pushNamed(
                                                    "/login/${loginAsset.template}");
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              )
                            : Container(
                                width: phoneWidth,
                                child: ClipRRect(
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(20)),
                                  child: CachedNetworkImage(
                                    imageUrl: list.values
                                            .firstWhereOrNull((element) =>
                                                element.name ==
                                                "COMING_SOON_IMAGE")
                                            ?.data ??
                                        "",
                                    fit: BoxFit.cover,
                                    height: phoneHeight / 4.5,
                                  ),
                                ),
                              ),
                      ),
                    ],
                  ),
                ),
              );
            }
          }
          return Container();
        });
  }

  void checkIn() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    Map? data = await apiPost.checkIn();
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          RewardsPage.onRefresh(context);
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }
}
