import 'dart:async';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Rewards/rewards_page.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class VoucherTemplate2 extends StatefulWidget {
  final String theme;

  const VoucherTemplate2({Key? key, required this.theme}) : super(key: key);

  @override
  _VoucherTemplate2State createState() => _VoucherTemplate2State();
}

class _VoucherTemplate2State extends State<VoucherTemplate2> {
  late Future boxFuture;
  late Box box;
  late Box boxGlobal;

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('REWARDS+VOUCHERS+2');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            bool isLogin = box.get("is_login") ?? false;
            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            if (list.isNotEmpty) {
              if (reward != null && reward?.pointsRewards != null) {
                return Container(
                  padding: EdgeInsets.symmetric(vertical: defaultPadding),
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                          padding:
                              EdgeInsets.symmetric(horizontal: defaultPadding),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                list.values
                                        .firstWhereOrNull((element) =>
                                            element.name == "TITLE")
                                        ?.data ??
                                    "",
                                style: TextStyle(
                                  color: widget.theme == "L"
                                      ? ThemeColors.dark
                                      : ThemeColors.light,
                                  fontSize: h1,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              if (isLogin)
                                TextButton.icon(
                                  onPressed: () {
                                    if (isLogin) {
                                      Navigator.pushNamed(context,
                                          "/rewards_list/${accountAsset.firstWhereOrNull((element) => element.sectionName == "VOUCHERS")?.template ?? ""}",
                                          arguments: {
                                            "theme": accountAsset
                                                    .firstWhereOrNull(
                                                        (element) =>
                                                            element
                                                                .sectionName ==
                                                            "VOUCHERS")
                                                    ?.theme ??
                                                "L",
                                          });
                                    } else {
                                      Navigator.of(context, rootNavigator: true)
                                          .pushNamed(
                                              "/login/${loginAsset.template}");
                                    }
                                  },
                                  icon: ImageIcon(
                                    AssetImage(
                                        "assets/icons/${boxGlobal.get("iconSet")}/History Icon.png"),
                                    size: 22,
                                    color: ThemeColors.primaryDark,
                                  ),
                                  label: Text(
                                    "My Rewards",
                                    style: TextStyle(
                                      color: ThemeColors.primaryDark,
                                      fontSize: h4,
                                    ),
                                  ),
                                )
                            ],
                          )),
                      Column(
                        children: List.generate(reward!.pointsRewards!.length,
                            (index) {
                          return Container(
                            height: phoneHeight / 3.5,
                            margin: EdgeInsets.fromLTRB(defaultPadding, 0,
                                defaultPadding, defaultInnerPadding),
                            padding: EdgeInsets.all(defaultInnerPadding),
                            decoration: BoxDecoration(
                              color: ThemeColors.light,
                              borderRadius: BorderRadius.circular(15),
                              border: Border.all(color: ThemeColors.disabled),
                            ),
                            child: Column(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(15),
                                  child: CachedNetworkImage(
                                    imageUrl: reward!.pointsRewards![index]
                                            .voucher.image ??
                                        "",
                                    width: phoneWidth,
                                    height: phoneHeight / 7.5,
                                    fit: BoxFit.fitWidth,
                                    placeholder: (context, url) {
                                      return Center(
                                        child: CircularProgressIndicator(
                                          color: ThemeColors.primaryDark,
                                        ),
                                      );
                                    },
                                    errorWidget: (context, url, error) {
                                      return Icon(
                                        Icons.error_outline_outlined,
                                        color: ThemeColors.primaryDark,
                                      );
                                    },
                                  ),
                                ),
                                SizedBox(height: spacingHeightMedium),
                                Row(
                                  children: [
                                    Expanded(
                                      flex: 2,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          AutoSizeText(
                                            reward!.pointsRewards![index]
                                                .voucher.title,
                                            style: TextStyle(
                                                color: ThemeColors.dark,
                                                fontSize: h4),
                                            maxLines: 1,
                                          ),
                                          AutoSizeText(
                                            reward!.pointsRewards![index]
                                                .voucher.description,
                                            style: TextStyle(
                                                color: ThemeColors.gray,
                                                fontSize: h4),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          SizedBox(height: spacingHeightSmall),
                                          Text(
                                            "${reward!.pointsRewards![index].point} Points",
                                            style: TextStyle(
                                              color: ThemeColors.primaryDark,
                                              fontSize: h2,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(width: spacingWidth),
                                    Expanded(
                                      child: DefaultButton(
                                        borderRadius: 15,
                                        text: list.values
                                                .firstWhereOrNull((element) =>
                                                    element.name ==
                                                    "REDEEM_BUTTON_TEXT")
                                                ?.data ??
                                            "",
                                        textColor: reward!.pointsRewards![index]
                                                    .isEligible ==
                                                true
                                            ? null
                                            : ThemeColors.dark,
                                        buttonColor: reward!
                                                    .pointsRewards![index]
                                                    .isEligible ==
                                                true
                                            ? ThemeColors.primaryDark
                                            : ThemeColors.disabled,
                                        clickable: reward!
                                            .pointsRewards![index].isEligible,
                                        onPressed: () {
                                          if (isLogin) {
                                            if (reward!.pointsRewards![index]
                                                    .isEligible ==
                                                true) {
                                              redeemPointsReward(reward!
                                                  .pointsRewards![index].id);
                                            }
                                          } else {
                                            Navigator.of(context,
                                                    rootNavigator: true)
                                                .pushNamed(
                                                    "/login/${loginAsset.template}");
                                          }
                                        },
                                      ),
                                    )
                                  ],
                                )
                              ],
                            ),
                          );
                        }),
                      ),
                    ],
                  ),
                );
              }
            }
          }
          return Container();
        });
  }

  void redeemPointsReward(int id) async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    Map? data = await apiPost.redeemPointsRewards(id);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        AwesomeDialog(
          context: context,
          dialogType: DialogType.success,
          animType: AnimType.bottomSlide,
          dismissOnTouchOutside: false,
          dialogBackgroundColor: Colors.white,
          showCloseIcon: true,
          padding: EdgeInsets.symmetric(
              vertical: defaultPadding, horizontal: verticalPaddingSmall),
          body: Column(
            children: [
              Text(
                "You have redeemed this voucher",
                style: TextStyle(
                  fontSize: h1,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: spacingHeightMedium),
              Text(
                "Head over to my voucher to use it",
                style: TextStyle(fontSize: h3),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: spacingHeightMedium),
            ],
          ),
          btnOkColor: ThemeColors.primaryDark,
          btnOkText: "View",
          btnOkOnPress: () {
            Navigator.pushNamed(context,
                "/rewards_list/${accountAsset.firstWhereOrNull((element) => element.sectionName == "VOUCHERS")?.template ?? ""}",
                arguments: {
                  "theme": accountAsset
                          .firstWhereOrNull(
                              (element) => element.sectionName == "VOUCHERS")
                          ?.theme ??
                      "L",
                }).then((value) {
              RewardsPage.onRefresh(context);
            });
          },
        ).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }
}
