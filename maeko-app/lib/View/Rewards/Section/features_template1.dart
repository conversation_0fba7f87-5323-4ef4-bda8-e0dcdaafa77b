import 'dart:async';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Rewards/reward_redeem_detail_template1.dart';
import 'package:amverton/View/Rewards/rewards_page.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class FeaturesTemplate1 extends StatefulWidget {
  final String theme;

  const FeaturesTemplate1({Key? key, required this.theme}) : super(key: key);

  @override
  _FeaturesTemplate1State createState() => _FeaturesTemplate1State();
}

class _FeaturesTemplate1State extends State<FeaturesTemplate1> {
  late Future boxFuture;
  late Box box;
  late Box boxGlobal;

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('REWARDS+FEATURED+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            bool isLogin = box.get("is_login") ?? false;
            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            if (list.isNotEmpty) {
              if (reward != null) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                        padding: EdgeInsets.only(
                            top: spacingHeightSmall,
                            left: spacingHeightLarge,
                            right: spacingHeightLarge),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              list.values
                                      .firstWhereOrNull(
                                          (element) => element.name == "TITLE")
                                      ?.data ??
                                  "",
                              style: TextStyle(
                                color: widget.theme == "L"
                                    ? ThemeColors.dark
                                    : ThemeColors.light,
                                fontSize: h1,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        )),
                    Container(
                      height: phoneHeight * 0.23,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        padding: EdgeInsets.symmetric(
                          horizontal: defaultPadding,
                          vertical: defaultInnerPadding,
                        ),
                        shrinkWrap: true,
                        itemCount: reward!.pointsRewards!.length,
                        itemBuilder: (BuildContext context, int index) {
                          return rewardVoucher(context, index);
                        },
                      ),
                    ),
                  ],
                );
              }
            }
          }
          return Container();
        });
  }

  Widget rewardVoucher(BuildContext context, int index) {
    return GestureDetector(
      onTap: () {
        bool isLogin = box.get("is_login") ?? false;
        if (isLogin) {
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (BuildContext context) => RewardsRedeemDetailTemplate1(
                  pointsReward: reward!.pointsRewards![index],
                  tag: "reward/features/redeem/$index",
                  buttonFunction: redeemPointsReward,
                ),
              ));
        } else {
          Navigator.of(context, rootNavigator: true)
              .pushNamed("/login/${loginAsset.template}");
        }
      },
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 8),
        width: phoneWidth * 0.7,
        decoration: BoxDecoration(
          color: ThemeColors.light,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 6.0,
              spreadRadius: 2.0,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              child: Hero(
                tag: "reward/features/redeem/$index",
                child: CachedNetworkImage(
                  imageUrl: reward!.pointsRewards![index].voucher.image ?? "",
                  fit: BoxFit.cover,
                  width: phoneWidth,
                  height: phoneWidth / 3.5,
                  placeholder: (context, url) {
                    return Center(
                      child: CircularProgressIndicator(
                        color: ThemeColors.primaryDark,
                      ),
                    );
                  },
                  errorWidget: (context, url, error) {
                    return Icon(
                      Icons.error_outline_outlined,
                      color: ThemeColors.primaryDark,
                    );
                  },
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 6,
                    child: Text(
                      reward!.pointsRewards![index].voucher.title,
                      style: TextStyle(
                        color: ThemeColors.dark,
                        fontSize: h4,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      "${reward!.pointsRewards![index].point} coins",
                      style: TextStyle(
                        color: ThemeColors.primaryDark,
                        fontSize: h4,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.end,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void redeemPointsReward(int id, BuildContext context) async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    Map? data = await apiPost.redeemPointsRewards(id);
    EasyLoading.dismiss();

    print("run?");

    if (data != null) {
      if (data['code'] == 200) {
        AwesomeDialog(
          context: context,
          dialogType: DialogType.success,
          animType: AnimType.bottomSlide,
          dismissOnTouchOutside: false,
          dialogBackgroundColor: Colors.white,
          showCloseIcon: true,
          padding: EdgeInsets.symmetric(
              vertical: defaultPadding, horizontal: verticalPaddingSmall),
          body: Column(
            children: [
              Text(
                "You have redeemed this voucher",
                style: TextStyle(
                  fontSize: h1,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: spacingHeightMedium),
              Text(
                "Head over to my voucher to use it",
                style: TextStyle(fontSize: h3),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: spacingHeightMedium),
            ],
          ),
          btnOkColor: important_variables.projectName == "obriens"
              ? ThemeColors.secondaryDark
              : ThemeColors.primaryDark,
          btnOkText: "View",
          btnOkOnPress: () {
            //todo need to get from asset
            // Navigator.pushNamed(context,
            //     "/rewards_list/${accountAsset.firstWhereOrNull((element) => element.sectionName == "VOUCHERS")?.template ?? ""}",
            //     arguments: {
            //       "theme": accountAsset
            //               .firstWhereOrNull(
            //                   (element) => element.sectionName == "VOUCHERS")
            //               ?.theme ??
            //           "L",
            //     }).then((value) {
            //   RewardsPage.onRefresh(context);
            // });
            Navigator.pushNamed(context, "/rewards_list/3", arguments: {
              "theme": accountAsset
                      .firstWhereOrNull(
                          (element) => element.sectionName == "VOUCHERS")
                      ?.theme ??
                  "L",
            }).then((value) {
              RewardsPage.onRefresh(context);
            });
          },
        ).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }
}
