import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/voucher.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Rewards/rewards_code.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class RewardsDetailTemplate1 extends StatefulWidget {
  final int id;
  final Object tag;

  const RewardsDetailTemplate1({
    Key? key,
    required this.id,
    required this.tag,
  }) : super(key: key);

  @override
  _RewardsDetailTemplate1State createState() => _RewardsDetailTemplate1State();
}

class _RewardsDetailTemplate1State extends State<RewardsDetailTemplate1> {
  Box boxGlobal = Hive.box("boxGlobal");
  late Future future;
  Voucher? voucher;
  String qrCode = "";

  @override
  void initState() {
    super.initState();
    future = getVoucherDetail();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeColors.light,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: Text(
          "Rewards Detail",
          style: TextStyle(color: ThemeColors.light),
        ),
        iconTheme: IconThemeData(color: ThemeColors.light),
      ),
      body: FutureBuilder(
          future: future,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            if (snapshot.connectionState == ConnectionState.done) {
              if (snapshot.hasData) {
                voucher = snapshot.data['data']['voucher'] == null
                    ? null
                    : Voucher.fromJson(snapshot.data['data']['voucher']);
                qrCode = snapshot.data['data']['voucher']['qr_code'] ?? "";

                if (voucher != null) {
                  return SingleChildScrollView(
                    child: Column(
                      children: [
                        Container(
                          padding: EdgeInsets.only(top: verticalPaddingLarge),
                          decoration: const BoxDecoration(
                              //todo temporary hardcode bg color
                              color: const Color(0xff20222B)),
                          child: Container(
                            margin: EdgeInsets.fromLTRB(
                                defaultPadding,
                                defaultPadding,
                                defaultPadding,
                                verticalPaddingSmall),
                            padding: EdgeInsets.all(defaultPadding),
                            width: phoneWidth,
                            decoration: BoxDecoration(
                              color: ThemeColors.light,
                              border: Border.all(color: ThemeColors.disabled),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Column(
                              children: [
                                Hero(
                                  tag: widget.tag,
                                  child: CachedNetworkImage(
                                    imageUrl: voucher!.image ?? "",
                                    placeholder: (context, url) {
                                      return Padding(
                                        padding: EdgeInsets.all(defaultPadding),
                                        child: CircularProgressIndicator(
                                          color: ThemeColors.primaryDark,
                                        ),
                                      );
                                    },
                                    errorWidget: (context, url, error) {
                                      return Padding(
                                        padding: EdgeInsets.all(defaultPadding),
                                        child: Icon(
                                          Icons.error_outline_outlined,
                                          color: ThemeColors.primaryDark,
                                        ),
                                      );
                                    },
                                  ),
                                ),
                                SizedBox(height: spacingHeightMedium),
                                Text(
                                  voucher!.title,
                                  style: TextStyle(
                                    color: ThemeColors.dark,
                                    fontSize: h1,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                SizedBox(height: spacingHeightSmall),
                                Text(
                                  voucher!.description,
                                  style: TextStyle(
                                      color: ThemeColors.dark, fontSize: h3),
                                ),
                                Text(
                                  "Expired On " + voucher!.endOn,
                                  style: TextStyle(
                                      color: ThemeColors.gray, fontSize: h4),
                                ),
                                Text(
                                  "Redeem at any counter",
                                  style: TextStyle(
                                      color: ThemeColors.dark, fontSize: h3),
                                ),
                                SizedBox(height: spacingHeightMedium),
                                DefaultButton(
                                  text: "Use Voucher",
                                  buttonColor: ThemeColors.primaryDark,
                                  onPressed: () {
                                    Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (BuildContext context) =>
                                                RewardsCode(
                                                  voucher: voucher!,
                                                  qrCode: qrCode,
                                                )));
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                        Container(
                          color: ThemeColors.disabled,
                          height: defaultInnerPadding,
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(
                            vertical: defaultInnerPadding,
                            horizontal: defaultPadding,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "How it works",
                                style: TextStyle(
                                  color: ThemeColors.dark,
                                  fontSize: h1,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: spacingHeightMedium),
                              const Text("Points"),
                              SizedBox(height: spacingHeightSmall),
                              const Text(
                                "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
                              ),
                            ],
                          ),
                        ),
                        Container(
                          color: ThemeColors.disabled,
                          height: defaultInnerPadding,
                        ),
                        ListTile(
                          onTap: () {
                            Navigator.pushNamed(context,
                                "/faq/${accountAsset.firstWhereOrNull((element) => element.sectionName == "QUESTIONS")?.template ?? ""}",
                                arguments: {
                                  "theme": accountAsset
                                          .firstWhereOrNull((element) =>
                                              element.sectionName ==
                                              "QUESTIONS")
                                          ?.theme ??
                                      "L",
                                });
                          },
                          title: const Text("FAQ"),
                          trailing: ImageIcon(AssetImage(
                              "assets/icons/${boxGlobal.get("iconSet")}/Standard_Moreleft.png")),
                        ),
                        SizedBox(height: defaultPadding),
                      ],
                    ),
                  );
                }
              }
            }
            return Container();
          }),
    );
  }

  Future<Map?> getVoucherDetail() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.voucherDetail(widget.id);
    return data;
  }
}
