import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_indicator/carousel_indicator.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:hive/hive.dart';

class IntroTemplate1 extends StatefulWidget {
  final String theme;

  const IntroTemplate1({Key? key, required this.theme}) : super(key: key);

  @override
  _IntroTemplate1State createState() => _IntroTemplate1State();
}

class _IntroTemplate1State extends State<IntroTemplate1> {
  late Future future;
  late Box boxGlobal;

  int currentIndex = 0;
  PageController pageController = PageController();
  List<String> imageList = [];
  List<String> titleList = [];
  List<String> subtitleList = [];
  List<String> nextButtonTextList = [];

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  Future<Box> openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    final boxAsset = await Hive.openBox<Asset>('INTRO+MAIN+1');

    for (Asset each in boxAsset.values) {
      if (each.data != null) {
        if (each.name.contains("IMAGE")) {
          imageList.add(each.data ?? "");
        } else if (each.name.contains("TITLE") && each.name[0] == "T") {
          titleList.add(each.data ?? "");
        } else if (each.name.contains("SUB")) {
          subtitleList.add(each.data ?? "");
        } else if (each.name.contains("NEXT_BUTTON")) {
          nextButtonTextList.add(each.data ?? "Next");
        }
      }
    }
    return boxAsset;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            return Scaffold(
              body: Stack(
                children: [
                  PageView(
                    physics: const ClampingScrollPhysics(),
                    controller: pageController,
                    children: List.generate(imageList.length, (index) {
                      return Container(
                        width: phoneWidth,
                        height: phoneHeight,
                        child: CachedNetworkImage(
                          imageUrl: imageList[index],
                          fit: BoxFit.cover,
                        ),
                      );
                    }),
                    onPageChanged: (index) {
                      setState(() {
                        currentIndex = index;
                      });
                    },
                  ),
                  Positioned(
                    top: phoneHeight * 0.7,
                    child: Container(
                      width: phoneWidth,
                      height: phoneHeight * 0.3,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20)),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Spacer(),
                          if (titleList.isNotEmpty)
                            Text(
                              titleList[currentIndex],
                              style: TextStyle(
                                color: ThemeColors.dark,
                                fontSize: h1,
                                fontWeight: FontWeight.bold,
                                fontFamily: fontFamily1,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          SizedBox(height: defaultPadding),
                          if (subtitleList.isNotEmpty)
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 32.0),
                              child: Text(
                                subtitleList[currentIndex],
                                style: TextStyle(
                                  color: ThemeColors.dark,
                                  fontSize: h3,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 3,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          Spacer(),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: DefaultButton(
                              text: nextButtonTextList[currentIndex],
                              buttonColor: ThemeColors.primaryDark,
                              onPressed: () {
                                if (currentIndex == imageList.length - 1) {
                                  Navigator.pushNamedAndRemoveUntil(
                                    context,
                                    "/my_home_page",
                                    (Route<dynamic> route) => false,
                                    arguments: {'dailyPopUp': true},
                                  );
                                  ;
                                } else {
                                  setState(() {
                                    pageController.animateToPage(
                                      currentIndex + 1,
                                      duration:
                                          const Duration(milliseconds: 500),
                                      curve: Curves.ease,
                                    );
                                  });
                                }
                              },
                            ),
                          ),
                          SizedBox(
                            height: 16,
                          )
                        ],
                      ),
                    ),
                  )
                ],
              ),
            );
          } else {
            return Container();
          }
        });
  }
}
