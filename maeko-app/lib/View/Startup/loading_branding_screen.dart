import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/important_variables.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Repository/connectivity_wrapper.dart';
import 'package:amverton/View/Startup/splash_screen.dart';
import 'package:amverton/main.dart';

class LoadingBrandScreen extends StatefulWidget {
  const LoadingBrandScreen({super.key});

  @override
  State<LoadingBrandScreen> createState() => _LoadingBrandScreenState();
}

class _LoadingBrandScreenState extends State<LoadingBrandScreen> {
  final splashDuration = 2;

  @override
  void initState() {
    super.initState();
    init();
  }

  Future<void> init() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none && mounted) {
      final state = navigatorKey.currentState?.context
          .findAncestorStateOfType<ConnectivityWrapperState>();
      if (state != null) {
        state.onConnectivityChanged(
            connectivityResult != ConnectivityResult.none);
      }
    } else {
      loadSplashWidget();
    }
  }

  loadSplashWidget() async {
    var duration = Duration(seconds: splashDuration);
    return Timer(duration, () {
      if (mounted) {
        Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => SplashScreen()));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        height: phoneHeight,
        width: phoneWidth,
        padding: EdgeInsets.only(bottom: phoneHeight / 5),
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(brandScreen),
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }
}
