import 'dart:async';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/important_variables.dart';
import 'package:amverton/Model/app_settings.dart';
import 'package:amverton/Repository/notification_service.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Constant/theme.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/user.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/first_time_open_error.dart';
import 'package:amverton/main.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hive/hive.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:collection/collection.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  //bool firstOpen = true;
  //bool firstOpenError = false;
  final notificationService = NotificationService();

  @override
  void initState() {
    super.initState();
    precessing();
  }

  precessing() async {
    await init();
    loadNextPage();
  }

  loadNextPage() async {
    // if (firstOpenError) {
    //   Navigator.pushAndRemoveUntil(
    //     context,
    //     MaterialPageRoute(builder: (context) => FirstTimeOpenError()),
    //     (Route<dynamic> route) => false,
    //   );
    // } else {
      nextPage();
    //}
  }

  init() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
   // firstOpen = prefs.getBool('firstOpen') ?? true;
    // firstOpen = true; //temp

    // clear when user clear apps
    if (!Hive.isBoxOpen('boxMenu')) {
      await Hive.openBox('boxMenu');
    }
    Box boxMenu = await Hive.box('boxMenu');
    await boxMenu.deleteFromDisk();

    // if (firstOpen) {
    //   await Hive.deleteFromDisk();
    // }

    await Hive.openBox('boxMenu');

    await getAssets();
    await getDefaultBar();

    //int? refreshCode = await refreshToken();
    //if (refreshCode != null && refreshCode == 200) await getMe();

    await notificationService.initialise();
    //await notificationService.saveNotificationToken();

    await checkLocationPermission();
  }

  getAssets() async {
    Box box = await Hive.openBox('box');
    Box boxGlobal = await Hive.openBox('boxGlobal');
    List homeSection = boxGlobal.get("homeSection") ?? [];
    List restaurantSection = boxGlobal.get("restaurantSection") ?? [];
    List bookingSection = boxGlobal.get("bookingSection") ?? [];
    List scanQRSection = boxGlobal.get("scanQRSection") ?? [];
    List orderSection = boxGlobal.get("orderSection") ?? [];
    List menuSection = boxGlobal.get("menuSection") ?? [];
    List outletSection = boxGlobal.get("outletSection") ?? [];
    List rewardsSection = boxGlobal.get("rewardsSection") ?? [];
    List accountSection = boxGlobal.get("accountSection") ?? [];

    ApiPost apiPost = ApiPost();
     print("Testt0");
    Map? data = await apiPost.assets();
    print("Testt1");
    if (data != null) {
       print("Testt3");
      String? revisionNumber = data['data']['latest_revision_number'];
      await box.put("revision_number", revisionNumber);

      List<String> countryCode = (data['data']['country_codes'] as List)
          .map((data) => data as String)
          .toList();
      if (countryCode.isNotEmpty) boxGlobal.put("countryCode", countryCode);

      List<Asset> global = (data['data']['globals'] as List)
          .map((data) => Asset.fromJson(data))
          .toList();
      if (global.isNotEmpty) await getGlobal(global);

      AppSettings appSettings = AppSettings.fromJson(data['data']['settings']);
      boxGlobal.put("appSettings", appSettings);

      List<Pages> pages = (data['data']['pages'] as List)
          .map((data) => Pages.fromJson(data))
          .toList();

      Pages restaurant = Pages( id: 1231232,
        name: "RESTAURANT",
        title: "Restaurant" ,
        sequence: 2,
        isMenu: 1,
        sections: []
      );

      Pages scanQr = Pages( id: 12312312412,
        name: "SCANQR",
        title: "Scan QR" ,
        sequence: 3,
        isMenu: 1,
        sections: []
      );
  
      if (pages.isNotEmpty) {
        // if (firstOpen) {
        //   await getIntroWidget(
        //       pages.firstWhere((element) => element.name == "INTRO"));
        // }

        int bookingPageIndex = pages.indexWhere((page) => page.name == "BOOKINGS");
        
        if (bookingPageIndex != -1) {
          // If REWARD exists, insert SCANQR before it
          pages.insert(bookingPageIndex, restaurant);
          pages.insert(bookingPageIndex+1, scanQr);
        }
          // Find index of the existing REWARD page
        pages.removeWhere((page) => page.name == "REWARDS"); 

        await updateAssets(pages);
        await getBottomBarList(pages);
        await getAuthWidget(pages);

        homeSection = pages
                .firstWhereOrNull((element) => element.name == "HOME")
                ?.sections ??
            [];
        boxGlobal.put("homeSection", homeSection);


        restaurantSection = pages
                .firstWhereOrNull((element) => element.name == "RESTAURANT")
                ?.sections ??
            [];
        boxGlobal.put("restaurantSection", restaurantSection);

        bookingSection = pages
                .firstWhereOrNull((element) => element.name == "BOOKINGS")
                ?.sections ??
            [];
        boxGlobal.put("bookingSection", bookingSection);

        scanQRSection = pages
                .firstWhereOrNull((element) => element.name == "SCANQR")
                ?.sections ??
            [];
        boxGlobal.put("scanQRSection", scanQRSection);

        orderSection = pages
                .firstWhereOrNull((element) => element.name == "ORDER")
                ?.sections ??
            [];
        boxGlobal.put("orderSection", orderSection);

        menuSection = pages
                .firstWhereOrNull((element) => element.name == "MENU")
                ?.sections ??
            [];
        boxGlobal.put("menuSection", menuSection);

        outletSection = pages
                .firstWhereOrNull((element) => element.name == "OUTLETS")
                ?.sections ??
            [];
        boxGlobal.put("outletSection", outletSection);

        // rewardsSection = pages
        //         .firstWhereOrNull((element) => element.name == "REWARDS")
        //         ?.sections ??
        //     [];
        // boxGlobal.put("rewardsSection", rewardsSection);

        accountSection = pages
                .firstWhereOrNull((element) => element.name == "ACCOUNT")
                ?.sections ??
            [];
        boxGlobal.put("accountSection", accountSection);
      }
   } else {
      // if (firstOpen) {
      //   setState(() {
      //     firstOpenError = true;
      //   });
      // }
   }

    await getHomeWidget(homeSection);
    await getRestaurantWidget(restaurantSection);
    await getBookingWidget(bookingSection);
    await getOrderWidget(orderSection);
    await getMenuWidget(menuSection);
    await getOutletWidget(outletSection);
    await getRewardsWidget(rewardsSection);
    await getAccountWidget(accountSection);
  }

  checkLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied ||
        permission == LocationPermission.deniedForever) {
      permission = await Geolocator.requestPermission();
    }
    return permission;
  }

  Future<int?> refreshToken() async {
    ApiPost apiPost = ApiPost();
    int? refreshCode = await apiPost.accessTokenRefresh();
    print("refresh token: $refreshCode");
    return refreshCode;
  }

  Future<Map?> getMe() async {
    Box box = await Hive.openBox("box");
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.me();

    if (data != null && data['data'] != null) {
      User user = User.fromJson(data['data']['me']);
      box.put("user", user);

      String barcode = data['data']['barcode'];
      box.put("barcode", barcode);

      int totalDaysCheckIn =
          data['data']['check_in']?['total_days_checked_in'] ?? 0;
      box.put("total_days_checked_in", totalDaysCheckIn);
    }
    return data;
  }

  // Future<bool> loadCacheImage() async {
  //   String url =
  //       "https://effigis.com/wp-content/uploads/2015/02/Airbus_Pleiades_50cm_8bit_RGB_Yogyakarta.jpg";
  //   await DefaultCacheManager().downloadFile(url).then((value) {});
  //   return true;
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey,
      body: Container(
        height: phoneHeight,
        width: phoneWidth,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(loadingImage),
            fit: BoxFit.cover,
          ),
        ),
        child: Column(
          children: [
            // Expanded(
            //   child: Image.asset(
            //     loadingGif,
            //     height: phoneHeight,
            //     width: phoneWidth,
            //   ),
            // ),
            Container(
              padding: EdgeInsets.only(
                bottom: bottomPaddingWithoutBar,
                left: bottomPaddingWithoutBar,
                right: bottomPaddingWithoutBar,
              ),
              child: Column(
                children: [
                  TweenAnimationBuilder<double>(
                    tween: Tween<double>(begin: 0, end: 100),
                    duration: Duration(seconds: 2),
                    builder: (context, value, child) {
                      return Text(
                        '${value.toInt()}% Loading',
                        style: TextStyle(fontSize: h2,fontFamily: fontFamily2, color: Colors.white),
                      );
                    },
                  ),      
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> nextPage() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String lastVisitDate = prefs.getString("lastVisit") ?? "";
    String todayDate =
        "${DateTime.now().year}-${DateTime.now().month}-${DateTime.now().day}";

    // first time install
  //  if (firstOpen && mounted) {
       if (mounted) {
   //   await prefs.setBool('firstOpen', false);
      await prefs.setString("lastVisit", todayDate);

     // if (introWidget == null) {
        Navigator.pushNamedAndRemoveUntil(
          context,
          "/my_home_page",
          (Route<dynamic> route) => false,
          arguments: {'dailyPopUp': true},
        );
      // } else {
      //   Navigator.of(context).pushReplacement(
      //       MaterialPageRoute(builder: (context) => introWidget!));
      // }
    } else {
      if (mounted) {
        // used today
        if (todayDate == lastVisitDate) {
          Navigator.pushNamedAndRemoveUntil(
            context,
            "/my_home_page",
            (Route<dynamic> route) => false,
            arguments: {'dailyPopUp': false},
          );
        } else {
          // first time use today
          await prefs.setString("lastVisit", todayDate);
          Navigator.pushNamedAndRemoveUntil(
            context,
            "/my_home_page",
            (Route<dynamic> route) => false,
            arguments: {'dailyPopUp': false},
          );
        }
      }
    }
  }
}
