import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/notifications.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/View/Custom%20Widgets/empty_list.dart';
import 'package:amverton/View/Notification/notification_detail_page.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';

class NotificationPage extends StatefulWidget {
  const NotificationPage({Key? key}) : super(key: key);

  @override
  _NotificationPageState createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage> {
  Box boxGlobal = Hive.box("boxGlobal");
  late Future future;

  final ScrollController scrollController = ScrollController();
  bool hasMorePages = false;
  int nextPage = 0;
  StreamController streamController = StreamController();
  List<Notifications> notificationList = [];

  @override
  void initState() {
    super.initState();
    getNotification();

    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (hasMorePages) getNotification();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeColors.light,
      appBar: AppBar(title: const Text("Notification")),
      body: StreamBuilder(
          stream: streamController.stream,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            if (snapshot.hasError) {
              return Container();
            } else {
              if (notificationList.isNotEmpty) {
                return Container(
                  padding: EdgeInsets.all(defaultPadding),
                  child: Column(
                      children: List.generate(notificationList.length, (index) {
                    return Container(
                      margin: EdgeInsets.only(bottom: defaultInnerPadding),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: ThemeColors.disabled),
                      ),
                      child: ListTile(
                        dense: true,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: defaultPadding,
                        ),
                        onTap: () {
                          markAsRead(notificationList[index]);
                        },
                        title: Text(
                          notificationList[index].title,
                          style: TextStyle(
                            fontWeight: notificationList[index].readAt == null
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                        subtitle: Text(
                          notificationList[index].message,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        trailing: notificationList[index].createdAt != null
                            ? Text(
                                DateFormat('d MMM yyyy\nhh:mm aa')
                                    .format(notificationList[index].createdAt!),
                                style: TextStyle(
                                  fontSize: h4,
                                ),
                                textAlign: TextAlign.end,
                              )
                            : null,
                      ),
                    );
                  })),
                );
              } else {
                return EmptyList(theme: "L");
              }
            }
          }),
    );
  }

  Future<Map?> getNotification() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.notificationList(nextPage);

    if (data != null && data['data'] != null) {
      List<Notifications> newList = (data['data']['notifications'] as List)
          .map((data) => Notifications.fromJson(data))
          .toList();

      hasMorePages = data["data"]["has_more_pages"];
      if (hasMorePages) {
        nextPage = data["data"]["next_page"] ?? 0;
      }
      notificationList.addAll(newList);
      streamController.add(data);
    }
    return data;
  }

  void markAsRead(Notifications notification) async {
    EasyLoading.show();
    if (notification.readAt != null) {
      EasyLoading.dismiss();
      Navigator.push(
          context,
          MaterialPageRoute(
            builder: (BuildContext context) =>
                NotificationDetailPage(notification: notification),
          ));
    } else {
      ApiPost apiPost = ApiPost();
      Map? data = await apiPost.markAsRead(notification.id);
      print(data);
      EasyLoading.dismiss();

      Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (BuildContext context) =>
                      NotificationDetailPage(notification: notification)))
          .then((value) {
        setState(() {
          notificationList.clear();
          future = getNotification();
        });
      });
    }
  }
}
