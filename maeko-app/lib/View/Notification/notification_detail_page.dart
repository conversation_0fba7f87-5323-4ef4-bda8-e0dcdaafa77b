import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/notifications.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';

class NotificationDetailPage extends StatefulWidget {
  final Notifications notification;

  const NotificationDetailPage({Key? key, required this.notification})
      : super(key: key);

  @override
  _NotificationDetailPageState createState() => _NotificationDetailPageState();
}

class _NotificationDetailPageState extends State<NotificationDetailPage> {
  Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeColors.light,
      appBar: AppBar(title: const Text("Notification")),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(vertical: defaultPadding),
              child: Text(
                widget.notification.title,
                style: TextStyle(
                    color: ThemeColors.dark,
                    fontSize: h1,
                    fontWeight: FontWeight.bold),
              ),
            ),
            Text(
              widget.notification.message,
              style: TextStyle(
                color: ThemeColors.dark,
                fontSize: h3,
              ),
            ),
            SizedBox(height: spacingHeightSmall),
            if (widget.notification.image != null)
              CachedNetworkImage(
                imageUrl: widget.notification.image!,
                placeholder: (context, url) {
                  return Padding(
                    padding: EdgeInsets.all(defaultPadding),
                    child: CircularProgressIndicator(
                      color: ThemeColors.primaryDark,
                    ),
                  );
                },
                errorWidget: (context, url, error) {
                  return Container();
                },
              ),
            SizedBox(height: spacingHeightMedium),
            if (widget.notification.createdAt != null)
              Text(
                DateFormat('d MMM yyyy HH:mm')
                    .format(widget.notification.createdAt!),
                style: TextStyle(
                  color: ThemeColors.gray,
                  fontSize: h4,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
