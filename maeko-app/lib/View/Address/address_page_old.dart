import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/address.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/empty_list.dart';
import 'package:hive/hive.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class AddressPage extends StatefulWidget {
  @override
  State<AddressPage> createState() => _AddressPageState();
}

class _AddressPageState extends State<AddressPage> {
  late Future future;
  Box boxGlobal = Hive.box("boxGlobal");
  List<Address> addressList = [];

  @override
  void initState() {
    super.initState();
    future = getAddressList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeColors.light,
      appBar: AppBar(title: const Text("My Addresses")),
      body: FutureBuilder(
          future: future,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            if (snapshot.connectionState == ConnectionState.done) {
              if (addressList.isNotEmpty) {
                return SingleChildScrollView(
                  padding: EdgeInsets.all(defaultPadding),
                  child: Column(
                    children: [
                      Align(
                        alignment: Alignment.centerRight,
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: spacingHeightSmall),
                          decoration: BoxDecoration(
                            border:
                                Border.all(color: ThemeColors.secondaryDark),
                          ),
                          child: Text(
                            "Default",
                            style: TextStyle(
                              color: ThemeColors.secondaryDark,
                              fontSize: h4,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: spacingHeightMedium),
                      ListView.builder(
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        itemCount: addressList.length,
                        itemBuilder: (BuildContext context, int index) {
                          return GestureDetector(
                            onTap: () {
                              int registerIndex = registerAsset.indexWhere(
                                  (element) => element.sectionName == "STEP_1");
                              // element.sectionName == "STEP_3");
                              if (registerIndex != -1)
                                Navigator.pushNamed(context,
                                    "/edit_address/${registerAsset[registerIndex].template}",
                                    arguments: {
                                      "id": addressList[index].id,
                                    }).then(onRefresh);
                            },
                            child: Container(
                                margin: EdgeInsets.only(
                                    bottom: defaultInnerPadding),
                                padding: EdgeInsets.all(defaultPadding),
                                decoration: BoxDecoration(
                                  color: ThemeColors.light,
                                  borderRadius: BorderRadius.circular(20),
                                  border:
                                      Border.all(color: ThemeColors.disabled),
                                ),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Text(
                                                addressList[index].name,
                                                style: TextStyle(
                                                    fontSize: h3,
                                                    fontWeight:
                                                        FontWeight.bold),
                                              ),
                                              SizedBox(
                                                  width: spacingHeightSmall),
                                              Text(
                                                "${addressList[index].countryCode}${addressList[index].phone}",
                                                style: TextStyle(fontSize: h3),
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: spacingHeightSmall),
                                          Text(
                                            "${addressList[index].address1}, ${addressList[index].address2 ?? ""}\n${addressList[index].postcode} ${addressList[index].city}, ${addressList[index].state?.name ?? ""}",
                                            style: TextStyle(fontSize: h4),
                                          ),
                                        ],
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () {
                                        if (addressList[index].isDefault != 1)
                                          setDefaultAddress(
                                              addressList[index].id);
                                      },
                                      child: Icon(
                                        addressList[index].isDefault == 1
                                            ? Icons.check_circle
                                            : Icons.circle_outlined,
                                        color: ThemeColors.primaryDark,
                                      ),
                                    ),
                                  ],
                                )),
                          );
                        },
                      ),
                    ],
                  ),
                );
              } else {
                return EmptyList(theme: "L");
              }
            }
            return Container();
          }),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.fromLTRB(
          defaultInnerPadding,
          defaultInnerPadding,
          defaultInnerPadding,
          bottomPaddingWithoutBar,
        ),
        child: DefaultButton(
          text: "Add New Address",
          buttonColor: important_variables.projectName == "obriens"
              ? ThemeColors.secondaryDark
              : ThemeColors.primaryDark,
          onPressed: () {
            int index = registerAsset
                .indexWhere((element) => element.sectionName == "STEP_1");
            // element.sectionName == "STEP_3");
            if (index != -1)
              Navigator.pushNamed(
                      context, "/add_address/${registerAsset[index].template}")
                  .then(onRefresh);
          },
        ),
      ),
    );
  }

  FutureOr onRefresh(dynamic value) {
    setState(() {
      future = getAddressList();
    });
  }

  void setDefaultAddress(int id) async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    Map? data = await apiPost.setDefaultAddress(id);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          setState(() {
            future = getAddressList();
          });
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  Future<Map?> getAddressList() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.addressList();

    if (data != null && data['data'] != null) {
      addressList = (data['data']['addresses'] as List)
          .map((data) => Address.fromJson(data))
          .toList();
    }
    return data;
  }
}
