import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Model/country.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/location_service.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_address_phone_field2.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_dropdown2.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield2.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class AddAddressTemplate2 extends StatefulWidget {
  static String routeName = "/add_address/2";

  const AddAddressTemplate2({Key? key}) : super(key: key);

  @override
  State<AddAddressTemplate2> createState() => _AddAddressTemplate2State();
}

class _AddAddressTemplate2State extends State<AddAddressTemplate2> {
  late Future boxFuture;
  late Box box;
  late Box boxGlobal;
  String theme = "L";

  late Future future;
  List<Country> countryList = [];
  TextEditingController nameController = TextEditingController();
  TextEditingController address1Controller = TextEditingController();
  TextEditingController address2Controller = TextEditingController();
  TextEditingController postcodeController = TextEditingController();
  TextEditingController cityController = TextEditingController();
  String? selectedStatesId;
  String? selectedCountryId;
  String selectedPhoneCode = "";
  TextEditingController phoneController = TextEditingController();

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();
    future = getCountryList();
  }

  openBox() async {
    box = await Hive.openBox("box");
    boxGlobal = await Hive.openBox("boxGlobal");

    int index =
        registerAsset.indexWhere((element) => element.sectionName == "STEP_1");
    // registerAsset.indexWhere((element) => element.sectionName == "STEP_3");
    theme = registerAsset[index].theme;
    return await Hive.openBox<Asset>('REGISTER+STEP_1+2');
    // return await Hive.openBox<Asset>('REGISTER+STEP_3+2');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                extendBodyBehindAppBar: true,
                appBar: AppBar(
                  title: Text(
                    "Add Address",
                    style: TextStyle(
                      color:
                          theme == "L" ? ThemeColors.dark : ThemeColors.light,
                    ),
                  ),
                  iconTheme: IconThemeData(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                ),
                body: Container(
                  height: phoneHeight,
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  child: FutureBuilder(
                    future: future,
                    builder: (BuildContext context, AsyncSnapshot snapshot) {
                      if (snapshot.connectionState == ConnectionState.done) {
                        if (countryList.isNotEmpty) {
                          return SingleChildScrollView(
                            physics: const ClampingScrollPhysics(),
                            padding: EdgeInsets.fromLTRB(
                                defaultPadding,
                                verticalPaddingLarge,
                                defaultPadding,
                                bottomPaddingWithoutBar),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(height: spacingHeightMedium),
                                Text(
                                  "Add New Address",
                                  style: TextStyle(
                                    color: ThemeColors.dark,
                                    fontSize: h1,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: spacingHeightSmall),
                                Text(
                                  "Fill in the blanks",
                                  style: TextStyle(
                                    color: theme == "L"
                                        ? ThemeColors.dark
                                        : ThemeColors.light,
                                    fontSize: h3,
                                  ),
                                ),
                                SizedBox(height: spacingHeightMedium),
                                DefaultTextField2(
                                  controller: nameController,
                                  labelText: "Name",
                                  hintText: "Required",
                                ),
                                DefaultDropdown2(
                                  selectedValue: selectedCountryId!,
                                  items: countryList.map((Country value) {
                                    return DropdownMenuItem<String>(
                                      value: value.id.toString(),
                                      child: Text(
                                        value.name,
                                        style: TextStyle(
                                            fontSize: h3,
                                            color: ThemeColors.dark),
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (String? value) {
                                    setState(() {
                                      selectedCountryId = value;
                                    });
                                  },
                                ),
                                DefaultAddressPhoneField2(
                                  textInputType: TextInputType.phone,
                                  controller: phoneController,
                                  selectedCountryCode: selectedPhoneCode,
                                  labelText: "Phone Number",
                                ),
                                DefaultTextField2(
                                  controller: address1Controller,
                                  labelText: "Address 1",
                                  hintText: "Required",
                                ),
                                DefaultTextField2(
                                  controller: address2Controller,
                                  labelText: "Address 2 (Optional)",
                                  hintText: "Optional",
                                ),
                                DefaultTextField2(
                                  controller: cityController,
                                  labelText: "City",
                                  hintText: "Required",
                                ),
                                DefaultTextField2(
                                  controller: postcodeController,
                                  labelText: "Postal Code",
                                  keyBoardType: TextInputType.number,
                                  hintText: "Required",
                                ),
                                DefaultDropdown2(
                                  selectedValue: selectedStatesId!,
                                  items: countryList
                                      .firstWhereOrNull((element) =>
                                          element.id.toString() ==
                                          selectedCountryId)
                                      ?.states
                                      ?.map((States value) {
                                    return DropdownMenuItem<String>(
                                      value: value.id.toString(),
                                      child: Text(
                                        value.name,
                                        style: TextStyle(
                                            fontSize: h3,
                                            color: ThemeColors.dark),
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (String? value) {
                                    setState(() {
                                      selectedStatesId = value;
                                    });
                                  },
                                ),
                                SizedBox(height: topPaddingWithoutBar),
                                DefaultButton(
                                  text: "Submit",
                                  buttonColor: ThemeColors.primaryDark,
                                  borderRadius: 15,
                                  onPressed: () {
                                    addAddress();
                                  },
                                ),
                              ],
                            ),
                          );
                        }
                      }
                      return Container();
                    },
                  ),
                ),
              ),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }

  void addAddress() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    // check location permission
    Position? currentPosition;
    if (Platform.isIOS) {
      currentPosition = await LocationService.getCurrentPosition();
    } else {
      Position? permissionCheck =
          await LocationService.checkAndroidLocationPermission();
      if (permissionCheck != null)
        currentPosition = await LocationService.getCurrentPosition();
    }

    Map? data = await apiPost.storeAddress(
        address1Controller.text,
        address2Controller.text,
        postcodeController.text,
        cityController.text,
        selectedStatesId,
        selectedCountryId,
        nameController.text,
        phoneController.text,
        currentPosition?.latitude,
        currentPosition?.longitude);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          Navigator.of(context).pop();
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  Future<Map?> getCountryList() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.countryList();

    if (data != null && data['data'] != null) {
      countryList = (data['data']['countries'] as List)
          .map((data) => Country.fromJson(data))
          .toList();

      if (countryList.isNotEmpty) {
        selectedCountryId = countryList.first.id.toString();
        selectedPhoneCode = countryList.first.phoneCode;

        if (countryList.first.states != null)
          selectedStatesId = countryList.first.states!.first.id.toString();
      }
    }
    return data;
  }
}
