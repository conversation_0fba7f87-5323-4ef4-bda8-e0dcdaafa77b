import 'package:flutter/material.dart';
import 'package:amverton/View/OrderMenu/order_history_detail_template2.dart';
import 'package:amverton/View/OrderMenu/order_history_detail_template1.dart';

class OrderHistoryDetailPage extends StatefulWidget {
  final int id;

  const OrderHistoryDetailPage({super.key, required this.id});

  @override
  State<OrderHistoryDetailPage> createState() => _OrderHistoryDetailPageState();
}

class _OrderHistoryDetailPageState extends State<OrderHistoryDetailPage> {
  //todo need get from asset
  int selectUI = 2;

  @override
  Widget build(BuildContext context) {
    switch (selectUI) {
      case 1:
        return OrderHistoryDetailTemplate1(
          id: widget.id,
        );
      case 2:
        return OrderHistoryDetailTemplate2(
          id: widget.id,
        );
      default:
        return Container();
    }
  }
}
