import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Model/address.dart';
import 'package:amverton/Model/bottom_bar.dart';
import 'package:amverton/Model/menu.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Model/product.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Cart/cart_page.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/OrderMenu/Section/menu_template1.dart';
import 'package:amverton/View/OrderMenu/Section/menu_template2.dart';
import 'package:amverton/View/OrderMenu/deliver_address_option.dart';
import 'package:amverton/View/OrderMenu/order_menu_detail_template1.dart';
import 'package:amverton/main.dart';
import 'package:hive/hive.dart';

class OrderMenuPage extends StatefulWidget {
  Outlet? outlet;
  final Address? address;
  final bool updateAddress;

  OrderMenuPage({
    required Key key,
    this.outlet,
    this.address,
    this.updateAddress = false,
  }) : super(key: key);

  @override
  OrderMenuPageState createState() => OrderMenuPageState();

  static void getProductDetail(
      BuildContext context, int productId, String tag) {
    OrderMenuPageState? state =
        context.findAncestorStateOfType<OrderMenuPageState>();
    state?.getProductDetail(productId, tag);
  }
}

late Future future;
int selectedCategoryIndex = 0;
List<MenuCategory> categoryList = [];
List<String> schedule = [];
Outlet? selectedOutlet;
Address? selectedAddress;
int? selectedDeliverMethod; // 0 = Delivery; 1 = Pickup
ValueNotifier<int> cartQuantity = ValueNotifier<int>(0);

class OrderMenuPageState extends State<OrderMenuPage> {
  bool isLogin = false;
  Box box = Hive.box("box");
  Box boxGlobal = Hive.box("boxGlobal");
  Box boxMenu = Hive.box("boxMenu");

  @override
  void initState() {
    super.initState();
    selectedOutlet = widget.outlet;
    selectedAddress = widget.address;
    selectedDeliverMethod = boxMenu.get("selected_deliver_method");
    future = getMenu();
  }

  @override
  void didUpdateWidget(covariant OrderMenuPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    selectedOutlet = widget.outlet;
    selectedAddress = widget.address;
    selectedDeliverMethod = boxMenu.get("selected_deliver_method");
    future = getMenu();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      behavior: HitTestBehavior.translucent,
      child: getOrderMenuTemplate(),
    );
  }

  Widget getOrderMenuTemplate() {
    // if (orderMainTemplate == "1")
    //   return MenuTemplate1(cartBtnFunction: cartFunction);
    // else if (orderMainTemplate == "2")
    //   return MenuTemplate2(cartBtnFunction: cartFunction);
    // else
    //   return MenuTemplate1(cartBtnFunction: cartFunction);

    // todo temp hardcode for obriens. If merchant portal add new template, then can remove this and use the code above.
    return MenuTemplate2(cartBtnFunction: cartFunction);
    //
  }

  void cartFunction() {
    if (isLogin) {
      List<BottomBar> bottomBarList = boxGlobal.get("bottomBar") ?? [];
      if (bottomBarList.isNotEmpty) {
        int cartIndex =
            bottomBarList.indexWhere((element) => element.name == "CART");
        if (cartIndex == -1) {
          Route route =
              MaterialPageRoute(builder: (BuildContext context) => CartPage());
          Navigator.push(context, route).then(onRefresh);
        } else {
          final dynamic navigationBar = barGlobalKey.currentWidget;
          navigationBar.onTap(cartIndex);
        }
      }
    } else {
      Navigator.of(context, rootNavigator: true)
          .pushNamed("/login/${loginAsset.template}");
    }
  }

  Future<Map?> getMenu() async {
    Box boxMenu = await Hive.openBox('boxMenu');
    int? selectedDeliverMethod = await boxMenu.get("selected_deliver_method");
    Outlet? selectedOutlet = boxMenu.get("selected_outlet") ?? null;
    Address? selectedAddress = boxMenu.get("selected_address") ?? null;

    isLogin = box.get("is_login") ?? false;

    ApiGet apiGet = ApiGet();

    Map? data = await apiGet.menu(
        selectedOutlet == null ? null : selectedOutlet.id,
        selectedAddress == null ? null : selectedAddress.id);

    if (data != null && data['data'] != null) {
      selectedCategoryIndex = 0;

      categoryList = (data['data']['categories'] as List)
          .map((data) => MenuCategory.fromJson(data))
          .toList();

      schedule = (data['data']['pickup_hours'] as List)
          .map((data) => data as String)
          .toList();

      schedule.insert(0, "ASAP");

      if (schedule.length <= 1) {
        defaultDialog(context, 400,
                "We're currently closed. Please come back tomorrow.", () {})
            .show();
      }

      if (selectedDeliverMethod == 0 && widget.updateAddress) {
        selectedOutlet = data['data']['outlet'] == null
            ? null
            : Outlet.fromJson(data['data']['outlet']);
        if (selectedOutlet != null) {
          await boxMenu.put("selected_outlet", selectedOutlet);
        }
      }

      if (selectedDeliverMethod == 0 && categoryList.isEmpty) {
        defaultDialog(context, 400,
            "We couldn't find a outlet that matched your delivery address.",
            () async {
          List<BottomBar> bottomBarList = boxGlobal.get("bottomBar") ?? [];
          if (bottomBarList.isNotEmpty) {
            int orderIndex =
                bottomBarList.indexWhere((element) => element.name == "ORDER");
            int homeIndex =
                bottomBarList.indexWhere((element) => element.name == "HOME");
            if (orderIndex != -1) {
              Box boxMenu = await Hive.openBox('boxMenu');
              await boxMenu.delete("selected_deliver_method");
              await boxMenu.delete("selected_address");
              await boxMenu.delete("selected_outlet");

              final dynamic navigationBar = barGlobalKey.currentWidget;
              navigationBar.onTap(homeIndex);

              Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (BuildContext context) => DeliverAddressOption(
                        selectedAddressId: selectedAddress?.id),
                  ));
            }
          }
        }).show();
      }

      cartQuantity.value = data['data']['cart_quantity'] ?? 0;
    }
    return data;
  }

  getProductDetail(int productId, String tag) async {
    EasyLoading.show();
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.productDetail(productId);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        Product product = Product.fromJson(data['data']['product']);
        List<Product> recommended =
            (data['data']['recommended_products'] as List)
                .map((data) => Product.fromJson(data))
                .toList();
        String? inviteMsg = data['data']['invite_message'] ?? null;
        String? inviteUrl = data['data']['invite_url'] ?? null;

        Route route = MaterialPageRoute(
            builder: (BuildContext context) => OrderMenuDetailTemplate1(
                  outlet: selectedOutlet!,
                  product: product,
                  recommended: recommended,
                  inviteMsg: inviteMsg,
                  inviteUrl: inviteUrl,
                  tag: "$tag${product.id}",
                ));
        Navigator.push(context, route).then(onRefresh);
      }
    }
  }

  FutureOr onRefresh(dynamic value) {
    setState(() {
      getMenu();
    });
  }
}

Future<Map?> getMenuSearch(String? searchValue) async {
  ApiGet apiGet = ApiGet();
  Map? data = await apiGet.menuSearch(selectedOutlet!.id, searchValue);
  return data;
}
