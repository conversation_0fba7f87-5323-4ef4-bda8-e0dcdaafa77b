import 'dart:math';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/bottom_bar.dart';
import 'package:amverton/Model/cart.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/product.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Cart/cart_page.dart';
import 'package:amverton/View/Custom%20Widgets/cart_button1.dart';
import 'package:amverton/View/Custom%20Widgets/cart_button2.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
import 'package:amverton/View/Home/product_list.dart';
import 'package:amverton/View/OrderMenu/order_menu_page.dart';
import 'package:amverton/main.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:async';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;
import 'package:vector_math/vector_math_64.dart' as math;

class OrderMenuDetailTemplate1 extends StatefulWidget {
  final Outlet outlet;
  final Product product;
  final List<Product> recommended;
  final String? inviteMsg;
  final String? inviteUrl;
  final Object tag;
  final bool editMode;
  final int? cartItemId;
  final int? quantity;
  final String? remark;

  const OrderMenuDetailTemplate1({
    Key? key,
    required this.outlet,
    required this.product,
    required this.recommended,
    this.inviteMsg,
    this.inviteUrl,
    required this.tag,
    this.editMode = false,
    this.cartItemId,
    this.quantity,
    this.remark,
  }) : super(key: key);

  @override
  _OrderMenuDetailTemplate1State createState() =>
      _OrderMenuDetailTemplate1State();
}

class _OrderMenuDetailTemplate1State extends State<OrderMenuDetailTemplate1>
    with TickerProviderStateMixin {
  late Future future;
  late Box box;
  late Box boxGlobal;
  bool isLogin = false;
  int quantity = 1;
  TextEditingController remarkController = TextEditingController();

  // add to cart animation //
  late AnimationController animationController;
  late Animation<Offset> moveAnimation;
  late Animation<double> sizeAnimation;
  double scale = 0.0;

  // bundle //
  List<List<bool>> bundleSelected = [];
  List<GlobalKey> bundleFormKey = [];
  List<GlobalKey<ShakeErrorState>> bundleShakeKey = [];

  // modifier //
  List<List<bool>> modifierSelected = [];
  List<GlobalKey> modifierFormKey = [];
  List<GlobalKey<ShakeErrorState>> modifierShakeKey = [];

  @override
  void initState() {
    super.initState();
    future = openBox();

    animationController =
        AnimationController(vsync: this, duration: Duration(seconds: 1));
    moveAnimation = Tween<Offset>(begin: Offset(0, 0), end: Offset(0, 4))
        .animate(animationController);
    sizeAnimation = Tween(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(
        parent: animationController,
        curve: Curves.fastLinearToSlowEaseIn,
      ),
    )..addListener(() {
        setState(() => scale = sizeAnimation.value);
      });

    if (widget.editMode) {
      quantity = widget.quantity ?? 1;
      remarkController.text = widget.remark ?? "";
    }

    // bundle //
    for (BundleCategory eachCategory in widget.product.bundleCategory!) {
      bundleSelected
          .add(List.generate(eachCategory.bundleItems.length, (index) {
        if (widget.editMode) {
          if (eachCategory.bundleItems[index].isSelected == 1) {
            return true;
          } else {
            return false;
          }
        } else {
          return false;
        }
      }));
      bundleFormKey.add(GlobalKey());
      bundleShakeKey.add(GlobalKey());
    }

    // modifier //
    for (ModifierCategory eachCategory in widget.product.modifierCategory!) {
      modifierSelected
          .add(List.generate(eachCategory.modifierItems.length, (index) {
        if (widget.editMode) {
          if (eachCategory.modifierItems[index].isSelected == 1) {
            return true;
          } else {
            return false;
          }
        } else {
          return false;
        }
      }));
      modifierFormKey.add(GlobalKey());
      modifierShakeKey.add(GlobalKey());
    }
  }

  openBox() async {
    box = await Hive.openBox("box");
    boxGlobal = await Hive.openBox("boxGlobal");
    isLogin = box.get("is_login") ?? false;

    Box boxMenu = await Hive.openBox('boxMenu');
    boxMenu.put("selected_outlet", widget.outlet);
    return await Hive.openBox<Asset>("ORDER+MAIN+1");
  }

  @override
  void dispose() {
    for (int a = 0; a < bundleFormKey.length; a++) {
      bundleFormKey[a].currentState?.dispose();
      bundleShakeKey[a].currentState?.dispose();
    }
    for (int a = 0; a < bundleFormKey.length; a++) {
      modifierFormKey[a].currentState?.dispose();
      modifierShakeKey[a].currentState?.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: future,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          final Box list = snapshot.data;
          list.watch();

          String? backgroundType = list.values
                  .firstWhereOrNull((element) =>
                      element.name == "PRODUCT_DETAIL_BACKGROUND_TYPE")
                  ?.data ??
              "";
          Asset? background = list.values.firstWhereOrNull((element) =>
              element.name == "PRODUCT_DETAIL_BACKGROUND_$backgroundType");

          return GestureDetector(
            onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
            behavior: HitTestBehavior.translucent,
            child: Scaffold(
              extendBodyBehindAppBar: true,
              appBar: AppBar(
                backgroundColor: Colors.transparent,
                leading: GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    margin: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                        shape: BoxShape.circle, color: ThemeColors.gray),
                    child: Icon(
                      Icons.arrow_back,
                      color: ThemeColors.light,
                    ),
                  ),
                ),
              ),
              body: Container(
                height: phoneHeight,
                decoration: background == null
                    ? null
                    : BoxDecoration(
                        image: backgroundType == "IMAGE"
                            ? DecorationImage(
                                image: CachedNetworkImageProvider(
                                    background.data ?? ""),
                                fit: BoxFit.cover,
                              )
                            : null,
                        color: backgroundType == "COLOR"
                            ? Color(
                                int.parse("0xff${background.data ?? "FFFFFF"}"))
                            : null,
                      ),
                child: Stack(
                  children: [
                    CachedNetworkImage(
                      imageUrl: widget.product.image ?? "",
                      height: phoneHeight / 3,
                      width: phoneWidth,
                      fit: BoxFit.cover,
                      placeholder: (context, url) {
                        return Center(
                          child: CircularProgressIndicator(
                            color: ThemeColors.primaryDark,
                          ),
                        );
                      },
                      errorWidget: (context, url, error) {
                        return Icon(
                          Icons.error_outline_outlined,
                          color: ThemeColors.primaryDark,
                        );
                      },
                    ),
                    SingleChildScrollView(
                      physics: ClampingScrollPhysics(),
                      padding: EdgeInsets.only(
                        top: phoneHeight / 3.75,
                        bottom: buttonHeight * 2,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            margin: EdgeInsets.symmetric(
                                horizontal: defaultPadding),
                            padding: EdgeInsets.all(defaultPadding),
                            decoration: BoxDecoration(
                              color: ThemeColors.light,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                detail(),
                                // todo temp hardcode for obriens (originally description is at below)
                                Text(
                                  widget.product.description!,
                                  style: TextStyle(
                                    color: ThemeColors.dark,
                                    fontSize: h4,
                                  ),
                                ),
                                SizedBox(height: spacingHeightLarge),
                                //
                                if (widget.product.bundleCategory != null)
                                  bundle(),
                                if (widget.product.modifierCategory!.isNotEmpty)
                                  modifier(),
                                DefaultTextField1(
                                  controller: remarkController,
                                  keyBoardType: TextInputType.text,
                                  hintText: "Remark",
                                  maxLines: 2,
                                ),
                                SizedBox(height: spacingHeightLarge),
                                DefaultButton(
                                  onPressed: () {
                                    if (isLogin) {
                                      bool bundleError = false;
                                      bool modifierError = false;
                                      if (widget.product.type == "BUNDLE")
                                        bundleError = bundleChecking();
                                      if (widget
                                          .product.modifierCategory!.isNotEmpty)
                                        modifierError = modifierChecking();

                                      if (!bundleError && !modifierError) {
                                        if (widget.editMode) {
                                          updateCart();
                                        } else {
                                          addToCart();
                                        }
                                      }
                                    } else {
                                      Navigator.of(context, rootNavigator: true)
                                          .pushNamed(
                                              "/login/${loginAsset.template}");
                                    }
                                  },
                                  text: widget.editMode
                                      ? "Update"
                                      : list.values
                                              .firstWhereOrNull((element) =>
                                                  element.name ==
                                                  "ADD_TO_CART_BUTTON_TEXT")
                                              ?.data ??
                                          "",
                                  buttonColor:
                                      important_variables.projectName ==
                                              "obriens"
                                          ? ThemeColors.secondaryDark
                                          : ThemeColors.primaryDark,
                                ),
                                // todo temp hardcode for obriens (originally description is at here)
                                // SizedBox(height: spacingHeightLarge * 2),
                                // Text(
                                //   widget.product.description!,
                                //   style: TextStyle(
                                //     color: Color(
                                //         int.parse(boxGlobal.get("colorD1"))),
                                //     fontSize: h4,
                                //   ),
                                // ),
                                //
                              ],
                            ),
                          ),
                          if (widget.recommended.isNotEmpty)
                            recommendedProductView(),
                        ],
                      ),
                    ),

                    // animation
                    SlideTransition(
                      position: moveAnimation,
                      child: Transform.scale(
                        scale: scale,
                        child: CachedNetworkImage(
                          imageUrl: widget.product.image ?? "",
                          height: phoneHeight / 3,
                          width: phoneWidth,
                          fit: BoxFit.cover,
                          errorWidget: (context, url, error) {
                            return Icon(
                              Icons.image,
                              size: 200,
                              color: ThemeColors.primaryDark,
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              floatingActionButtonLocation: () {
                // todo CartButton1 = FloatingActionButtonLocation.centerFloat; CartButton2 = FloatingActionButtonLocation.endFloat
                // if (orderMainTemplate == "1")
                //   return FloatingActionButtonLocation.centerFloat;
                // else if (orderMainTemplate == "2")
                return FloatingActionButtonLocation.endFloat;
                // else
                // return FloatingActionButtonLocation.centerFloat;
              }(),
              floatingActionButton: isLogin
                  ? widget.editMode
                      ? null
                      : cartButton()
                  : null,
            ),
          );
        }
        return Container();
      },
    );
  }

  Widget detail() {
    return Column(
      children: [
        // Name //
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Text(
                widget.product.name,
                style: TextStyle(
                  color: ThemeColors.dark,
                  fontSize: h2,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            SizedBox(width: defaultPadding),
            if (widget.inviteMsg != null || widget.inviteUrl != null)
              GestureDetector(
                onTap: () {
                  shareMore();
                },
                child: ImageIcon(
                  AssetImage(
                      "assets/icons/${boxGlobal.get("iconSet")}/share.png"),
                  color: ThemeColors.primaryDark,
                ),
              ),
          ],
        ),
        SizedBox(height: spacingHeightMedium),
        // Price //
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              important_variables.currency + widget.product.price!,
              style: TextStyle(
                color: ThemeColors.primaryDark,
                fontSize: h3,
                fontWeight: FontWeight.bold,
              ),
            ),
            Row(
              children: [
                GestureDetector(
                  onTap: () {
                    if (quantity > 1) {
                      setState(() {
                        quantity--;
                      });
                    }
                  },
                  child: ImageIcon(
                    AssetImage(
                        "assets/icons/${boxGlobal.get("iconSet")}/General_Minus.png"),
                    color: quantity > 1
                        ? ThemeColors.primaryDark
                        : ThemeColors.disabled,
                  ),
                ),
                Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: spacingHeightMedium),
                  child: Text(
                    quantity.toString(),
                    style: TextStyle(
                      color: ThemeColors.dark,
                      fontSize: h3,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      quantity++;
                    });
                  },
                  child: ImageIcon(
                    AssetImage(
                        "assets/icons/${boxGlobal.get("iconSet")}/General_Plus.png"),
                    color: ThemeColors.primaryDark,
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget bundle() {
    return Column(
      children:
          List.generate(widget.product.bundleCategory!.length, (categoryIndex) {
        return ShakeError(
          key: bundleShakeKey[categoryIndex],
          child: Container(
            key: bundleFormKey[categoryIndex],
            margin: EdgeInsets.only(top: defaultInnerPadding),
            padding: EdgeInsets.only(top: defaultInnerPadding),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(width: 3.5, color: ThemeColors.disabled),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        widget.product.bundleCategory![categoryIndex].name,
                        style: TextStyle(
                          color: ThemeColors.dark,
                          fontSize: h3,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Text(
                      () {
                        if (widget.product.bundleCategory![categoryIndex]
                                .minimumSelection ==
                            0) {
                          if (widget.product.bundleCategory![categoryIndex]
                                  .maximumSelection ==
                              0) {
                            return "Optional";
                          } else {
                            return "Optional, max ${widget.product.bundleCategory![categoryIndex].maximumSelection}";
                          }
                        } else {
                          if (widget.product.bundleCategory![categoryIndex]
                                  .minimumSelection ==
                              widget.product.bundleCategory![categoryIndex]
                                  .maximumSelection) {
                            return "Choose ${widget.product.bundleCategory![categoryIndex].minimumSelection}";
                          } else {
                            if (widget.product.bundleCategory![categoryIndex]
                                    .maximumSelection ==
                                0) {
                              return "Choose at least ${widget.product.bundleCategory![categoryIndex].minimumSelection}";
                            } else {
                              return "Choose ${widget.product.bundleCategory![categoryIndex].minimumSelection} to ${widget.product.bundleCategory![categoryIndex].maximumSelection}";
                            }
                          }
                        }
                      }(),
                      style: TextStyle(
                        color: ThemeColors.dark,
                        fontSize: h4,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Column(
                  children: List.generate(
                      widget.product.bundleCategory![categoryIndex].bundleItems
                          .length, (itemIndex) {
                    return Row(
                      children: [
                        Checkbox(
                            activeColor: ThemeColors.primaryDark,
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                            shape: CircleBorder(),
                            value: bundleSelected[categoryIndex][itemIndex],
                            onChanged: (value) {
                              // check selected
                              int count = 0;
                              for (bool each in bundleSelected[categoryIndex])
                                if (each) count++;

                              if (value!) {
                                // check bundle condition
                                if (count <
                                        widget
                                            .product
                                            .bundleCategory![categoryIndex]
                                            .maximumSelection ||
                                    widget
                                            .product
                                            .bundleCategory![categoryIndex]
                                            .maximumSelection ==
                                        0) {
                                  setState(() {
                                    bundleSelected[categoryIndex][itemIndex] =
                                        value;
                                  });
                                } else if (widget
                                        .product
                                        .bundleCategory![categoryIndex]
                                        .maximumSelection ==
                                    1) {
                                  setState(() {
                                    for (int a = 0;
                                        a <
                                            bundleSelected[categoryIndex]
                                                .length;
                                        a++) {
                                      bundleSelected[categoryIndex][a] = false;
                                    }
                                    bundleSelected[categoryIndex][itemIndex] =
                                        value;
                                  });
                                }
                              } else {
                                setState(() {
                                  bundleSelected[categoryIndex][itemIndex] =
                                      value;
                                });
                              }
                            }),
                        Expanded(
                          child: Text(
                            widget.product.bundleCategory![categoryIndex]
                                .bundleItems[itemIndex].product.name,
                            style: TextStyle(
                              color: bundleSelected[categoryIndex][itemIndex]
                                  ? ThemeColors.dark
                                  : ThemeColors.gray,
                              fontSize: h4,
                            ),
                          ),
                        ),
                        SizedBox(width: spacingWidth),
                        Text(
                          widget.product.bundleCategory![categoryIndex]
                              .bundleItems[itemIndex].additionalPrice,
                          style: TextStyle(
                            color: bundleSelected[categoryIndex][itemIndex]
                                ? ThemeColors.dark
                                : ThemeColors.gray,
                            fontSize: h4,
                          ),
                        ),
                      ],
                    );
                  }),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget modifier() {
    return Column(
      children: List.generate(widget.product.modifierCategory!.length,
          (categoryIndex) {
        return ShakeError(
          key: modifierShakeKey[categoryIndex],
          child: Container(
            key: modifierFormKey[categoryIndex],
            margin: EdgeInsets.only(top: defaultInnerPadding),
            padding: EdgeInsets.only(top: defaultInnerPadding),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(width: 3.5, color: ThemeColors.disabled),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        widget.product.modifierCategory![categoryIndex].name,
                        style: TextStyle(
                          color: ThemeColors.dark,
                          fontSize: h3,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Text(
                      () {
                        if (widget.product.modifierCategory![categoryIndex]
                                .minimumSelection ==
                            0) {
                          if (widget.product.modifierCategory![categoryIndex]
                                  .maximumSelection ==
                              0) {
                            return "Optional";
                          } else {
                            return "Optional, max ${widget.product.modifierCategory![categoryIndex].maximumSelection}";
                          }
                        } else {
                          if (widget.product.modifierCategory![categoryIndex]
                                  .minimumSelection ==
                              widget.product.modifierCategory![categoryIndex]
                                  .maximumSelection) {
                            return "Choose ${widget.product.modifierCategory![categoryIndex].minimumSelection}";
                          } else {
                            if (widget.product.modifierCategory![categoryIndex]
                                    .maximumSelection ==
                                0) {
                              return "Choose at least ${widget.product.modifierCategory![categoryIndex].minimumSelection}";
                            } else {
                              return "Choose ${widget.product.modifierCategory![categoryIndex].minimumSelection} to ${widget.product.modifierCategory![categoryIndex].maximumSelection}";
                            }
                          }
                        }
                      }(),
                      style: TextStyle(
                        color: ThemeColors.dark,
                        fontSize: h4,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Column(
                  children: List.generate(
                      widget.product.modifierCategory![categoryIndex]
                          .modifierItems.length, (itemIndex) {
                    return Row(
                      children: [
                        Checkbox(
                            activeColor: ThemeColors.primaryDark,
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                            shape: CircleBorder(),
                            value: modifierSelected[categoryIndex][itemIndex],
                            onChanged: (value) {
                              // check selected
                              int count = 0;
                              for (bool each in modifierSelected[categoryIndex])
                                if (each) count++;

                              if (value!) {
                                // check modifier condition
                                if (count <
                                        widget
                                            .product
                                            .modifierCategory![categoryIndex]
                                            .maximumSelection ||
                                    widget
                                            .product
                                            .modifierCategory![categoryIndex]
                                            .maximumSelection ==
                                        0) {
                                  setState(() {
                                    modifierSelected[categoryIndex][itemIndex] =
                                        value;
                                  });
                                } else if (widget
                                        .product
                                        .modifierCategory![categoryIndex]
                                        .maximumSelection ==
                                    1) {
                                  setState(() {
                                    for (int a = 0;
                                        a <
                                            modifierSelected[categoryIndex]
                                                .length;
                                        a++) {
                                      modifierSelected[categoryIndex][a] =
                                          false;
                                    }
                                    modifierSelected[categoryIndex][itemIndex] =
                                        value;
                                  });
                                }
                              } else {
                                setState(() {
                                  modifierSelected[categoryIndex][itemIndex] =
                                      value;
                                });
                              }
                            }),
                        Expanded(
                          child: Text(
                            widget.product.modifierCategory![categoryIndex]
                                .modifierItems[itemIndex].name,
                            style: TextStyle(
                              color: modifierSelected[categoryIndex][itemIndex]
                                  ? ThemeColors.dark
                                  : ThemeColors.gray,
                              fontSize: h4,
                            ),
                          ),
                        ),
                        SizedBox(width: spacingWidth),
                        Text(
                          widget.product.modifierCategory![categoryIndex]
                              .modifierItems[itemIndex].additionalPrice,
                          style: TextStyle(
                            color: modifierSelected[categoryIndex][itemIndex]
                                ? ThemeColors.dark
                                : ThemeColors.gray,
                            fontSize: h4,
                          ),
                        ),
                      ],
                    );
                  }),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget recommendedProductView() {
    return Container(
      decoration: BoxDecoration(
        color: ThemeColors.light,
      ),
      padding: EdgeInsets.symmetric(vertical: defaultPadding),
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(
              left: defaultPadding,
              right: defaultPadding,
              bottom: defaultInnerPadding,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Recommended",
                  style: TextStyle(
                    color: ThemeColors.dark,
                    fontSize: h2,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (BuildContext context) => ProductList(
                            outlet: widget.outlet,
                            appBarTitle: "Recommended",
                            products: widget.recommended,
                          ),
                        ));
                  },
                  child: Text(
                    "View More",
                    style: TextStyle(
                      color: ThemeColors.dark,
                      fontSize: h3,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: phoneWidth,
            child: SingleChildScrollView(
              padding: EdgeInsets.only(left: defaultPadding),
              scrollDirection: Axis.horizontal,
              child: Row(
                children: List.generate(
                    widget.recommended.length > 5
                        ? 4
                        : widget.recommended.length, (index) {
                  return GestureDetector(
                    onTap: () {
                      getProductDetail(widget.recommended[index].id);
                    },
                    child: Container(
                      margin: EdgeInsets.only(right: spacingWidth),
                      width: phoneWidth / 3,
                      height: phoneHeight / 3.8,
                      decoration: BoxDecoration(
                        color: ThemeColors.light,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: ThemeColors.disabled),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Hero(
                            tag:
                                "${widget.tag}/recommended/${widget.recommended[index].id}",
                            child: ClipRRect(
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(20),
                                topRight: Radius.circular(20),
                              ),
                              child: CachedNetworkImage(
                                imageUrl: widget.recommended[index].image ?? "",
                                height: phoneWidth / 3,
                                width: phoneWidth / 3,
                                fit: BoxFit.cover,
                                placeholder: (context, url) {
                                  return Center(
                                    child: CircularProgressIndicator(
                                      color: ThemeColors.primaryDark,
                                    ),
                                  );
                                },
                                errorWidget: (context, url, error) {
                                  return Icon(
                                    Icons.error_outline_outlined,
                                    color: ThemeColors.primaryDark,
                                  );
                                },
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.recommended[index].name,
                                  style: TextStyle(
                                      color: ThemeColors.dark, fontSize: h4),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                SizedBox(height: spacingHeightSmall),
                                Text(
                                  important_variables.currency +
                                      widget.recommended[index].price!,
                                  style: TextStyle(
                                      color: ThemeColors.primaryDark,
                                      fontSize: h3),
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  );
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget cartButton() {
    // todo obriens using CartButton2, sbk using CartButton1 (CartButton temporarily dont hv template)

    // return CartButton1(
    return CartButton2(
      onPressed: () {
        if (isLogin) {
          List<BottomBar> bottomBarList = boxGlobal.get("bottomBar") ?? [];
          if (bottomBarList.isNotEmpty) {
            int cartIndex =
                bottomBarList.indexWhere((element) => element.name == "CART");
            if (cartIndex == -1) {
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (BuildContext context) => const CartPage()));
            } else {
              final dynamic navigationBar = barGlobalKey.currentWidget;
              navigationBar.onTap(cartIndex);
            }
          }
        } else {
          Navigator.of(context, rootNavigator: true)
              .pushNamed("/login/${loginAsset.template}");
        }
      },
    );
  }

  bool bundleChecking() {
    bool error = false;
    int errorIndex = 0;
    for (int a = 0; a < widget.product.bundleCategory!.length; a++) {
      int selectedCount = 0;
      for (bool each in bundleSelected[a]) if (each) selectedCount++;

      if (widget.product.bundleCategory![a].minimumSelection == 0) {
        if (widget.product.bundleCategory![a].maximumSelection != 0) {
          if (selectedCount >
              widget.product.bundleCategory![a].maximumSelection) {
            error = true;
            errorIndex = a;
            break;
          }
        }
      } else {
        if (widget.product.bundleCategory![a].minimumSelection ==
            widget.product.bundleCategory![a].maximumSelection) {
          if (selectedCount !=
              widget.product.bundleCategory![a].maximumSelection) {
            error = true;
            errorIndex = a;
            break;
          }
        } else {
          if (widget.product.bundleCategory![a].maximumSelection == 0) {
            if (selectedCount <
                widget.product.bundleCategory![a].minimumSelection) {
              error = true;
              errorIndex = a;
              break;
            }
          } else {
            if (selectedCount <
                    widget.product.bundleCategory![a].minimumSelection &&
                selectedCount <
                    widget.product.bundleCategory![a].maximumSelection) {
              error = true;
              errorIndex = a;
              break;
            }
          }
        }
      }
    }
    if (error) {
      Scrollable.ensureVisible(
          bundleFormKey[errorIndex].currentContext!); // scroll to the item
      bundleShakeKey[errorIndex]
          .currentState
          ?.shake(); // shake the item's widget
    }
    return error;
  }

  bool modifierChecking() {
    bool error = false;
    int errorIndex = 0;
    for (int a = 0; a < widget.product.modifierCategory!.length; a++) {
      int selectedCount = 0;
      for (bool each in modifierSelected[a]) if (each) selectedCount++;

      if (widget.product.modifierCategory![a].minimumSelection == 0) {
        if (widget.product.modifierCategory![a].maximumSelection != 0) {
          if (selectedCount >
              widget.product.modifierCategory![a].maximumSelection) {
            error = true;
            errorIndex = a;
            break;
          }
        }
      } else {
        if (widget.product.modifierCategory![a].minimumSelection ==
            widget.product.modifierCategory![a].maximumSelection) {
          if (selectedCount !=
              widget.product.modifierCategory![a].maximumSelection) {
            error = true;
            errorIndex = a;
            break;
          }
        } else {
          if (widget.product.modifierCategory![a].maximumSelection == 0) {
            if (selectedCount <
                widget.product.modifierCategory![a].minimumSelection) {
              error = true;
              errorIndex = a;
              break;
            }
          } else {
            if (selectedCount <
                    widget.product.modifierCategory![a].minimumSelection &&
                selectedCount <
                    widget.product.modifierCategory![a].maximumSelection) {
              error = true;
              errorIndex = a;
              break;
            }
          }
        }
      }
    }
    if (error) {
      Scrollable.ensureVisible(
          modifierFormKey[errorIndex].currentContext!); // scroll to the item
      modifierShakeKey[errorIndex]
          .currentState
          ?.shake(); // shake the item's widget
    }
    return error;
  }

  void addToCart() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    // bundle //
    List<int> bundleItemId = [];
    if (widget.product.type == "BUNDLE") {
      for (int a = 0; a < bundleSelected.length; a++) {
        for (int b = 0; b < bundleSelected[a].length; b++) {
          if (bundleSelected[a][b]) {
            bundleItemId
                .add(widget.product.bundleCategory![a].bundleItems[b].id);
          }
        }
      }
    }

    // modifier //
    List<int> modifierItemId = [];
    if (widget.product.modifierCategory!.isNotEmpty) {
      for (int a = 0; a < modifierSelected.length; a++) {
        for (int b = 0; b < modifierSelected[a].length; b++) {
          if (modifierSelected[a][b]) {
            modifierItemId
                .add(widget.product.modifierCategory![a].modifierItems[b].id);
          }
        }
      }
    }

    Map? data = await apiPost.storeCart(
      widget.outlet.id,
      widget.product.id,
      quantity,
      remarkController.text,
      bundleItemId,
      modifierItemId,
    );
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        animationController.reset();
        animationController.forward();

        List<Cart> cartList = (data['data']['items'] as List)
            .map((data) => Cart.fromJson(data))
            .toList();

        int quantity = 0;
        for (Cart each in cartList) quantity += each.quantity;
        cartQuantity.value = quantity;
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  void updateCart() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    // bundle //
    List<int> bundleItemId = [];
    if (widget.product.type == "BUNDLE") {
      for (int a = 0; a < bundleSelected.length; a++) {
        for (int b = 0; b < bundleSelected[a].length; b++) {
          if (bundleSelected[a][b]) {
            bundleItemId
                .add(widget.product.bundleCategory![a].bundleItems[b].id);
          }
        }
      }
    }

    // modifier //
    List<int> modifierItemId = [];
    if (widget.product.modifierCategory!.isNotEmpty) {
      for (int a = 0; a < modifierSelected.length; a++) {
        for (int b = 0; b < modifierSelected[a].length; b++) {
          if (modifierSelected[a][b]) {
            modifierItemId
                .add(widget.product.modifierCategory![a].modifierItems[b].id);
          }
        }
      }
    }

    Map? data = await apiPost.updateCart(widget.outlet.id, widget.cartItemId,
        quantity, remarkController.text, null, bundleItemId, modifierItemId);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        Navigator.pop(context, data);
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  getProductDetail(int productId) async {
    EasyLoading.show();
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.productDetail(productId);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        Product product = Product.fromJson(data['data']['product']);
        List<Product> recommended =
            (data['data']['recommended_products'] as List)
                .map((data) => Product.fromJson(data))
                .toList();
        String? inviteMsg = data['data']['invite_message'] ?? null;
        String? inviteUrl = data['data']['invite_url'] ?? null;

        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (BuildContext context) => OrderMenuDetailTemplate1(
                      outlet: widget.outlet,
                      product: product,
                      recommended: recommended,
                      inviteMsg: inviteMsg,
                      inviteUrl: inviteUrl,
                      tag: "${widget.tag}/recommended/${product.id}",
                    )));
      }
    }
  }

  void shareMore() async {
    final box = context.findRenderObject() as RenderBox?;
    await Share.share(
        "${widget.inviteMsg != null ? "${widget.inviteMsg} " : ""}${widget.inviteUrl ?? ""}",
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size);
  }
}

// with shake error effect
class ShakeError extends StatefulWidget {
  const ShakeError({Key? key, required this.child}) : super(key: key);

  final Widget child;

  @override
  ShakeErrorState createState() => ShakeErrorState();
}

class ShakeErrorState extends State<ShakeError>
    with SingleTickerProviderStateMixin {
  late AnimationController animationController;
  late Animation<double> animation;

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 800), // how long the shake happens
    )..addListener(() => setState(() {}));

    animation = Tween<double>(
      begin: 0.0,
      end: 120.0,
    ).animate(animationController);
  }

  math.Vector3 _shake() {
    double progress = animationController.value;
    double offset =
        sin(progress * pi * 10.0); // change 10 to make it vibrate faster
    return math.Vector3(
        offset * 10, 0.0, 0.0); // change 25 to make it vibrate wider
  }

  shake() {
    animationController.forward(from: 0);
  }

  @override
  Widget build(BuildContext context) {
    return Transform(
      transform: Matrix4.translation(_shake()),
      child: widget.child,
    );
  }
}
