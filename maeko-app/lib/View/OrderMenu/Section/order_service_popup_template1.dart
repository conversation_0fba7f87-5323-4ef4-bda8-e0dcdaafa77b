import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/OrderMenu/deliver_address_option.dart';
import 'package:amverton/View/OrderMenu/order_menu_outlet.dart';
import 'package:hive/hive.dart';

class OrderServicePopupTemplate1 extends StatefulWidget {
  const OrderServicePopupTemplate1({Key? key}) : super(key: key);

  @override
  _OrderServicePopupTemplate1State createState() =>
      _OrderServicePopupTemplate1State();
}

class _OrderServicePopupTemplate1State
    extends State<OrderServicePopupTemplate1> {
  Box boxGlobal = Hive.box('boxGlobal');

  @override
  Widget build(BuildContext context) {
    return Theme(
      data:
          Theme.of(context).copyWith(dialogBackgroundColor: Colors.transparent),
      child: Stack(
        children: [
          Positioned(
            left: defaultPadding,
            right: defaultPadding,
            top: phoneHeight / 5.2,
            bottom: phoneHeight / 5.2,
            child: Container(
              padding: EdgeInsets.all(verticalPaddingSmall),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: ThemeColors.light,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Text(
                    "Choose a service",
                    style: TextStyle(
                      fontSize: h3,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  GestureDetector(
                    onTap: () async {
                      Box box = Hive.box("box");
                      bool isLogin = box.get("is_login") ?? false;
                      Navigator.pop(context);
                      if (isLogin) {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (BuildContext context) =>
                                  DeliverAddressOption(),
                            ));
                      } else {
                        Navigator.of(context, rootNavigator: true)
                            .pushNamed("/login/${loginAsset.template}");
                      }
                    },
                    child: Image.asset(
                        "assets/images/temp-order-popup-delivery.png"),
                  ),
                  GestureDetector(
                    onTap: () async {
                      Navigator.pop(context);
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (BuildContext context) =>
                                OrderMenuOutlet(selectedDeliverMethod: 1),
                          ));
                    },
                    child: Image.asset(
                        "assets/images/temp-order-popup-pickup.png"),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
