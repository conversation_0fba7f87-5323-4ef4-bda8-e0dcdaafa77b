import 'dart:async';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/app_settings.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/product.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/cart_button2.dart';
import 'package:amverton/View/Custom%20Widgets/default_dropdown1.dart';
import 'package:amverton/View/Custom%20Widgets/default_search_bar.dart';
import 'package:amverton/View/OrderMenu/deliver_address_option.dart';
import 'package:amverton/View/OrderMenu/order_menu_outlet.dart' as OrderOutlet;
import 'package:amverton/View/OrderMenu/order_menu_page.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;
import 'package:toggle_switch/toggle_switch.dart';

class MenuTemplate2 extends StatefulWidget {
  final Function() cartBtnFunction;

  const MenuTemplate2({
    Key? key,
    required this.cartBtnFunction,
  }) : super(key: key);

  @override
  _MenuTemplate2State createState() => _MenuTemplate2State();
}

class _MenuTemplate2State extends State<MenuTemplate2> {
  late Future boxFuture;
  late Box box;
  late Box boxGlobal;
  late Box boxMenu;
  AppSettings? appSettings;

  List<String> deliverMethod = ["Delivery", "Pickup"];
  String selectedSchedule = "";
  bool onSearch = false;
  List<Product> searchList = [];
  PageController pageController = PageController();
  TextEditingController searchController = TextEditingController();
  final StreamController streamController = StreamController.broadcast();

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();

    pageController = PageController();
    searchController.addListener(() {
      streamController.add(searchController.text);
    });
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    boxMenu = await Hive.openBox('boxMenu');
    appSettings = boxGlobal.get("appSettings") ?? null;

    // todo temp for obriens
    return await Hive.openBox<Asset>('ORDER+MAIN+1');
    // return await Hive.openBox<Asset>('ORDER+MAIN+2');
  }

  getSelectedSchedule() async {
    if (schedule.isNotEmpty) {
      selectedSchedule = boxMenu.get("selected_schedule") ?? "";

      int check = schedule.indexWhere((element) => element == selectedSchedule);
      if (check == -1) selectedSchedule = schedule.first;
      await boxMenu.put("selected_schedule", selectedSchedule);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FutureBuilder(
          future: boxFuture,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            if (snapshot.connectionState == ConnectionState.done) {
              final Box list = snapshot.data;
              list.watch();

              String? backgroundType = list.values
                      .firstWhereOrNull(
                          (element) => element.name == "MENU_BACKGROUND_TYPE")
                      ?.data ??
                  "";
              Asset? background = list.values.firstWhereOrNull((element) =>
                  element.name == "MENU_BACKGROUND_$backgroundType");

              return Container(
                decoration: background == null
                    ? null
                    : BoxDecoration(
                        image: backgroundType == "IMAGE"
                            ? DecorationImage(
                                image: CachedNetworkImageProvider(
                                    background.data ?? ""),
                                fit: BoxFit.cover,
                              )
                            : null,
                        color: backgroundType == "COLOR"
                            ? Color(
                                int.parse("0xff${background.data ?? "FFFFFF"}"))
                            : null,
                      ),
                width: phoneWidth,
                child: Column(
                  children: [
                    FutureBuilder(
                        future: future,
                        builder:
                            (BuildContext context, AsyncSnapshot snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.done) {
                            getSelectedSchedule();
                            return header(list);
                          }
                          return Container();
                        }),
                    Visibility(
                      visible: !onSearch,
                      child: FutureBuilder(
                          future: future,
                          builder:
                              (BuildContext context, AsyncSnapshot snapshot) {
                            if (snapshot.connectionState ==
                                ConnectionState.done) {
                              return Expanded(
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: ThemeColors.light,
                                  ),
                                  child: Row(
                                    children: [
                                      category(),
                                      item(),
                                    ],
                                  ),
                                ),
                              );
                            }
                            return Container();
                          }),
                    ),
                    Visibility(
                      visible: onSearch,
                      child: StreamBuilder(
                        stream: streamController.stream.asBroadcastStream(),
                        builder:
                            (BuildContext context, AsyncSnapshot snapshot) {
                          if (snapshot.hasData) {
                            return Expanded(
                              child: Container(
                                color: ThemeColors.light,
                                child: SingleChildScrollView(
                                  padding: EdgeInsets.only(top: defaultPadding),
                                  child: Column(
                                    children: [
                                      Text(
                                        "Search Result",
                                        style: TextStyle(
                                            color: orderTheme == "L"
                                                ? ThemeColors.dark
                                                : ThemeColors.light,
                                            fontSize: h2,
                                            fontWeight: FontWeight.bold),
                                      ),
                                      SizedBox(height: spacingHeightMedium),
                                      FutureBuilder(
                                        future: getMenuSearch(snapshot.data),
                                        builder: (BuildContext context,
                                            AsyncSnapshot snapshot) {
                                          if (snapshot.data != null &&
                                              snapshot.data['data'] != null) {
                                            searchList = (snapshot.data['data']
                                                    ['products'] as List)
                                                .map((data) =>
                                                    Product.fromJson(data))
                                                .toList();

                                            return searchItem();
                                          }
                                          return SizedBox(
                                            height: phoneHeight / 3,
                                            child: Center(
                                              child: CircularProgressIndicator(
                                                  color:
                                                      ThemeColors.primaryDark),
                                            ),
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          }
                          return Container();
                        },
                      ),
                    ),
                  ],
                ),
              );
            }
            return Container();
          }),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      floatingActionButton: CartButton2(onPressed: widget.cartBtnFunction),
    );
  }

  Widget header(Box list) {
    return Container(
      height:
          selectedDeliverMethod == 0 ? phoneHeight / 2.8 : phoneHeight / 4.05,
      padding: EdgeInsets.fromLTRB(
          defaultPadding, topPaddingWithoutBar, defaultPadding, defaultPadding),
      color: ThemeColors.primaryLight,
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(boxShadow: [
              BoxShadow(
                color: ThemeColors.disabled,
                offset: const Offset(0.0, 12.0),
                blurRadius: 8.0,
                spreadRadius: -20,
              )
            ]),
            child: Row(
              children: [
                Expanded(
                  child: DefaultSearchBar(
                    controller: searchController,
                    hintText: "Search",
                    borderRadius: 10,
                    onChanged: (value) {
                      if (value.isEmpty && onSearch) {
                        setState(() {
                          onSearch = false;
                        });
                      } else if (value.isNotEmpty && !onSearch) {
                        setState(() {
                          onSearch = true;
                        });
                      }
                    },
                  ),
                ),
                if (appSettings?.deliveryOptions?.hasDelivery == 1 &&
                    appSettings?.deliveryOptions?.hasPickup == 1)
                  Container(
                    margin: EdgeInsets.only(left: spacingWidth),
                    child: ToggleSwitch(
                      minHeight: buttonHeight,
                      minWidth: phoneWidth / 5,
                      cornerRadius: 10,
                      //todo get from asset
                      activeBgColor: [ThemeColors.primaryDark],
                      // activeBgColor: [ThemeColors.secondaryDark],
                      inactiveFgColor: ThemeColors.disabled,
                      radiusStyle: true,
                      centerText: true,
                      initialLabelIndex: selectedDeliverMethod,
                      totalSwitches: deliverMethod.length,
                      changeOnTap: false,
                      labels: deliverMethod,
                      customTextStyles: [
                        TextStyle(
                          fontSize: h4,
                          fontWeight: FontWeight.bold,
                        ),
                      ],
                      onToggle: (index) async {
                        if (selectedDeliverMethod != index) {
                          if (index == 0) {
                            Box box = Hive.box("box");
                            bool isLogin = box.get("is_login") ?? false;
                            if (isLogin) {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (BuildContext context) =>
                                        DeliverAddressOption(),
                                  ));
                            } else {
                              Navigator.of(context, rootNavigator: true)
                                  .pushNamed("/login/${loginAsset.template}");
                            }
                          } else if (index == 1) {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (BuildContext context) =>
                                      OrderOutlet.OrderMenuOutlet(
                                          selectedDeliverMethod: index),
                                ));
                          }
                        }
                      },
                    ),
                  ),
              ],
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Outlet Location:",
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          color: orderTheme == "L"
                              ? ThemeColors.dark
                              : ThemeColors.light,
                          fontSize: h3,
                        ),
                      ),
                      GestureDetector(
                        onTap: () async {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (BuildContext context) =>
                                  OrderOutlet.OrderMenuOutlet(),
                            ),
                          );
                        },
                        child: Container(
                          padding: EdgeInsets.all(defaultInnerPadding),
                          height: buttonHeight,
                          decoration: BoxDecoration(
                            color: ThemeColors.light,
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: ThemeColors.disabled),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  selectedOutlet?.name ?? "",
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    color: orderTheme == "L"
                                        ? ThemeColors.dark
                                        : ThemeColors.light,
                                    fontSize: h3,
                                  ),
                                ),
                              ),
                              SizedBox(width: spacingWidth),
                              //todo need get from asset
                              // ImageIcon(
                              //   AssetImage(
                              //       "assets/icons/${boxGlobal.get("iconSet")}/tabler_edit.png"),
                              //   color: orderTheme == "L"
                              //       ? ThemeColors.dark
                              //       : Color(
                              //           int.parse(boxGlobal.get("colorD4"))),
                              //   size: 20,
                              // ),
                              Icon(
                                Icons.keyboard_arrow_down,
                                color: orderTheme == "L"
                                    ? ThemeColors.dark
                                    : ThemeColors.light,
                                size: 25,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  )),
              if (schedule.length > 2) SizedBox(width: 5),
              if (schedule.length > 2)
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Schedule:",
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          color: orderTheme == "L"
                              ? ThemeColors.dark
                              : ThemeColors.light,
                          fontSize: h3,
                        ),
                      ),
                      Container(
                        child: DefaultDropdown1(
                          verticalMargin: 0,
                          borderRadius: 10,
                          selectedValue: selectedSchedule,
                          items: schedule.map((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(
                                value,
                                style: TextStyle(
                                  color: ThemeColors.dark,
                                  fontSize: h4,
                                ),
                              ),
                            );
                          }).toList(),
                          onChanged: (String? value) {
                            setState(() {
                              selectedSchedule = value!;
                            });
                            boxMenu.put("selected_schedule", selectedSchedule);
                          },
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
          if (selectedDeliverMethod == 0)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: defaultInnerPadding),
                Text(
                  "Deliver Address:",
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: orderTheme == "L"
                        ? ThemeColors.dark
                        : ThemeColors.light,
                    fontSize: h3,
                  ),
                ),
                GestureDetector(
                  onTap: () async {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (BuildContext context) =>
                              DeliverAddressOption(
                                  selectedAddressId: selectedAddress?.id),
                        ));
                  },
                  child: Container(
                    padding: EdgeInsets.all(defaultInnerPadding),
                    height: buttonHeight,
                    decoration: BoxDecoration(
                      color: ThemeColors.light,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: ThemeColors.disabled),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            selectedAddress?.name ?? "",
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: orderTheme == "L"
                                  ? ThemeColors.dark
                                  : ThemeColors.light,
                              fontSize: h3,
                            ),
                          ),
                        ),
                        SizedBox(width: spacingWidth),
                        ImageIcon(
                          AssetImage(
                              "assets/icons/${boxGlobal.get("iconSet")}/tabler_edit.png"),
                          color: orderTheme == "L"
                              ? ThemeColors.dark
                              : ThemeColors.light,
                          size: 20,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          SizedBox(height: spacingHeightSmall),
        ],
      ),
    );
  }

  Widget category() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(
            color: orderTheme == "L" ? ThemeColors.disabled : ThemeColors.gray,
            width: 3,
          ),
        ),
      ),
      width: phoneWidth / 4,
      child: ListView.builder(
        padding: EdgeInsets.zero,
        itemCount: categoryList.length,
        itemBuilder: (BuildContext context, int index) {
          return GestureDetector(
              onTap: () {
                setState(() {
                  selectedCategoryIndex = index;
                  pageController.jumpToPage(index);
                });
              },
              child: Container(
                height: categoryList[index].icon == null
                    ? phoneHeight / 13
                    : phoneHeight / 8,
                padding: EdgeInsets.symmetric(
                  horizontal: spacingHeightSmall,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (categoryList[index].icon != null)
                      Container(
                        alignment: Alignment.center,
                        padding: EdgeInsets.only(bottom: spacingHeightSmall),
                        child: CachedNetworkImage(
                          imageUrl: categoryList[index].icon!,
                          width: phoneWidth / 12,
                          height: phoneWidth / 12,
                          placeholder: (context, url) {
                            return Center(
                              child: CircularProgressIndicator(
                                color: (selectedCategoryIndex == index)
                                    ? ThemeColors.light
                                    : menuTheme == "L"
                                        ? ThemeColors.dark
                                        : ThemeColors.light,
                              ),
                            );
                          },
                          errorWidget: (context, url, error) {
                            return Icon(
                              Icons.error_outline_outlined,
                              color: (selectedCategoryIndex == index)
                                  ? ThemeColors.light
                                  : menuTheme == "L"
                                      ? ThemeColors.dark
                                      : ThemeColors.light,
                            );
                          },
                        ),
                      ),
                    Text(
                      categoryList[index].name,
                      style: TextStyle(
                        color: (selectedCategoryIndex == index)
                            ? ThemeColors.primaryDark
                            : ThemeColors.gray,
                        height: 1.25,
                        fontSize: h4,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ));
        },
      ),
    );
  }

  Widget item() {
    return Expanded(
      child: PageView(
        physics: const NeverScrollableScrollPhysics(),
        controller: pageController,
        children: List.generate(categoryList.length, (index) {
          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                categoryList[index].banner != null
                    ? CachedNetworkImage(
                        imageUrl: categoryList[index].banner!,
                        height: phoneHeight * 0.10,
                        width: double.infinity,
                        fit: BoxFit.cover,
                      )
                    : Text(
                        categoryList[index].name,
                        style: TextStyle(
                            color: orderTheme == "L"
                                ? ThemeColors.dark
                                : ThemeColors.light,
                            fontSize: h2,
                            fontWeight: FontWeight.bold),
                      ),
                Container(
                  padding: EdgeInsets.only(
                    left: defaultPadding,
                    bottom: buttonHeight * 2,
                  ),
                  child: GridView.builder(
                      padding: EdgeInsets.only(top: defaultInnerPadding),
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 1,
                        mainAxisExtent: phoneHeight * 0.15,
                      ),
                      shrinkWrap: true,
                      //here
                      itemCount:
                          categoryList[selectedCategoryIndex].products.length,
                      itemBuilder: (BuildContext ctx, index) {
                        return GestureDetector(
                          onTap: () {
                            OrderMenuPage.getProductDetail(
                              context,
                              categoryList[selectedCategoryIndex]
                                  .products[index]
                                  .id,
                              "order_menu/",
                            );
                          },
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: Container(
                                      margin: EdgeInsets.only(right: 8),
                                      child: Hero(
                                        tag:
                                            "order_menu/${categoryList[selectedCategoryIndex].products[index].id}",
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(20),
                                          child: CachedNetworkImage(
                                            imageUrl: categoryList[
                                                        selectedCategoryIndex]
                                                    .products[index]
                                                    .image ??
                                                "",
                                            height: phoneWidth / 4,
                                            width: phoneWidth / 4,
                                            fit: BoxFit.cover,
                                            placeholder: (context, url) {
                                              return Center(
                                                child:
                                                    CircularProgressIndicator(
                                                  color:
                                                      ThemeColors.primaryDark,
                                                ),
                                              );
                                            },
                                            errorWidget: (context, url, error) {
                                              return Icon(
                                                Icons.error_outline_outlined,
                                                color: ThemeColors.primaryDark,
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          categoryList[selectedCategoryIndex]
                                              .products[index]
                                              .name,
                                          style: TextStyle(
                                            color: ThemeColors.dark,
                                            fontWeight: FontWeight.w600,
                                            fontSize: h3,
                                          ),
                                        ),
                                        Text(
                                          important_variables.currency +
                                              categoryList[
                                                      selectedCategoryIndex]
                                                  .products[index]
                                                  .price!,
                                          style: TextStyle(
                                            color: ThemeColors.primaryDark,
                                            fontSize: h4,
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                ],
                              ),
                              Container(
                                margin: EdgeInsets.only(top: defaultPadding),
                                color: ThemeColors.disabled,
                                height: 1,
                              )
                            ],
                          ),
                        );
                      }),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget searchItem() {
    return GridView.builder(
      padding: EdgeInsets.only(
        bottom: defaultInnerPadding,
        left: defaultPadding,
      ),
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        mainAxisExtent: phoneHeight / 4.5,
      ),
      shrinkWrap: true,
      itemCount: searchList.length,
      itemBuilder: (BuildContext ctx, index) {
        return GestureDetector(
          onTap: () {
            OrderMenuPage.getProductDetail(
                context, searchList[index].id, "order_menu/search/");
          },
          child: Container(
            margin: EdgeInsets.only(right: spacingWidth),
            child: Column(
              children: [
                Hero(
                  tag: "order_menu/search/${searchList[index].id}",
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: CachedNetworkImage(
                      imageUrl: searchList[index].image ?? "",
                      height: phoneWidth / 3.5,
                      fit: BoxFit.cover,
                      placeholder: (context, url) {
                        return Center(
                          child: CircularProgressIndicator(
                            color: ThemeColors.primaryDark,
                          ),
                        );
                      },
                      errorWidget: (context, url, error) {
                        return Icon(
                          Icons.error_outline_outlined,
                          color: ThemeColors.primaryDark,
                        );
                      },
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Column(
                    children: [
                      AutoSizeText(
                        searchList[index].name,
                        style: TextStyle(color: ThemeColors.dark),
                        maxFontSize: h4,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                      // SizedBox(height: spacingHeightSmall),
                      Text(
                        important_variables.currency + searchList[index].price!,
                        style: TextStyle(
                          color: ThemeColors.primaryDark,
                          fontSize: h4,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }
}
