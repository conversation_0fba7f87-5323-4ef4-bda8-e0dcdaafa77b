import 'dart:async';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/app_settings.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/product.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/cart_button1.dart';
import 'package:amverton/View/Custom%20Widgets/default_dropdown1.dart';
import 'package:amverton/View/Custom%20Widgets/default_search_bar.dart';
import 'package:amverton/View/OrderMenu/deliver_address_option.dart';
import 'package:amverton/View/OrderMenu/order_menu_outlet.dart' as OrderOutlet;
import 'package:amverton/View/OrderMenu/order_menu_page.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;
import 'package:toggle_switch/toggle_switch.dart';

class MenuTemplate1 extends StatefulWidget {
  final Function() cartBtnFunction;

  const MenuTemplate1({
    Key? key,
    required this.cartBtnFunction,
  }) : super(key: key);

  @override
  _MenuTemplate1State createState() => _MenuTemplate1State();
}

class _MenuTemplate1State extends State<MenuTemplate1> {
  late Future boxFuture;
  late Box boxGlobal;
  late Box boxMenu;
  AppSettings? appSettings;

  Outlet? outlet;
  int selectedIndex = 0;
  List<String> deliverMethod = ["Delivery", "Pickup"];
  String selectedSchedule = "";
  bool onSearch = false;
  List<Product> searchList = [];
  PageController pageController = PageController();
  TextEditingController searchController = TextEditingController();
  final StreamController streamController = StreamController.broadcast();

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();

    pageController = PageController();
    searchController.addListener(() {
      streamController.add(searchController.text);
    });
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    boxMenu = await Hive.openBox('boxMenu');
    outlet = await boxMenu.get("selected_outlet");
    appSettings = boxGlobal.get("appSettings") ?? null;

    return await Hive.openBox<Asset>('ORDER+MAIN+1');
  }

  getSelectedSchedule() async {
    if (schedule.isNotEmpty) {
      selectedSchedule = boxMenu.get("selected_schedule") ?? "";

      int check = schedule.indexWhere((element) => element == selectedSchedule);
      if (check == -1) selectedSchedule = schedule.first;
      await boxMenu.put("selected_schedule", selectedSchedule);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FutureBuilder(
          future: boxFuture,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            if (snapshot.connectionState == ConnectionState.done) {
              final Box list = snapshot.data;
              list.watch();

              String? backgroundType = list.values
                      .firstWhereOrNull(
                          (element) => element.name == "MENU_BACKGROUND_TYPE")
                      ?.data ??
                  "";
              Asset? background = list.values.firstWhereOrNull((element) =>
                  element.name == "MENU_BACKGROUND_$backgroundType");

              return Container(
                decoration: background == null
                    ? null
                    : BoxDecoration(
                        image: backgroundType == "IMAGE"
                            ? DecorationImage(
                                image: CachedNetworkImageProvider(
                                    background.data ?? ""),
                                fit: BoxFit.cover,
                              )
                            : null,
                        color: backgroundType == "COLOR"
                            ? Color(
                                int.parse("0xff${background.data ?? "FFFFFF"}"))
                            : null,
                      ),
                width: phoneWidth,
                child: Column(
                  children: [
                    FutureBuilder(
                        future: future,
                        builder:
                            (BuildContext context, AsyncSnapshot snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.done) {
                            getSelectedSchedule();
                            return header(list);
                          }
                          return Container();
                        }),
                    Visibility(
                      visible: !onSearch,
                      child: FutureBuilder(
                          future: future,
                          builder:
                              (BuildContext context, AsyncSnapshot snapshot) {
                            if (snapshot.connectionState ==
                                ConnectionState.done) {
                              return Expanded(
                                child: Row(
                                  children: [
                                    category(),
                                    item(),
                                  ],
                                ),
                              );
                            }
                            return Container();
                          }),
                    ),
                    Visibility(
                      visible: onSearch,
                      child: StreamBuilder(
                        stream: streamController.stream.asBroadcastStream(),
                        builder:
                            (BuildContext context, AsyncSnapshot snapshot) {
                          if (snapshot.hasData) {
                            return Expanded(
                              child: SingleChildScrollView(
                                padding: EdgeInsets.only(top: defaultPadding),
                                child: Column(
                                  children: [
                                    Text(
                                      "Search Result",
                                      style: TextStyle(
                                          color: orderTheme == "L"
                                              ? ThemeColors.dark
                                              : ThemeColors.light,
                                          fontSize: h2,
                                          fontWeight: FontWeight.bold),
                                    ),
                                    SizedBox(height: spacingHeightMedium),
                                    FutureBuilder(
                                      future: getMenuSearch(snapshot.data),
                                      builder: (BuildContext context,
                                          AsyncSnapshot snapshot) {
                                        if (snapshot.data != null &&
                                            snapshot.data['data'] != null) {
                                          searchList = (snapshot.data['data']
                                                  ['products'] as List)
                                              .map((data) =>
                                                  Product.fromJson(data))
                                              .toList();

                                          return searchItem();
                                        }
                                        return SizedBox(
                                          height: phoneHeight / 3,
                                          child: Center(
                                            child: CircularProgressIndicator(
                                                color: ThemeColors.primaryDark),
                                          ),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }
                          return Container();
                        },
                      ),
                    ),
                  ],
                ),
              );
            }
            return Container();
          }),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: CartButton1(onPressed: widget.cartBtnFunction),
    );
  }

  Widget header(Box list) {
    return Container(
      height:
          selectedDeliverMethod == 0 ? phoneHeight / 3.2 : phoneHeight / 4.3,
      decoration: BoxDecoration(
        color: ThemeColors.primaryLight,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(15),
          bottomRight: Radius.circular(15),
        ),
      ),
      padding: EdgeInsets.fromLTRB(
          defaultPadding, topPaddingWithoutBar, defaultPadding, defaultPadding),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: DefaultSearchBar(
                    controller: searchController,
                    hintText: "Search",
                    borderRadius: 15,
                    onChanged: (value) {
                      if (value.isEmpty && onSearch) {
                        setState(() {
                          onSearch = false;
                        });
                      } else if (value.isNotEmpty && !onSearch) {
                        setState(() {
                          onSearch = true;
                        });
                      }
                    }),
              ),
              if (appSettings?.deliveryOptions?.hasDelivery == 1 &&
                  appSettings?.deliveryOptions?.hasPickup == 1)
                Container(
                  margin: EdgeInsets.only(left: spacingWidth),
                  child: ToggleSwitch(
                    minHeight: buttonHeight,
                    minWidth: phoneWidth / 5,
                    cornerRadius: 10,
                    activeBgColor: [ThemeColors.primaryDark],
                    inactiveFgColor: ThemeColors.disabled,
                    radiusStyle: true,
                    centerText: true,
                    initialLabelIndex: selectedDeliverMethod,
                    totalSwitches: deliverMethod.length,
                    changeOnTap: false,
                    labels: deliverMethod,
                    customTextStyles: [
                      TextStyle(
                        fontSize: h4,
                        fontWeight: FontWeight.bold,
                      ),
                    ],
                    onToggle: (index) async {
                      if (selectedDeliverMethod != index) {
                        if (index == 0) {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (BuildContext context) =>
                                    DeliverAddressOption(),
                              ));
                        } else if (index == 1) {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (BuildContext context) =>
                                    OrderOutlet.OrderMenuOutlet(
                                        selectedDeliverMethod: index),
                              ));
                        }
                      }
                    },
                  ),
                ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex: 2,
                child: GestureDetector(
                  onTap: () async {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (BuildContext context) =>
                              OrderOutlet.OrderMenuOutlet(),
                        ));
                  },
                  child: Container(
                    padding: EdgeInsets.all(defaultInnerPadding),
                    height: buttonHeight,
                    decoration: BoxDecoration(
                      color: ThemeColors.light,
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(color: ThemeColors.disabled),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            selectedOutlet?.name ?? "",
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: orderTheme == "L"
                                  ? ThemeColors.dark
                                  : ThemeColors.light,
                              fontSize: h4,
                            ),
                          ),
                        ),
                        SizedBox(width: spacingWidth),
                        ImageIcon(
                          AssetImage(
                              "assets/icons/${boxGlobal.get("iconSet")}/tabler_edit.png"),
                          color: orderTheme == "L"
                              ? ThemeColors.dark
                              : ThemeColors.light,
                          size: 20,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              if (schedule.length > 2) SizedBox(width: 5),
              if (schedule.length > 2)
                Expanded(
                  child: Container(
                    child: DefaultDropdown1(
                      verticalMargin: 0,
                      borderRadius: 15,
                      selectedValue: selectedSchedule,
                      items: schedule.map((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(
                            value,
                            style: TextStyle(
                              color: ThemeColors.dark,
                              fontSize: h5,
                            ),
                          ),
                        );
                      }).toList(),
                      onChanged: (String? value) {
                        setState(() {
                          selectedSchedule = value!;
                        });
                        boxMenu.put("selected_schedule", selectedSchedule);
                      },
                    ),
                  ),
                ),
            ],
          ),
          if (selectedDeliverMethod == 0)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: defaultInnerPadding),
                GestureDetector(
                  onTap: () async {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (BuildContext context) =>
                              DeliverAddressOption(
                                  selectedAddressId: selectedAddress?.id),
                        ));
                  },
                  child: Container(
                    padding: EdgeInsets.all(defaultInnerPadding),
                    height: buttonHeight,
                    decoration: BoxDecoration(
                      color: ThemeColors.light,
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(color: ThemeColors.disabled),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            selectedAddress?.name ?? "",
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: orderTheme == "L"
                                  ? ThemeColors.dark
                                  : ThemeColors.light,
                              fontSize: h4,
                            ),
                          ),
                        ),
                        SizedBox(width: spacingWidth),
                        ImageIcon(
                          AssetImage(
                              "assets/icons/${boxGlobal.get("iconSet")}/tabler_edit.png"),
                          color: orderTheme == "L"
                              ? ThemeColors.dark
                              : ThemeColors.light,
                          size: 20,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          SizedBox(height: spacingHeightSmall),
        ],
      ),
    );
  }

  Widget category() {
    return Container(
      padding: EdgeInsets.only(bottom: buttonHeight),
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(
            color: orderTheme == "L" ? ThemeColors.disabled : ThemeColors.gray,
            width: 3,
          ),
        ),
      ),
      width: phoneWidth / 4,
      child: ListView.builder(
        padding: EdgeInsets.zero,
        itemCount: categoryList.length,
        itemBuilder: (BuildContext context, int index) {
          return GestureDetector(
              onTap: () {
                setState(() {
                  selectedIndex = index;
                  pageController.jumpToPage(index);
                });
              },
              child: Container(
                decoration: BoxDecoration(
                  color: (selectedIndex == index)
                      ? ThemeColors.primaryDark
                      : orderTheme == "L"
                          ? ThemeColors.light
                          : ThemeColors.dark,
                  border: Border(
                    bottom: BorderSide(
                      color: orderTheme == "L"
                          ? ThemeColors.disabled
                          : ThemeColors.gray,
                    ),
                  ),
                ),
                height: categoryList[index].icon == null
                    ? phoneHeight / 13
                    : phoneHeight / 8,
                padding: EdgeInsets.symmetric(
                  horizontal: spacingHeightSmall,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (categoryList[index].icon != null)
                      Container(
                        alignment: Alignment.center,
                        padding: EdgeInsets.only(bottom: spacingHeightSmall),
                        child: CachedNetworkImage(
                          imageUrl: categoryList[index].icon!,
                          width: phoneWidth / 12,
                          height: phoneWidth / 12,
                          placeholder: (context, url) {
                            return Center(
                              child: CircularProgressIndicator(
                                color: (selectedIndex == index)
                                    ? ThemeColors.light
                                    : menuTheme == "L"
                                        ? ThemeColors.dark
                                        : ThemeColors.light,
                              ),
                            );
                          },
                          errorWidget: (context, url, error) {
                            return Icon(
                              Icons.error_outline_outlined,
                              color: (selectedIndex == index)
                                  ? ThemeColors.light
                                  : menuTheme == "L"
                                      ? ThemeColors.dark
                                      : ThemeColors.light,
                            );
                          },
                        ),
                      ),
                    Text(
                      categoryList[index].name,
                      style: TextStyle(
                        color: (selectedIndex == index)
                            ? ThemeColors.light
                            : ThemeColors.primaryDark,
                        height: 1.25,
                        fontSize: h4,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ));
        },
      ),
    );
  }

  Widget item() {
    return Expanded(
      child: PageView(
        physics: const NeverScrollableScrollPhysics(),
        controller: pageController,
        children: List.generate(categoryList.length, (index) {
          return SingleChildScrollView(
            padding: EdgeInsets.only(
              left: defaultPadding,
              top: defaultPadding,
              bottom: buttonHeight * 2,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  categoryList[index].name,
                  style: TextStyle(
                      color: orderTheme == "L"
                          ? ThemeColors.dark
                          : ThemeColors.light,
                      fontSize: h2,
                      fontWeight: FontWeight.bold),
                ),
                GridView.builder(
                    padding: EdgeInsets.only(top: defaultInnerPadding),
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      mainAxisExtent: phoneHeight / 3.5,
                      mainAxisSpacing: 10,
                    ),
                    shrinkWrap: true,
                    itemCount: categoryList[selectedIndex].products.length,
                    itemBuilder: (BuildContext ctx, index) {
                      return GestureDetector(
                        onTap: () {
                          OrderMenuPage.getProductDetail(
                            context,
                            categoryList[selectedIndex].products[index].id,
                            "order_menu/",
                          );
                        },
                        child: Container(
                          margin: EdgeInsets.only(right: spacingWidth),
                          decoration: BoxDecoration(
                            color: ThemeColors.light,
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(color: ThemeColors.disabled),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Hero(
                                tag:
                                    "order_menu/${categoryList[selectedIndex].products[index].id}",
                                child: ClipRRect(
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(20),
                                    topRight: Radius.circular(20),
                                  ),
                                  child: CachedNetworkImage(
                                    imageUrl: categoryList[selectedIndex]
                                            .products[index]
                                            .image ??
                                        "",
                                    height: phoneWidth / 3,
                                    width: phoneWidth / 3,
                                    fit: BoxFit.cover,
                                    placeholder: (context, url) {
                                      return Center(
                                        child: CircularProgressIndicator(
                                          color: ThemeColors.primaryDark,
                                        ),
                                      );
                                    },
                                    errorWidget: (context, url, error) {
                                      return Icon(
                                        Icons.error_outline_outlined,
                                        color: ThemeColors.primaryDark,
                                      );
                                    },
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(8),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    AutoSizeText(
                                      categoryList[selectedIndex].name,
                                      style: TextStyle(
                                          color: ThemeColors.primaryDark,
                                          fontSize: h4),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    AutoSizeText(
                                      categoryList[selectedIndex]
                                          .products[index]
                                          .name,
                                      style: TextStyle(
                                          color: ThemeColors.dark,
                                          fontSize: h4),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    SizedBox(height: spacingHeightSmall),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: AutoSizeText(
                                            important_variables.currency +
                                                categoryList[selectedIndex]
                                                    .products[index]
                                                    .price!,
                                            style: TextStyle(
                                              color: ThemeColors.primaryDark,
                                              fontWeight: FontWeight.bold,
                                            ),
                                            minFontSize: h5,
                                            maxFontSize: h3,
                                            maxLines: 1,
                                          ),
                                        ),
                                        SizedBox(width: spacingWidth),
                                        GestureDetector(
                                          onTap: () {
                                            OrderMenuPage.getProductDetail(
                                              context,
                                              categoryList[selectedIndex]
                                                  .products[index]
                                                  .id,
                                              "order_menu/",
                                            );
                                          },
                                          child: ImageIcon(
                                            AssetImage(
                                                "assets/icons/${boxGlobal.get("iconSet")}/Cart 01.png"),
                                            color: ThemeColors.primaryDark,
                                          ),
                                        )
                                      ],
                                    )
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                      );
                    }),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget searchItem() {
    return GridView.builder(
      padding: EdgeInsets.only(
        bottom: defaultInnerPadding,
        left: defaultPadding,
      ),
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        mainAxisExtent: phoneHeight / 4.25,
        mainAxisSpacing: 10,
      ),
      shrinkWrap: true,
      itemCount: searchList.length,
      itemBuilder: (BuildContext ctx, index) {
        return GestureDetector(
          onTap: () {
            OrderMenuPage.getProductDetail(
                context, searchList[index].id, "order_menu/search/");
          },
          child: Container(
            margin: EdgeInsets.only(right: spacingWidth),
            decoration: BoxDecoration(
              color: ThemeColors.light,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: ThemeColors.disabled),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Hero(
                  tag: "order_menu/search/${searchList[index].id}",
                  child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                    child: CachedNetworkImage(
                      imageUrl: searchList[index].image ?? "",
                      height: phoneWidth / 3.5,
                      width: phoneWidth / 3.5,
                      fit: BoxFit.cover,
                      placeholder: (context, url) {
                        return Center(
                          child: CircularProgressIndicator(
                            color: ThemeColors.primaryDark,
                          ),
                        );
                      },
                      errorWidget: (context, url, error) {
                        return Icon(
                          Icons.error_outline_outlined,
                          color: ThemeColors.primaryDark,
                        );
                      },
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AutoSizeText(
                        searchList[index].name,
                        style: TextStyle(color: ThemeColors.dark, fontSize: h4),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: spacingHeightSmall),
                      Text(
                        important_variables.currency + searchList[index].price!,
                        style: TextStyle(
                          color: ThemeColors.primaryDark,
                          fontSize: h3,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }
}
