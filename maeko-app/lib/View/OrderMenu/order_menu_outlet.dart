import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/location_service.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hive/hive.dart';

class OrderMenuOutlet extends StatefulWidget {
  final int? selectedDeliverMethod;

  OrderMenuOutlet({
    Key? key,
    this.selectedDeliverMethod,
  }) : super(key: key);

  @override
  _OrderMenuOutletState createState() => _OrderMenuOutletState();
}

List<Outlet> outletList = [];
late Future future;
int? selectedDeliverMethod;

class _OrderMenuOutletState extends State<OrderMenuOutlet> {
  late Future boxFuture;
  late Box boxGlobal;
  late Box boxMenu;

  int service = 0;

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();
    selectedDeliverMethod = widget.selectedDeliverMethod;
    future = getOutlet();
  }

  openBox() async {
    boxMenu = await Hive.openBox('boxMenu');
    setState(() {
      service = boxMenu.get("service") ?? 0;
    });
    boxGlobal = await Hive.openBox('boxGlobal');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: boxFuture,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          return GestureDetector(
            onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
            behavior: HitTestBehavior.translucent,
            child: Scaffold(
              extendBodyBehindAppBar: true,
              appBar: AppBar(
                title: Text(
                  "Outlet",
                ),
              ),
              body: RefreshIndicator(
                  color: ThemeColors.primaryDark,
                  onRefresh: () {
                    return Future.delayed(const Duration(seconds: 1), () {
                      setState(() {
                        future = getOutlet();
                      });
                    });
                  },
                  child: Stack(
                    children: [
                      orderMainWidget ?? Container(),
                    ],
                  )),
            ),
          );
        }
        return Container();
      },
    );
  }

  Future<Map?> getOutlet() async {
    ApiGet apiGet = ApiGet();

    // check location permission
    Position? currentPosition;
    if (Platform.isIOS) {
      currentPosition = await LocationService.getCurrentPosition();
    } else {
      //do not need checking cause it will return null.
      //     await LocationService.checkAndroidLocationPermission();
      // if (permissionCheck != null)
      currentPosition = await LocationService.getCurrentPosition();
    }

    Map? data = await apiGet.outletList(
        currentPosition?.latitude, currentPosition?.longitude);

    if (data != null && data['data'] != null) {
      outletList = (data['data']['outlets'] as List)
          .map((data) => Outlet.fromJson(data))
          .toList();
    }
    return data;
  }
}

Future<Map?> getOutletSearch(String? searchValue) async {
  ApiGet apiGet = ApiGet();
  Map? data = await apiGet.outletListSearch(searchValue);
  return data;
}
