import 'dart:async';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/address.dart';
import 'package:amverton/Model/bottom_bar.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Address/address_page.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/OrderMenu/order_menu_outlet.dart';
import 'package:amverton/View/OrderMenu/order_menu_page.dart';
import 'package:amverton/main.dart';
import 'package:hive/hive.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class DeliverAddressOption extends StatefulWidget {
  final int? selectedAddressId;
  final bool updateCartDeliverAddress;

  DeliverAddressOption({
    Key? key,
    this.selectedAddressId,
    this.updateCartDeliverAddress = false,
  }) : super(key: key);

  @override
  State<DeliverAddressOption> createState() => _DeliverAddressOptionState();
}

class _DeliverAddressOptionState extends State<DeliverAddressOption> {
  late Future future;
  Box boxGlobal = Hive.box("boxGlobal");
  List<Address> addressList = [];

  @override
  void initState() {
    super.initState();
    future = getAddressList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeColors.light,
      appBar: AppBar(
        title: const Text("Address Selection"),
      ),
      body: FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            if (addressList.isNotEmpty) {
              return Column(
                children: [
                  Align(
                    alignment: Alignment.topRight,
                    child: TextButton(
                      onPressed: () {
                        Route route = MaterialPageRoute(
                            builder: (context) => AddressPage());
                        Navigator.push(context, route).then(onRefresh);
                      },
                      child: Text(
                        "Edit",
                        style: TextStyle(
                          color: ThemeColors.secondaryDark,
                          fontSize: h4,
                        ),
                      ),
                    ),
                  ),
                  ListView.builder(
                    padding: EdgeInsets.all(defaultPadding),
                    itemCount: addressList.length,
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemBuilder: (BuildContext context, int index) {
                      return GestureDetector(
                        onTap: () async {
                          Box boxMenu = await Hive.openBox('boxMenu');
                          await boxMenu.put("selected_deliver_method", 0);
                          await boxMenu.put(
                              "selected_address", addressList[index]);

                          if (widget.updateCartDeliverAddress) {
                            Navigator.pop(context, addressList[index].id);
                          } else {
                            List<BottomBar> bottomBarList =
                                boxGlobal.get("bottomBar") ?? [];

                            if (bottomBarList.isNotEmpty) {
                              int orderIndex = bottomBarList.indexWhere(
                                  (element) => element.name == "ORDER");
                              if (orderIndex != -1) {
                                int? selectedDeliverMethod = await boxMenu
                                        .get("selected_deliver_method") ??
                                    null;
                                Outlet? selectedOutlet =
                                    boxMenu.get("selected_outlet") ?? null;

                                if (selectedDeliverMethod == null ||
                                    selectedDeliverMethod == 0 ||
                                    (selectedDeliverMethod == 1 &&
                                        selectedOutlet == null)) {
                                  Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (BuildContext context) =>
                                            OrderMenuOutlet(
                                                selectedDeliverMethod: 0),
                                      ));
                                } else {
                                  final dynamic navigationBar =
                                      barGlobalKey.currentWidget;
                                  navigationBar.onTap(orderIndex);
                                }
                              }
                            }
                          }
                        },
                        child: Container(
                            margin:
                                EdgeInsets.only(bottom: defaultInnerPadding),
                            padding: EdgeInsets.all(defaultPadding),
                            decoration: BoxDecoration(
                              color: ThemeColors.light,
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(color: ThemeColors.disabled),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Text(
                                            addressList[index].name,
                                            style: TextStyle(
                                                fontSize: h3,
                                                fontWeight: FontWeight.bold),
                                          ),
                                          SizedBox(width: spacingHeightSmall),
                                          Text(
                                            "${addressList[index].countryCode}${addressList[index].phone}",
                                            style: TextStyle(fontSize: h3),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: spacingHeightSmall),
                                      Text(
                                        "${addressList[index].address1}, ${addressList[index].address2 ?? ""}\n${addressList[index].postcode} ${addressList[index].city}, ${addressList[index].state?.name ?? ""}",
                                        style: TextStyle(fontSize: h4),
                                      ),
                                      if (addressList[index].isDefault == 1)
                                        Container(
                                          margin: EdgeInsets.only(
                                              top: spacingHeightSmall),
                                          padding: EdgeInsets.symmetric(
                                              horizontal: spacingHeightSmall),
                                          decoration: BoxDecoration(
                                            border: Border.all(
                                                color:
                                                    ThemeColors.secondaryDark),
                                          ),
                                          child: Text(
                                            "Default",
                                            style: TextStyle(
                                              color: ThemeColors.secondaryDark,
                                              fontSize: h4,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                                SizedBox(width: spacingWidth),
                                Icon(
                                  Icons.check,
                                  color: widget.selectedAddressId ==
                                          addressList[index].id
                                      ? ThemeColors.primaryDark
                                      : ThemeColors.light,
                                ),
                              ],
                            )),
                      );
                    },
                  ),
                ],
              );
            } else {
              return Container(
                padding: EdgeInsets.symmetric(
                    horizontal: verticalPaddingSmall,
                    vertical: bottomPaddingWithoutBar),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Spacer(),
                    Icon(
                      Icons.edit_location_outlined,
                      size: 80,
                      color: ThemeColors.primaryDark,
                    ),
                    SizedBox(height: spacingHeightLarge),
                    Text(
                      "You don't have any deliver address yet. Please click the button below to add an address.",
                      style: TextStyle(
                        color: ThemeColors.dark,
                        fontSize: h3,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            }
          }
          return Container();
        },
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.fromLTRB(
          defaultInnerPadding,
          defaultInnerPadding,
          defaultInnerPadding,
          bottomPaddingWithoutBar,
        ),
        child: DefaultButton(
          text: "Add New Address",
          buttonColor: important_variables.projectName == "obriens"
              ? ThemeColors.secondaryDark
              : ThemeColors.primaryDark,
          onPressed: () {
            int index = registerAsset
                .indexWhere((element) => element.sectionName == "STEP_1");
            // element.sectionName == "STEP_3");
            if (index != -1)
              Navigator.pushNamed(
                      context, "/add_address/${registerAsset[index].template}")
                  .then(onRefresh);
          },
        ),
      ),
    );
  }

  FutureOr onRefresh(dynamic value) {
    setState(() {
      future = getAddressList();
    });
  }

  Future<Map?> getAddressList() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.addressList();

    if (data != null && data['data'] != null) {
      addressList = (data['data']['addresses'] as List)
          .map((data) => Address.fromJson(data))
          .toList();
    }
    return data;
  }
}
