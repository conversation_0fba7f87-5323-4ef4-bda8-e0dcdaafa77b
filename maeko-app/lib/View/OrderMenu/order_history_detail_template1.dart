import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:easy_stepper/easy_stepper.dart' as EasyStepper;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/eghl_payment.dart';
import 'package:amverton/Model/order.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Home/web_view.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class OrderHistoryDetailTemplate1 extends StatefulWidget {
  final int id;

  const OrderHistoryDetailTemplate1({Key? key, required this.id})
      : super(key: key);

  static const androidPlatform = const MethodChannel('fnb.android');
  static const iosPlatform = const MethodChannel('fnb.ios');

  @override
  _OrderHistoryDetailTemplate1State createState() =>
      _OrderHistoryDetailTemplate1State();
}

class _OrderHistoryDetailTemplate1State
    extends State<OrderHistoryDetailTemplate1> {
  Box boxGlobal = Hive.box("boxGlobal");
  String theme = "L";

  late Future future;
  Order? order;
  int activeStep = 0;

  @override
  void initState() {
    super.initState();
    future = getOrderDetail();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("Track Order")),
      body: FutureBuilder(
          future: future,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            if (snapshot.connectionState == ConnectionState.done) {
              if (order != null) {
                return SizedBox(
                  height: phoneHeight,
                  child: Column(
                    children: [
                      Expanded(
                        flex: order!.paymentStatus == "PENDING" ? 2 : 3,
                        child: Container(
                          width: phoneWidth,
                          margin: EdgeInsets.all(defaultPadding),
                          decoration: BoxDecoration(
                            color: ThemeColors.light,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: SingleChildScrollView(
                            padding: EdgeInsets.all(defaultPadding),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                orderInfo(),
                                SizedBox(height: spacingHeightMedium),
                                Divider(),
                                SizedBox(height: spacingHeightSmall),
                                // product //
                                Text(
                                  "Your Order (${order!.items?.length ?? "0"})",
                                  style: TextStyle(
                                    color: ThemeColors.dark,
                                    fontSize: h2,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: spacingHeightMedium),
                                Column(
                                  children: List.generate(order!.items!.length,
                                      (index) {
                                    return Container(
                                      margin: EdgeInsets.only(
                                          bottom: spacingHeightMedium),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                child: Text(
                                                  order!.items![index].product
                                                          ?.name ??
                                                      "",
                                                  style: TextStyle(
                                                    color: ThemeColors.dark,
                                                    fontSize: h3,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                              SizedBox(width: spacingWidth),
                                              Text(
                                                important_variables.currency +
                                                    order!.items![index].price,
                                                style: TextStyle(
                                                  color:
                                                      ThemeColors.primaryDark,
                                                  fontSize: h3,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ],
                                          ),
                                          if (order!
                                                  .items![index].bundleItems !=
                                              null)
                                            bundle(index),
                                          if (order!.items![index]
                                                  .modifierItems !=
                                              null)
                                            modifier(index),
                                          if (order!.items![index].remark !=
                                              null)
                                            Text(
                                              order!.items![index].remark!,
                                              style: TextStyle(
                                                color: ThemeColors.gray,
                                                fontSize: h4,
                                              ),
                                            ),
                                          SizedBox(height: spacingHeightSmall),
                                          Text(
                                            "${order!.items![index].quantity} item",
                                            style: TextStyle(
                                              color: ThemeColors.dark,
                                              fontSize: h4,
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  }),
                                ),
                                if (order!.remark != null) remark(),
                              ],
                            ),
                          ),
                        ),
                      ),
                      // summary
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: defaultPadding,
                            vertical: verticalPaddingSmall,
                          ),
                          decoration: BoxDecoration(
                            color: ThemeColors.light,
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(20),
                              bottomRight: Radius.circular(20),
                            ),
                          ),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "Subtotal",
                                    style: TextStyle(
                                      color: ThemeColors.dark,
                                      fontSize: h3,
                                    ),
                                  ),
                                  Text(
                                    important_variables.currency +
                                        order!.subtotal,
                                    style: TextStyle(
                                      color: ThemeColors.dark,
                                      fontSize: h3,
                                    ),
                                  ),
                                ],
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "Discount",
                                    style: TextStyle(
                                      color: ThemeColors.dark,
                                      fontSize: h3,
                                    ),
                                  ),
                                  Text(
                                    important_variables.currency +
                                        order!.discount,
                                    style: TextStyle(
                                      color: ThemeColors.dark,
                                      fontSize: h3,
                                    ),
                                  ),
                                ],
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "SST",
                                    style: TextStyle(
                                      color: ThemeColors.dark,
                                      fontSize: h3,
                                    ),
                                  ),
                                  Text(
                                    important_variables.currency + order!.tax,
                                    style: TextStyle(
                                      color: ThemeColors.dark,
                                      fontSize: h3,
                                    ),
                                  ),
                                ],
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "Service Tax",
                                    style: TextStyle(
                                      color: ThemeColors.dark,
                                      fontSize: h3,
                                    ),
                                  ),
                                  Text(
                                    important_variables.currency +
                                        order!.serviceTax,
                                    style: TextStyle(
                                      color: ThemeColors.dark,
                                      fontSize: h3,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: spacingHeightMedium),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "Total",
                                    style: TextStyle(
                                      color: ThemeColors.primaryDark,
                                      fontSize: h2,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    important_variables.currency + order!.total,
                                    style: TextStyle(
                                      color: ThemeColors.primaryDark,
                                      fontSize: h2,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              if (order!.paymentStatus == "PENDING")
                                SizedBox(height: spacingHeightMedium),
                              if (order!.paymentStatus == "PENDING")
                                DefaultButton(
                                  text: "Remake Payment",
                                  buttonColor:
                                      important_variables.projectName ==
                                              "obriens"
                                          ? ThemeColors.secondaryDark
                                          : ThemeColors.primaryDark,
                                  borderRadius: 15,
                                  onPressed: () {
                                    remakePayment();
                                  },
                                ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              } else {
                return Container();
              }
            }
            return Container();
          }),
    );
  }

  Widget orderInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Tracking Number",
          style: TextStyle(
            color: ThemeColors.dark,
            fontSize: h2,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: spacingHeightSmall),
        Text(
          order!.number,
          style: TextStyle(
            color: ThemeColors.dark,
            fontSize: h3,
          ),
        ),
        SizedBox(height: spacingHeightMedium),
        Text(
          "Payment Method",
          style: TextStyle(
            color: ThemeColors.dark,
            fontSize: h2,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: spacingHeightSmall),
        Text(
          order!.paymentMethod != null ? order!.paymentMethod!.name : '-',
          style: TextStyle(
            color: ThemeColors.dark,
            fontSize: h3,
          ),
        ),
        SizedBox(height: spacingHeightSmall),
        Divider(
          color: ThemeColors.disabled,
        ),
        SizedBox(height: spacingHeightSmall),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Order placed",
                  style: TextStyle(
                    color: ThemeColors.dark,
                    fontSize: h2,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: spacingHeightSmall),
                Text(
                  DateFormat('HH:mm, d MMM yyyy').format(order!.createdAt),
                  style: TextStyle(
                    color: ThemeColors.dark,
                    fontSize: h3,
                  ),
                ),
              ],
            ),
            if (order!.pickupAt != null)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Pickup Time",
                    style: TextStyle(
                      color: ThemeColors.dark,
                      fontSize: h2,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: spacingHeightSmall),
                  Text(
                    DateFormat('HH:mm, d MMM yyyy').format(order!.pickupAt!),
                    style: TextStyle(
                      color: ThemeColors.dark,
                      fontSize: h3,
                    ),
                  ),
                ],
              )
          ],
        ),
        EasyStepper.EasyStepper(
          activeStep: activeStep,
          lineLength: phoneWidth / 2.5,
          lineSpace: 0,
          lineType: EasyStepper.LineType.normal,
          defaultLineColor: ThemeColors.disabled,
          finishedLineColor: important_variables.projectName == "obriens"
              ? ThemeColors.secondaryDark
              : Colors.green[300],
          finishedStepTextColor: ThemeColors.dark,
          activeStepTextColor: ThemeColors.dark,
          internalPadding: 0,
          showLoadingAnimation: false,
          stepRadius: 20,
          showStepBorder: false,
          lineThickness: 2.5,
          steps: [
            EasyStepper.EasyStep(
              customStep: CircleAvatar(
                backgroundColor: activeStep >= 0
                    ? important_variables.projectName == "obriens"
                        ? ThemeColors.secondaryDark
                        : Colors.green[300]
                    : ThemeColors.disabled,
                child: ImageIcon(
                  AssetImage(
                      "assets/icons/${boxGlobal.get("iconSet")}/check-broken.png"),
                  color: ThemeColors.light,
                ),
              ),
              title: 'Pending',
            ),
            EasyStepper.EasyStep(
              customStep: CircleAvatar(
                backgroundColor: activeStep >= 1
                    ? important_variables.projectName == "obriens"
                        ? ThemeColors.secondaryDark
                        : Colors.green[300]
                    : ThemeColors.disabled,
                child: ImageIcon(
                  AssetImage(
                      "assets/icons/${boxGlobal.get("iconSet")}/check-broken.png"),
                  color: ThemeColors.light,
                ),
              ),
              title: 'Paid',
            ),
          ],
        ),
        Container(),
        Center(
          child: Text(
            "Your order is ${order!.status.toLowerCase()}.",
            style: TextStyle(
              color: ThemeColors.dark,
              fontSize: h3,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget bundle(int index) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: List.generate(order!.items![index].bundleItems!.length,
          (bundleIndex) {
        return Text(
          order!.items![index].bundleItems![bundleIndex].product.name,
          style: TextStyle(color: ThemeColors.dark, fontSize: h4),
        );
      }),
    );
  }

  Widget modifier(int index) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: List.generate(order!.items![index].modifierItems!.length,
          (modifierIndex) {
        return Text(
          order!.items![index].modifierItems![modifierIndex].name,
          style: TextStyle(color: ThemeColors.dark, fontSize: h4),
        );
      }),
    );
  }

  Widget remark() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: spacingHeightSmall),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Remark :",
            style: TextStyle(
              color: ThemeColors.dark,
              fontSize: h3,
              // fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            order!.remark!,
            style: TextStyle(
              color: ThemeColors.gray,
              fontSize: h4,
            ),
          ),
        ],
      ),
    );
  }

  void remakePayment() async {
    EasyLoading.show();

    ApiPost apiPost = ApiPost();
    Map? data = await apiPost.remakePayment(order!.id);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        String paymentUrl = data['data']['payment_url'] ?? "";
        // todo backend return eghl_request: []
        EghlPayment? eghl;
        try {
          eghl = data['data']['eghl_request'] == null
              ? null
              : EghlPayment.fromJson(data['data']['eghl_request']);
        } catch (e) {
          print(e.toString());
        }

        if (paymentUrl != "") {
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (BuildContext context) => WebView(
                  url: data['data']['payment_url'],
                ),
              )).then((value) async {
            setState(() {
              future = getOrderDetail();
            });
          });
        } else if (eghl != null) {
          EghlPayment eghl = EghlPayment.fromJson(data['data']['eghl_request']);
          if (Platform.isIOS) {
            try {
              await OrderHistoryDetailTemplate1.iosPlatform
                  .invokeMethod('eGHL', <String, dynamic>{
                'transactionType': eghl.transactionType ?? "",
                'paymentMethod': eghl.paymentMethod ?? "",
                'serviceId': eghl.serviceId ?? "",
                'merchantPass': eghl.merchantPass ?? "",
                'paymentId': eghl.paymentId ?? "",
                'orderNumber': eghl.orderNumber ?? "",
                'paymentDesc': eghl.paymentDesc ?? "",
                'merchantName': eghl.merchantName ?? "",
                'merchantCallBackUrl': eghl.merchantCallBackUrl ?? "",
                'amount': eghl.amount ?? "",
                'currencyCode': eghl.currencyCode ?? "",
                'custIp': eghl.custIp ?? "",
                'custName': eghl.custName ?? "",
                'custEmail': eghl.custEmail ?? "",
                'custPhone': eghl.custPhone ?? "",
                'languageCode': eghl.languageCode ?? "",
                'paymentGateway': eghl.paymentGateway ?? "",
              });
            } catch (e) {
              print(e);
            }
          } else {
            try {
              await OrderHistoryDetailTemplate1.androidPlatform
                  .invokeMethod('eGHL', <String, dynamic>{
                'transactionType': eghl.transactionType ?? "",
                'paymentMethod': eghl.paymentMethod ?? "",
                'serviceId': eghl.serviceId ?? "",
                'merchantPass': eghl.merchantPass ?? "",
                'paymentId': eghl.paymentId ?? "",
                'orderNumber': eghl.orderNumber ?? "",
                'paymentDesc': eghl.paymentDesc ?? "",
                'merchantName': eghl.merchantName ?? "",
                'merchantCallBackUrl': eghl.merchantCallBackUrl ?? "",
                'amount': eghl.amount ?? "",
                'currencyCode': eghl.currencyCode ?? "",
                'custIp': eghl.custIp ?? "",
                'custName': eghl.custName ?? "",
                'custEmail': eghl.custEmail ?? "",
                'custPhone': eghl.custPhone ?? "",
                'languageCode': eghl.languageCode ?? "",
                'paymentGateway': eghl.paymentGateway ?? "",
              });
            } catch (e) {
              print(e);
            }
          }
          setState(() {
            future = getOrderDetail();
          });
        } else {
          setState(() {
            future = getOrderDetail();
          });
        }
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  Future<Map?> getOrderDetail() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.orderDetail(widget.id);

    if (data != null && data['data'] != null) {
      order = Order.fromJson(data['data']['order']);
      if (order != null) {
        if (order!.status == "PENDING") {
          activeStep = 0;
        } else if ((order!.status == "PAID")) {
          activeStep = 1;
        }
      }
    }
    return data;
  }
}
