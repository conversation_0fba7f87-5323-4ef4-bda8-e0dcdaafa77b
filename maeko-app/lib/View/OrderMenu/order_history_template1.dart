import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/order.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/OrderMenu/order_history_detail_page.dart';
import 'package:amverton/View/Custom%20Widgets/empty_list.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:intl/intl.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class OrderHistoryTemplate1 extends StatefulWidget {
  static String routeName = "/order_history/1";
  final dynamic args;

  const OrderHistoryTemplate1({Key? key, this.args}) : super(key: key);

  @override
  _OrderHistoryTemplate1State createState() => _OrderHistoryTemplate1State();
}

class _OrderHistoryTemplate1State extends State<OrderHistoryTemplate1> {
  late Box boxGlobal;
  late Future boxFuture;
  String theme = "L";

  final ScrollController scrollController = ScrollController();
  bool hasMorePages = false;
  int nextPage = 0;
  StreamController streamController = StreamController();
  List<Order> orderList = [];

  @override
  void initState() {
    super.initState();
    theme = widget.args["theme"];
    boxFuture = openBox();
    getOrder();

    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (hasMorePages) getOrder();
      }
    });
  }

  openBox() async {
    boxGlobal = await Hive.openBox("boxGlobal");
    return await Hive.openBox<Asset>('ACCOUNT+ORDER_HISTORY+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return Scaffold(
              appBar: AppBar(
                title: Text(
                  list.values
                          .firstWhereOrNull(
                              (element) => element.name == "TITLE")
                          ?.data ??
                      "",
                  style: TextStyle(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                ),
                iconTheme: IconThemeData(
                  color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                ),
              ),
              body: Container(
                decoration: background == null
                    ? null
                    : BoxDecoration(
                        image: backgroundType == "IMAGE"
                            ? DecorationImage(
                                image: CachedNetworkImageProvider(
                                    background.data ?? ""),
                                fit: BoxFit.cover,
                              )
                            : null,
                        color: backgroundType == "COLOR"
                            ? Color(
                                int.parse("0xff${background.data ?? "FFFFFF"}"))
                            : null,
                      ),
                height: phoneHeight,
                child: RefreshIndicator(
                  edgeOffset: verticalPaddingMedium,
                  color: ThemeColors.primaryDark,
                  onRefresh: () {
                    return Future.delayed(const Duration(seconds: 1), () {
                      setState(() {
                        hasMorePages = false;
                        nextPage = 0;
                        orderList.clear();
                        getOrder();
                      });
                    });
                  },
                  child: StreamBuilder(
                      stream: streamController.stream,
                      builder: (BuildContext context, AsyncSnapshot snapshot) {
                        if (snapshot.hasError) {
                          return Container();
                        } else {
                          if (orderList.isEmpty) {
                            return EmptyList(theme: theme);
                          } else {
                            return ListView.builder(
                              physics: AlwaysScrollableScrollPhysics(),
                              padding: EdgeInsets.all(defaultPadding),
                              itemCount: orderList.length,
                              itemBuilder: (BuildContext context, int index) {
                                return GestureDetector(
                                  onTap: () {
                                    Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (BuildContext context) =>
                                                OrderHistoryDetailPage(
                                                  id: orderList[index].id,
                                                )));
                                  },
                                  child: Container(
                                    margin: EdgeInsets.only(
                                        bottom: defaultInnerPadding),
                                    padding: EdgeInsets.symmetric(
                                      vertical: defaultPadding,
                                      horizontal: defaultInnerPadding,
                                    ),
                                    width: phoneWidth,
                                    decoration: BoxDecoration(
                                      color: ThemeColors.light,
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(
                                          color: ThemeColors.disabled),
                                      boxShadow: [
                                        BoxShadow(
                                          color: ThemeColors.disabled,
                                          offset: const Offset(0.0, 5.0),
                                          blurRadius: 5.0,
                                          spreadRadius: 0.5,
                                        )
                                      ],
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          DateFormat('yyyy-MM-dd h:mm a')
                                              .format(
                                                  orderList[index].createdAt),
                                          style: TextStyle(
                                            color: ThemeColors.dark,
                                            fontSize: h3,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        SizedBox(height: spacingHeightSmall),
                                        detailList(
                                            "Total",
                                            important_variables.currency +
                                                orderList[index].total),
                                        detailList(
                                            "Status", orderList[index].status),
                                        detailList("Payment Status",
                                            orderList[index].paymentStatus),
                                        SizedBox(height: spacingHeightSmall),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            );
                          }
                        }
                      }),
                ),
              ),
            );
          }
          return Container();
        });
  }

  Widget detailList(String title, String data) {
    return Row(
      children: [
        Container(
          width: phoneWidth / 3,
          child: Text(
            "$title:",
            style: TextStyle(
              color: ThemeColors.gray,
              fontSize: h3,
            ),
          ),
        ),
        SizedBox(width: spacingWidth),
        Expanded(
          child: Text(
            data,
            style: TextStyle(
              color: ThemeColors.dark,
              fontSize: h4,
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  Future<Map?> getOrder() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.orderList(nextPage);

    if (data != null && data['data'] != null) {
      List<Order> newList = (data['data']['orders'] as List)
          .map((data) => Order.fromJson(data))
          .toList();

      hasMorePages = data["data"]["has_more_pages"];
      if (hasMorePages) {
        nextPage = data["data"]["next_page"] ?? 0;
      }
      orderList.addAll(newList);
      streamController.add(data);
    }
    return data;
  }
}
