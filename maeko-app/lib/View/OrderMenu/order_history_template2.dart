import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/order.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Custom%20Widgets/default_divider2.dart';
import 'package:amverton/View/OrderMenu/order_history_detail_page.dart';
import 'package:amverton/View/Custom%20Widgets/empty_list.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:intl/intl.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;
import 'package:rxdart/subjects.dart';

class OrderHistoryTemplate2 extends StatefulWidget {
  static String routeName = "/order_history/2";
  final dynamic args;

  const OrderHistoryTemplate2({Key? key, this.args}) : super(key: key);

  @override
  _OrderHistoryTemplate2State createState() => _OrderHistoryTemplate2State();
}

class _OrderHistoryTemplate2State extends State<OrderHistoryTemplate2> {
  late Box boxGlobal;
  late Future boxFuture;
  String theme = "L";

  final ScrollController activeScrollController = ScrollController();
  bool activeHasMorePages = false;
  int activeNextPage = 0;
  StreamController activeStreamController = BehaviorSubject();
  List<Order> activeOrderList = [];

  final ScrollController inactiveScrollController = ScrollController();
  bool inactiveHasMorePages = false;
  int inactiveNextPage = 0;
  StreamController inactiveStreamController = BehaviorSubject();
  List<Order> inactiveOrderList = [];

  @override
  void initState() {
    super.initState();
    theme = widget.args["theme"];
    boxFuture = openBox();
    getOrder("active", activeNextPage);
    getOrder("inactive", inactiveNextPage);

    activeScrollController.addListener(() {
      if (activeScrollController.position.pixels ==
          activeScrollController.position.maxScrollExtent) {
        if (activeHasMorePages) getOrder("active", activeNextPage);
        ;
      }
    });

    inactiveScrollController.addListener(() {
      if (inactiveScrollController.position.pixels ==
          inactiveScrollController.position.maxScrollExtent) {
        if (inactiveHasMorePages) getOrder("inactive", inactiveNextPage);
      }
    });
  }

  openBox() async {
    boxGlobal = await Hive.openBox("boxGlobal");
    return await Hive.openBox<Asset>('ACCOUNT+ORDER_HISTORY+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return DefaultTabController(
              length: 2,
              child: Scaffold(
                appBar: AppBar(
                  title: Text(
                    list.values
                            .firstWhereOrNull(
                                (element) => element.name == "TITLE")
                            ?.data ??
                        "",
                    style: TextStyle(
                      color:
                          theme == "L" ? ThemeColors.dark : ThemeColors.light,
                    ),
                  ),
                  iconTheme: IconThemeData(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                ),
                body: Container(
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  child: Column(
                    children: [
                      TabBar(
                        padding: EdgeInsets.symmetric(
                            horizontal: defaultPadding + 10),
                        labelColor: ThemeColors.primaryDark,
                        indicator: BoxDecoration(
                          color: ThemeColors.primaryDark,
                        ),
                        indicatorWeight: 1,
                        indicatorPadding: EdgeInsets.only(top: 40),
                        unselectedLabelColor: ThemeColors.gray,
                        tabs: const [
                          Tab(text: "Active"),
                          Tab(text: "Past"),
                        ],
                      ),
                      Expanded(
                        child: TabBarView(
                          children: [
                            active(background, backgroundType),
                            inactive(background, backgroundType),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }
          return Container();
        });
  }

  Widget active(background, backgroundType) {
    return Container(
      decoration: background == null
          ? null
          : BoxDecoration(
              image: backgroundType == "IMAGE"
                  ? DecorationImage(
                      image: CachedNetworkImageProvider(background.data ?? ""),
                      fit: BoxFit.cover,
                    )
                  : null,
              color: backgroundType == "COLOR"
                  ? Color(int.parse("0xff${background.data ?? "FFFFFF"}"))
                  : null,
            ),
      height: phoneHeight,
      child: RefreshIndicator(
        edgeOffset: verticalPaddingMedium,
        color: ThemeColors.primaryDark,
        onRefresh: () {
          return Future.delayed(const Duration(seconds: 1), () {
            setState(() {
              activeHasMorePages = false;
              activeNextPage = 0;
              activeOrderList.clear();
              getOrder("active", activeNextPage);
            });
          });
        },
        child: StreamBuilder(
            stream: activeStreamController.stream,
            builder: (BuildContext context, AsyncSnapshot snapshot) {
              if (snapshot.hasError) {
                return Container();
              } else {
                if (activeOrderList.isEmpty) {
                  return EmptyList(theme: theme);
                } else {
                  return ListView.builder(
                    physics: AlwaysScrollableScrollPhysics(),
                    padding: EdgeInsets.all(defaultPadding),
                    itemCount: activeOrderList.length,
                    itemBuilder: (BuildContext context, int index) {
                      Order order = activeOrderList[index];
                      return GestureDetector(
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (BuildContext context) =>
                                      OrderHistoryDetailPage(
                                        id: activeOrderList[index].id,
                                      )));
                        },
                        child: orderCard(order, true),
                      );
                    },
                  );
                }
              }
            }),
      ),
    );
  }

  Widget inactive(background, backgroundType) {
    return RefreshIndicator(
      edgeOffset: verticalPaddingMedium,
      color: ThemeColors.primaryDark,
      onRefresh: () {
        return Future.delayed(const Duration(seconds: 1), () {
          setState(() {
            inactiveHasMorePages = false;
            inactiveNextPage = 0;
            inactiveOrderList.clear();
            getOrder("inactive", inactiveNextPage);
          });
        });
      },
      child: StreamBuilder(
          stream: inactiveStreamController.stream,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            if (snapshot.hasError) {
              return Container();
            } else {
              if (inactiveOrderList.isEmpty) {
                return EmptyList(theme: theme);
              } else {
                return ListView.builder(
                  physics: AlwaysScrollableScrollPhysics(),
                  padding: EdgeInsets.all(defaultPadding),
                  itemCount: inactiveOrderList.length,
                  itemBuilder: (BuildContext context, int index) {
                    Order order = inactiveOrderList[index];
                    return GestureDetector(
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (BuildContext context) =>
                                    OrderHistoryDetailPage(
                                      id: inactiveOrderList[index].id,
                                    )));
                      },
                      child: orderCard(order, false),
                    );
                  },
                );
              }
            }
          }),
    );
  }

  Container orderCard(Order order, bool active) {
    return Container(
      margin: EdgeInsets.only(bottom: defaultInnerPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: kElevationToShadow[3],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            decoration: BoxDecoration(
              color: active ? ThemeColors.primaryLight : ThemeColors.disabled,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            padding: EdgeInsets.symmetric(
              horizontal: defaultPadding,
              vertical: defaultInnerPadding,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  active ? 'Active order' : 'Past order',
                  style: TextStyle(
                    fontSize: h2,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.only(
              bottom: defaultPadding,
              left: defaultInnerPadding,
              right: defaultInnerPadding,
            ),
            child: Column(
              children: [
                SizedBox(height: spacingHeightMedium),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Row(
                              children: [
                                Text(
                                  'ID: ',
                                  style: TextStyle(
                                    fontSize: h3,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  order.number.toString(),
                                  style: TextStyle(
                                    fontSize: h3,
                                    fontWeight: FontWeight.bold,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        SizedBox(height: spacingHeightSmall),
                        Text(
                          DateFormat('yyyy MMM dd  HH:mm')
                              .format(order.createdAt),
                          style: TextStyle(
                            fontSize: h3,
                          ),
                        ),
                      ],
                    ),
                    IgnorePointer(
                      child: ElevatedButton(
                        onPressed: () {},
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ThemeColors.secondaryDark,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                        child: Text(
                          'View',
                          style: TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                Divider(height: 20, thickness: 1),
                Container(
                  margin: EdgeInsets.symmetric(horizontal: defaultPadding),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.store, color: Colors.black54),
                          SizedBox(
                            width: defaultInnerPadding,
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                order.pickupAt != null
                                    ? "Pick Up At:"
                                    : "Delivery From:",
                                style: TextStyle(
                                  fontSize: h3,
                                ),
                              ),
                              SizedBox(
                                height: spacingHeightSmall,
                              ),
                              Text(
                                order.outlet!.name,
                                style: TextStyle(
                                  fontSize: h3,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Price",
                            style: TextStyle(
                              fontSize: h3,
                            ),
                          ),
                          SizedBox(
                            height: spacingHeightSmall,
                          ),
                          Text(
                            "RM ${order.total}",
                            style: TextStyle(
                              fontSize: h3,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget detailList(String title, String data) {
    return Row(
      children: [
        Container(
          width: phoneWidth / 3,
          child: Text(
            "$title:",
            style: TextStyle(
              color: ThemeColors.gray,
              fontSize: h3,
            ),
          ),
        ),
        SizedBox(width: spacingWidth),
        Expanded(
          child: Text(
            data,
            style: TextStyle(
              color: ThemeColors.dark,
              fontSize: h4,
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  Future<Map?> getOrder(String filter, int nextPage) async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.orderList(nextPage, filter);

    if (data != null && data['data'] != null) {
      List<Order> newList = (data['data']['orders'] as List)
          .map((data) => Order.fromJson(data))
          .toList();

      if (filter == "active") {
        activeHasMorePages = data["data"]["has_more_pages"];
        if (activeHasMorePages) {
          nextPage = data["data"]["next_page"] ?? 0;
        }
        activeOrderList.addAll(newList);
        activeStreamController.add(data);
      }

      if (filter == "inactive") {
        inactiveHasMorePages = data["data"]["has_more_pages"];
        if (inactiveHasMorePages) {
          nextPage = data["data"]["next_page"] ?? 0;
        }
        inactiveOrderList.addAll(newList);
        inactiveStreamController.add(data);
      }
    }
    return data;
  }
}
