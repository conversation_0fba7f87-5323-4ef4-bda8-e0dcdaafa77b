import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/address.dart';
import 'package:amverton/Model/app_settings.dart';
import 'package:amverton/Model/bottom_bar.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/OrderMenu/deliver_address_option.dart';
import 'package:amverton/View/OrderMenu/order_menu_outlet.dart';
import 'package:amverton/main.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class OrderTemplate1 extends StatefulWidget {
  final String theme;

  const OrderTemplate1({Key? key, required this.theme}) : super(key: key);

  @override
  State<OrderTemplate1> createState() => _OrderTemplate1State();
}

class _OrderTemplate1State extends State<OrderTemplate1> {
  late Future future;
  late Box box;
  late Box boxGlobal;
  AppSettings? appSettings;

  late Box boxMenu;
  List<BottomBar> bottomBarList = [];

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    appSettings = boxGlobal.get("appSettings") ?? null;

    boxMenu = await Hive.openBox('boxMenu');
    bottomBarList = List<BottomBar>.from(boxGlobal.get("bottomBar")) ?? [];
    return await Hive.openBox<Asset>('HOME+ORDER+3');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            bool isLogin = box.get("is_login") ?? false;
            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            if (!list.isNotEmpty) {
              return Container(
                decoration: background == null
                    ? null
                    : BoxDecoration(
                        image: backgroundType == "IMAGE"
                            ? DecorationImage(
                                image: CachedNetworkImageProvider(
                                    background.data ?? ""),
                                fit: BoxFit.cover,
                              )
                            : null,
                        color: backgroundType == "COLOR"
                            ? Color(
                                int.parse("0xff${background.data ?? "FFFFFF"}"))
                            : null,
                      ),
                child: Container(
                  margin: EdgeInsets.all(defaultPadding),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      items(context, isLogin, "golf",
                          "assets/icons/home/<USER>", "Golf"),
                      items(context, isLogin, "hotel",
                          "assets/icons/home/<USER>", "Hotel"),
                      items(context, isLogin, "/order_history/2",
                          "assets/icons/home/<USER>", "My Booking"),
                    ],
                  ),
                ),
              );
            }
          }
          return Container();
        });
  }

  GestureDetector items(BuildContext context, bool isLogin, String pushnamed,
      String iconPath, String title) {
    return GestureDetector(
      onTap: () {
        if (pushnamed == "golf" || pushnamed == "hotel") {
          if (bottomBarList.isNotEmpty) {
            int orderIndex = bottomBarList
                .indexWhere((element) => element.name == "BOOKINGS");
            if (orderIndex != -1) {
              final dynamic navigationBar = barGlobalKey.currentWidget;
              navigationBar.onTap(orderIndex);
            }
          }
          return;
        }

        if (isLogin) {
          Navigator.pushNamed(
              context,
              //todo get from asset
              // "/order_history/${accountAsset.firstWhereOrNull((element) => element.sectionName == "ORDER_HISTORY")?.template ?? ""}",
              pushnamed,
              arguments: {
                "theme": accountAsset
                        .firstWhereOrNull(
                            (element) => element.sectionName == "ORDER_HISTORY")
                        ?.theme ??
                    "L",
              });
        } else {
          Navigator.of(context, rootNavigator: true)
              .pushNamed("/login/${loginAsset.template}");
        }
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          Positioned(
            bottom: 0,
            child: Container(
              width: phoneWidth / 4.0,
              height: phoneWidth / 3.5,
              decoration: BoxDecoration(
                  color: Color(0xffFFF8DF),
                  boxShadow: kElevationToShadow[1],
                  borderRadius: BorderRadius.circular(12)),
            ),
          ),
          Container(
            width: phoneWidth / 4.0,
            height: phoneWidth / 3.5,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  iconPath,
                  height: phoneWidth / 7,
                  width: phoneWidth / 7,
                  fit: BoxFit.cover,
                ),
                SizedBox(height: spacingHeightSmall),
                Text(
                  title,
                  style: TextStyle(
                    color: widget.theme == "L"
                        ? ThemeColors.dark
                        : ThemeColors.light,
                    fontSize: h4,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
