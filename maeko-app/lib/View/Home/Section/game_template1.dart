import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/home.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Provider/providers.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Home/web_view.dart';
import 'package:amverton/View/Home/home_page.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class GameTemplate1 extends ConsumerStatefulWidget {
  final String theme;

  const GameTemplate1({Key? key, required this.theme}) : super(key: key);

  @override
  _GameTemplate1State createState() => _GameTemplate1State();
}

class _GameTemplate1State extends ConsumerState<GameTemplate1> {
  late Future future;
  late Box box;
  late Box boxGlobal;

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    await Hive.openBox('box');
    await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('HOME+GAMIFICATION+1');
  }

  @override
  Widget build(BuildContext context) {
    Home? home = ref.watch(homeProvider);

    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();
            box = Hive.box('box');
            boxGlobal = Hive.box('boxGlobal');

            bool isLogin = box.get("is_login") ?? false;
            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            if (list.isNotEmpty) {
              return GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  if (isLogin) {
                    // if (home?.gamification?.url != null) {
                    //   Navigator.push(
                    //       context,
                    //       MaterialPageRoute(
                    //           builder: (BuildContext context) =>
                    //               WebView(url: home!.gamification!.url!)));
                    // }
                  } else {
                    Navigator.of(context, rootNavigator: true)
                        .pushNamed("/login/${loginAsset.template}");
                  }
                },
                child: Container(
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  child: Container(
                    margin: EdgeInsets.all(defaultPadding),
                    width: phoneWidth,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      color: ThemeColors.light,
                    ),
                    child: Column(
                      children: [
                        ClipRRect(
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20),
                            bottomLeft: Radius.circular(20),
                            bottomRight: Radius.circular(20),
                          ),
                          child: CachedNetworkImage(
                            imageUrl: list.values
                                    .firstWhereOrNull(
                                        (element) => element.name == "BANNER")
                                    ?.data ??
                                "",
                            width: phoneWidth,
                            fit: BoxFit.fitWidth,
                            placeholder: (context, url) {
                              return Padding(
                                padding: EdgeInsets.only(
                                    top: verticalPaddingSmall,
                                    bottom: defaultPadding),
                                child: Center(
                                  child: CircularProgressIndicator(
                                    color: ThemeColors.primaryDark,
                                  ),
                                ),
                              );
                            },
                            errorWidget: (context, url, error) {
                              return Padding(
                                padding:
                                    EdgeInsets.only(top: verticalPaddingSmall),
                                child: Icon(
                                  Icons.error_outline_outlined,
                                  color: ThemeColors.primaryDark,
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }
          }
          return Container();
        });
  }
}
