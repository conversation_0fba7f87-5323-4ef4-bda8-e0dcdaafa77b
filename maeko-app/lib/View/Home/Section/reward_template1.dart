import 'package:amverton/Constant/theme.dart';
import 'package:amverton/Model/bottom_bar.dart';
import 'package:amverton/main.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class RewardTemplate1 extends StatefulWidget {
  final String theme;

  const RewardTemplate1({Key? key, required this.theme}) : super(key: key);

  @override
  State<RewardTemplate1> createState() => _RewardTemplate1State();
}

class _RewardTemplate1State extends State<RewardTemplate1> {
  late Future future;
  late Box box;
  late Box boxGlobal;
  late Box boxMenu;

  late List<BottomBar> bottomBarList;

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    boxMenu = await Hive.openBox('boxMenu');
    bottomBarList = List<BottomBar>.from(boxGlobal.get("bottomBar")) ?? [];
    return await Hive.openBox<Asset>('HOME+REWARDS+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            bool isLogin = box.get("is_login") ?? false;
            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            String title = list.values
                .firstWhereOrNull((element) => element.name == "TITLE")
                ?.data;

            String details = list.values
                .firstWhereOrNull((element) => element.name == "DESCRIPTION")
                ?.data;

            String buttonText = list.values
                .firstWhereOrNull((element) => element.name == "BUTTON_TEXT")
                ?.data;

            String? image = list.values
                .firstWhereOrNull(
                    (element) => element.name == "REWARD_BACKGROUND")
                ?.data;

            if (list.isNotEmpty) {
              return Container(
                decoration: background == null
                    ? null
                    : BoxDecoration(
                        image: backgroundType == "IMAGE"
                            ? DecorationImage(
                                image: CachedNetworkImageProvider(
                                    background.data ?? ""),
                                fit: BoxFit.cover,
                              )
                            : null,
                        color: backgroundType == "COLOR"
                            ? Color(
                                int.parse("0xff${background.data ?? "FFFFFF"}"))
                            : null,
                      ),
                child: GestureDetector(
                  onTap: () {
                    if (bottomBarList.isNotEmpty) {
                      int orderIndex = bottomBarList
                          .indexWhere((element) => element.name == "REWARDS");
                      if (orderIndex != -1) {
                        final dynamic navigationBar =
                            barGlobalKey.currentWidget;
                        navigationBar.onTap(orderIndex);
                      }
                    }
                  },
                  child: Container(
                    margin: EdgeInsets.all(defaultPadding),
                    width: phoneWidth,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      color: ThemeColors.light,
                      boxShadow: [
                        BoxShadow(
                          color: ThemeColors.disabled,
                          offset: const Offset(0.0, 5.0),
                          blurRadius: 5.0,
                          spreadRadius: 2.0,
                        )
                      ],
                    ),
                    child: IntrinsicHeight(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Expanded(
                            flex: 2,
                            child: Container(
                              padding: EdgeInsets.all(defaultPadding),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    title,
                                    style: TextStyle(
                                      fontSize: h1,
                                      fontWeight: FontWeight.bold,
                                      fontFamily: fontFamily1,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    details,
                                    style: TextStyle(
                                      fontSize: h5,
                                      color: Colors.black54,
                                    ),
                                  ),
                                  SizedBox(height: defaultPadding),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                    decoration: BoxDecoration(
                                      color: ThemeColors.secondaryLight,
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          buttonText,
                                          style: TextStyle(
                                            fontSize: h5,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                          ),
                                        ),
                                        SizedBox(
                                          width: 8,
                                        ),
                                        Icon(
                                          Icons.arrow_forward_ios,
                                          color: Colors.white,
                                          size: 10,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(width: defaultPadding),
                          if (image != null)
                            Expanded(
                              flex: 1,
                              child: Container(
                                child: ClipRRect(
                                  borderRadius: const BorderRadius.only(
                                    topRight: Radius.circular(20),
                                    bottomRight: Radius.circular(20),
                                  ),
                                  child: CachedNetworkImage(
                                    imageUrl: image,
                                    fit: BoxFit.cover,
                                    placeholder: (context, url) {
                                      return Center(
                                        child: CircularProgressIndicator(
                                          color: ThemeColors.primaryDark,
                                        ),
                                      );
                                    },
                                    errorWidget: (context, url, error) {
                                      return Icon(
                                        Icons.error_outline_outlined,
                                        color: ThemeColors.primaryDark,
                                      );
                                    },
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            }
          }
          return Container();
        });
  }
}
