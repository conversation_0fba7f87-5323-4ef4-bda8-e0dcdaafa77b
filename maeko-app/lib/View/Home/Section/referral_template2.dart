import 'package:amverton/Constant/theme.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class ReferralTemplate2 extends StatefulWidget {
  final String theme;

  const ReferralTemplate2({Key? key, required this.theme}) : super(key: key);

  @override
  State<ReferralTemplate2> createState() => _ReferralTemplate2State();
}

class _ReferralTemplate2State extends State<ReferralTemplate2> {
  late Future future;
  late Box box;
  late Box boxGlobal;

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('HOME+REFERRAL+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            bool isLogin = box.get("is_login") ?? false;
            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            String title = list.values
                .firstWhereOrNull(
                    (element) => element.name == "INVITE_FRIEND_TEXT")
                ?.data;

            String details = list.values
                .firstWhereOrNull((element) => element.name == "DESCRIPTION")
                ?.data;

            String buttonText = list.values
                .firstWhereOrNull((element) => element.name == "BUTTON_TEXT")
                ?.data;

            String? image = list.values
                .firstWhereOrNull(
                    (element) => element.name == "INVITE_FRIEND_BACKGROUND")
                ?.data;

            if (list.isNotEmpty) {
              return Container(
                decoration: background == null
                    ? null
                    : BoxDecoration(
                        image: backgroundType == "IMAGE"
                            ? DecorationImage(
                                image: CachedNetworkImageProvider(
                                    background.data ?? ""),
                                fit: BoxFit.cover,
                              )
                            : null,
                        color: backgroundType == "COLOR"
                            ? Color(
                                int.parse("0xff${background.data ?? "FFFFFF"}"))
                            : null,
                      ),
                child: GestureDetector(
                  onTap: () {
                    if (isLogin) {
                      Navigator.pushNamed(context,
                          "/referral/2",
                          arguments: {
                            "theme": "L",
                          });
                    } else {
                      Navigator.of(context, rootNavigator: true)
                          .pushNamed("/login/1");
                    }
                  },
                  child: Container(
                    margin: EdgeInsets.all(defaultPadding),
                    width: phoneWidth,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      color: ThemeColors.light,
                      boxShadow: [
                        BoxShadow(
                          color: ThemeColors.disabled,
                          offset: const Offset(0.0, 5.0),
                          blurRadius: 5.0,
                          spreadRadius: 2.0,
                        )
                      ],
                    ),
                    child: IntrinsicHeight(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Expanded(
                            flex: 2,
                            child: Container(
                              padding: EdgeInsets.all(defaultPadding),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    title,
                                    style: TextStyle(
                                      fontSize: h2,
                                      fontWeight: FontWeight.bold,
                                      fontFamily: fontFamily2,
                                      color: ThemeColors.primaryDark,
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    details,
                                    style: TextStyle(
                                      fontSize: h5,
                                      color:  ThemeColors.primaryDark,
                                       fontFamily: fontFamily2,
                                    ),
                                  ),
                                  SizedBox(height: defaultPadding),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                    decoration: BoxDecoration(
                                     // color: ThemeColors.secondaryLight,
                                        gradient: LinearGradient(
                                          colors: [
                                            Color(0xFF818592),
                                            Color(0xFF595C68),
                                          ],
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                        ),
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          'Invites',
                                          style: TextStyle(
                                            fontSize: h5,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                          ),
                                        ),
                                        SizedBox(
                                          width: 8,
                                        ),
                                        Icon(
                                          Icons.arrow_forward_ios,
                                          color: Colors.white,
                                          size: 10,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(width: defaultPadding),
                          if (image != null)
                            Expanded(
                              flex: 1,
                              child: ClipRRect(
                                borderRadius: const BorderRadius.only(
                                  topRight: Radius.circular(20),
                                  bottomRight: Radius.circular(20),
                                ),
                                child: Container(
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: AssetImage("assets/images/InviteFriend.jpg"),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            }
          }
          return Container();
        });
  }
}
