import 'package:amverton/Constant/theme.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/home.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Provider/providers.dart';
import 'package:hive/hive.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:collection/collection.dart';

class WelcomeTemplate1 extends ConsumerStatefulWidget {
  final String theme;

  const WelcomeTemplate1({Key? key, required this.theme}) : super(key: key);

  @override
  _WelcomeTemplate1State createState() => _WelcomeTemplate1State();
}

class _WelcomeTemplate1State extends ConsumerState<WelcomeTemplate1> {
  late Future future;
  late Box box;
  late Box boxGlobal;
  late bool isLogin;

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    var homeWelcomeBox = await Hive.openBox<Asset>('HOME+WELCOME+1');

    isLogin = box.get("is_login") ?? false;
    return homeWelcomeBox;
  }

  @override
  Widget build(BuildContext context) {
    Home? home = ref.watch(homeProvider);

    return FutureBuilder(
      future: future,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          final Box list = snapshot.data;
          list.watch();

          String? backgroundType = list.values
                  .firstWhereOrNull(
                      (element) => element.name == "BACKGROUND_TYPE")
                  ?.data ??
              "";
          Asset? background = list.values.firstWhereOrNull(
              (element) => element.name == "BACKGROUND_$backgroundType");

          if (list.isNotEmpty) {
            return Container(
              width: phoneWidth,
              padding: EdgeInsets.all(defaultInnerPadding),
              decoration: background == null
                  ? null
                  : BoxDecoration(
                      image: backgroundType == "IMAGE"
                          ? DecorationImage(
                              image: CachedNetworkImageProvider(
                                  background.data ?? ""),
                              fit: BoxFit.cover,
                            )
                          : null,
                      color: backgroundType == "COLOR"
                          ? Color(
                              int.parse("0xff${background.data ?? "FFFFFF"}"))
                          : null),
              // child: Text(
              //   () {
              //     if (isLogin) {
              //       String memberTitle = list.values
              //               .firstWhereOrNull(
              //                   (element) => element.name == "MEMBER_TITLE")
              //               ?.data ??
              //           "";
              //       return memberTitle.replaceFirst(
              //           RegExp(':name'), home?.me.name ?? "");
              //     } else {
              //       return list.values
              //               .firstWhereOrNull(
              //                   (element) => element.name == "GUEST_TITLE")
              //               ?.data ??
              //           "";
              //     }
              //   }(),
              //   textAlign: TextAlign.center,
              //   style: TextStyle(
              //     color: widget.theme == "L"
              //         ? ThemeColors.dark
              //         : ThemeColors.light,
              //     fontSize: h1,
              //     fontWeight: FontWeight.bold,
              //     fontFamily: fontFamily1,
              //   ),
              // ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    () {
                      return "SAVOURY EVERY MOMENT";
                    }(),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: ThemeColors.disabled,
                      fontSize: h2,
                      fontWeight: FontWeight.bold,
                      fontFamily: fontFamily2,
                    ),
                  ),
                  SizedBox(height: 8), // spacing between title and subtitle
                  Text(
                    () {
                      return "Dine With Us";
                    }(),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: ThemeColors.primaryDark,
                      fontSize: h1, // slightly smaller font size
                      fontWeight: FontWeight.bold,
                      fontFamily: fontFamily2,
                    ),
                  ),
                ],
              ),

            );
          }
        }
        return Container();
      },
    );
  }
}
