import 'package:amverton/Model/bottom_bar.dart';
import 'package:amverton/main.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class BannerTemplate1 extends StatefulWidget {
  final String theme;

  const BannerTemplate1({
    Key? key,
    required this.theme,
  }) : super(key: key);

  @override
  State<BannerTemplate1> createState() => _BannerTemplate1State();
}

class _BannerTemplate1State extends State<BannerTemplate1> {
  late Future future;
  late Box boxGlobal;

  late List<BottomBar> bottomBarList;

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    bottomBarList = List<BottomBar>.from(boxGlobal.get("bottomBar")) ?? [];
    return await Hive.openBox<Asset>('HOME+BANNER+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            if (list.isNotEmpty) {
              return GestureDetector(
                onTap: () {
                  if (bottomBarList.isNotEmpty) {
                    int orderIndex = bottomBarList
                        .indexWhere((element) => element.name == "BOOKINGS");
                    if (orderIndex != -1) {
                      final dynamic navigationBar = barGlobalKey.currentWidget;
                      navigationBar.onTap(orderIndex);
                    }
                  }
                },
                child: Container(
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  child: Container(
                    margin: EdgeInsets.all(defaultPadding),
                    width: phoneWidth,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.all(Radius.circular(20)),
                      boxShadow: background == null
                          ? [
                              BoxShadow(
                                color: ThemeColors.disabled,
                                offset: const Offset(0.0, 5.0),
                                blurRadius: 5.0,
                                spreadRadius: 2.0,
                              )
                            ]
                          : null,
                    ),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.all(Radius.circular(20)),
                      child: CachedNetworkImage(
                        imageUrl: list.values
                                .firstWhereOrNull(
                                    (element) => element.name == "BANNER")
                                ?.data ??
                            "",
                        width: phoneWidth,
                        fit: BoxFit.fitWidth,
                        placeholder: (context, url) {
                          return Center(
                            child: CircularProgressIndicator(
                              color: ThemeColors.primaryDark,
                            ),
                          );
                        },
                        errorWidget: (context, url, error) {
                          return Icon(
                            Icons.error_outline_outlined,
                            color: ThemeColors.primaryDark,
                          );
                        },
                      ),
                    ),
                  ),
                ),
              );
            }
          }
          return Container();
        });
  }
}
