import 'dart:convert';
import 'package:amverton/Constant/theme.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/home.dart';
import 'package:amverton/Model/user.dart';
import 'package:amverton/Provider/providers.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Account/account_page.dart';
import 'package:amverton/View/Credit/credit_history_page.dart';
import 'package:amverton/View/Credit/credit_topup.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Points/points_history_page.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class BalanceTemplate1 extends ConsumerStatefulWidget {
  final String theme;

  const BalanceTemplate1({Key? key, required this.theme}) : super(key: key);

  @override
  _BalanceTemplate1State createState() => _BalanceTemplate1State();
}

class _BalanceTemplate1State extends ConsumerState<BalanceTemplate1> {
  late Future future;
  late Box box;
  late Box boxGlobal;
  bool enlarge = false;

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('ACCOUNT+BALANCE+1');
  }

  @override
  Widget build(BuildContext context) {
    // Watch both home and user providers
    Home? home = ref.watch(homeProvider);
    User? user = ref.watch(userProvider);

    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            bool isLogin = box.get("is_login") ?? false;
            
            // Determine what data to show
            String displayName = "GUEST";
            String displayPoints = "0";
            String displayTier = "";
            String displayExpiry = "";
            String displayCard = "";
            
            if (isLogin && home?.user != null) {
              // User is logged in and we have user data from home
              displayName = home!.user!.fullName.toUpperCase();
              displayPoints = home.user!.point.split('.')[0]; // Remove decimal for display
              displayTier = home.user!.membership.name ?? "Member";
              displayExpiry = home.user!.membershipExpireOn ?? "";
              displayCard = home.user!.membership.cardImage ?? "";
            } else if (home?.membership != null) {
              // No user login but we have membership data to show as template
              displayName = "MEMBERSHIP BENEFITS";
              displayPoints = "0"; // Default points for non-members
              displayTier = home!.membership!.name ?? "Standard";
              displayExpiry = "";
              displayCard = home.membership!.cardImage ?? "";
            }

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            String? balanceLabel = list.values
                .firstWhereOrNull((element) => element.name == "CREDIT_LABEL")
                ?.data;
            String? pointLabel = list.values
                .firstWhereOrNull((element) => element.name == "POINT_LABEL")
                ?.data;

            if (list.isNotEmpty) {
              return GestureDetector(
                onTap: () {
                  if (isLogin) {
                    Navigator.pushNamed(
                      context,
                      "/membership/1",
                      arguments: {
                        "theme": "L",
                      },
                    );
                  } else {
                    Navigator.of(context, rootNavigator: true)
                        .pushNamed("/login/1");
                  }
                },
                child: Container(
                  margin: EdgeInsets.only(top:20,bottom:defaultPadding, left: defaultPadding, right: defaultPadding),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: Container(
                      width: phoneWidth,
                      height: 230, // Fixed height for card-like appearance
                      decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              image: DecorationImage(
                                      image: CachedNetworkImageProvider(
                                          displayCard),
                                      fit: BoxFit.cover,
                                    ),
                              boxShadow: [
                                BoxShadow(
                                  color: ThemeColors.disabled,
                                  offset: const Offset(0.0, 1.0),
                                  blurRadius: 5.0,
                                  spreadRadius: 2.0,
                                )
                              ],
                            ),
                      child: Stack(
                        children: [
                          // Card content
                          Padding(
                            padding: EdgeInsets.all(defaultPadding),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [

                                Spacer(),
                                
                                // Bottom section - Member info
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    SizedBox(), // Placeholder when not logged in
                                    
                                    // Name and points on the right
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.end,
                                        children: [
                                          // Member name
                                          Text(
                                            displayName,
                                            style: TextStyle(
                                              color: Colors.black,
                                              fontSize: h2,
                                              fontWeight: FontWeight.bold,
                                              fontFamily: fontFamily2,
                                            ),
                                            textAlign: TextAlign.right,
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          
                                          SizedBox(height: 12),
                                          
                                          // Points
                                          if (pointLabel != null)
                                            Text(
                                              "$displayPoints ${list.values.firstWhereOrNull((element) => element.name == "POINT_UNIT")?.data ?? "Points"}",
                                              style: TextStyle(
                                                color: Colors.black,
                                                fontSize: h2,
                                                fontWeight: FontWeight.bold,
                                                fontFamily: fontFamily2,
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                
                                SizedBox(height: 8),
                                
                                // Expiry or benefits info
                                 Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Text(
                                      isLogin && home?.user != null 
                                          ? "Expires on $displayExpiry"
                                          : displayExpiry,
                                      style: TextStyle(
                                        color: ThemeColors.gray,
                                        fontSize: h3,
                                        fontFamily: fontFamily2,
                                      ),
                                    ),
                                    Spacer(),
                                    Text(
                                      "100 points = MYR 5 i.e 5 %",
                                      style: TextStyle(
                                        color: ThemeColors.gray,
                                        fontSize: h4,
                                        fontFamily: fontFamily2,
                                      ),
                                    ),
                                  ]
                                 )
                                
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            }
          }
          return Container();
        });
  }
}