import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_indicator/carousel_indicator.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/home.dart';
import 'package:amverton/Model/branch.dart';
import 'package:amverton/Provider/providers.dart';
import 'package:hive/hive.dart';

class ReferralTemplate1 extends ConsumerStatefulWidget {
  final String theme;

  const ReferralTemplate1({Key? key, required this.theme}) : super(key: key);

  @override
  _ReferralTemplate1State createState() => _ReferralTemplate1State();
}

class _ReferralTemplate1State extends ConsumerState<ReferralTemplate1> {
  late Future future;
  late Box boxGlobal;
  late Box boxAsset;

  int currentIndex = 0;
  Timer? timer;
  PageController pageController = PageController(initialPage: 0, viewportFraction: 0.95);
  List<SlideItem> slideItems = [];

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  @override
  void dispose() {
    super.dispose();
    try {
      timer?.cancel();
    } catch (e) {}
  }

  Future<Box> openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    boxAsset = await Hive.openBox<Asset>('HOME+REFERRAL+1');

    return boxAsset;
  }

  void _buildSlideItems() {
    slideItems.clear();
    
    // Get restaurant data from provider
    Home? home = ref.watch(homeProvider);
    
    if (home?.branches != null && home!.branches!.isNotEmpty) {
      // Use restaurant data from Home API
      for (Branch branch in home.branches!) {
        slideItems.add(SlideItem(
          name: branch.name ?? "Branch",
          imageUrl: branch.image1 ?? "", // Use image_1 from restaurant
          branch: branch,
        ));
      }
    } 
    
    // Start auto-scroll timer if we have items
    if (slideItems.isNotEmpty) {
      _startAutoScroll();
    }
  }

  void _startAutoScroll() {
    timer?.cancel();
    timer = Timer.periodic(const Duration(seconds: 5), (Timer timer) {
      if (currentIndex == slideItems.length - 1) {
        currentIndex = 0;
      } else {
        currentIndex++;
      }

      if (pageController.hasClients) {
        pageController.animateToPage(
          currentIndex,
          duration: const Duration(milliseconds: 350),
          curve: Curves.easeIn,
        );
      }
    });
  }

  Widget _buildSlideItem(SlideItem item, int index) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 6),
      height: 200,
      width: double.infinity,
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: ThemeColors.disabled,
                  offset: const Offset(0.0, 1.0),
                  blurRadius: 5.0,
                  spreadRadius: 2.0,
                )
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: item.imageUrl.isNotEmpty
                  ? CachedNetworkImage(
                      imageUrl: item.imageUrl,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                      placeholder: (context, url) {
                        return Container(
                          color: Colors.grey[300],
                          child: Center(
                            child: CircularProgressIndicator(
                              color: ThemeColors.primaryDark,
                            ),
                          ),
                        );
                      },
                      errorWidget: (context, url, error) {
                        return Container(
                          color: Colors.grey[300],
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.restaurant,
                                color: ThemeColors.primaryDark,
                                size: 48,
                              ),
                              SizedBox(height: 8),
                              Text(
                                item.name,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: h2,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        );
                      },
                    )
                  : Container(
                      color: Colors.grey[300],
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.restaurant,
                            color: ThemeColors.primaryDark,
                            size: 48,
                          ),
                          SizedBox(height: 8),
                          Text(
                            item.name,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: h2,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
            ),
          ),
          // Gradient overlay for better text visibility
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withOpacity(0.7),
                ],
              ),
            ),
          ),
          // Restaurant name overlay
          Positioned(
            bottom: 100,
            left: 12,
            right: 12,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "WELCOME TO",
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: h2,
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        offset: Offset(0, 1),
                        blurRadius: 3,
                        color: Colors.black.withOpacity(0.7),
                      ),
                    ],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          Positioned(
            bottom: 70,
            left: 12,
            right: 12,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.name,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: h1,
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        offset: Offset(0, 1),
                        blurRadius: 3,
                        color: Colors.black.withOpacity(0.7),
                      ),
                    ],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            // Build slide items when data is ready
            _buildSlideItems();

            if (slideItems.isNotEmpty) {
              return Container(
                child: Column(
                  children: [
                    SizedBox(
                      height: phoneHeight / 3.3,
                      child: PageView(
                        controller: pageController,
                        onPageChanged: (index) {
                          setState(() {
                            currentIndex = index;
                          });
                        },
                        children: slideItems.map((item) {
                          return GestureDetector(
                            onTap: () {
                              if (item.branch != null) {
                                Navigator.pushNamed(
                                  context,
                                  '/branch/${item.branch!.id}',
                                  arguments: item.branch,
                                );
                              }
                            },
                            child: _buildSlideItem(item, currentIndex),
                          );
                        }).toList(),
                      )

                    ),
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Column(
                        children: [
                          SizedBox(height: spacingHeightSmall),
                          SizedBox(
                            height: spacingHeightLarge,
                            child: CarouselIndicator(
                              count: slideItems.length,
                              index: currentIndex,
                              activeColor: ThemeColors.primaryDark,
                              color: widget.theme == "L"
                                  ? ThemeColors.disabled
                                  : ThemeColors.gray,
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              );
            } else {
              // Loading state or empty state
              return Container(
                height: phoneHeight / 3.3,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        color: ThemeColors.primaryDark,
                      ),
                      SizedBox(height: 16),
                      Text(
                        "Loading restaurants...",
                        style: TextStyle(
                          color: ThemeColors.primaryDark,
                          fontSize: h3,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }
          }
          return Container(
            height: phoneHeight / 3.3,
            child: Center(
              child: CircularProgressIndicator(
                color: ThemeColors.primaryDark,
              ),
            ),
          );
        });
  }
}

// Helper class to manage slide items
class SlideItem {
  final String name;
  final String imageUrl;
  final Branch? branch;

  SlideItem({
    required this.name,
    required this.imageUrl,
    this.branch,
  });
}