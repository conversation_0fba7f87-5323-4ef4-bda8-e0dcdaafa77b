import 'dart:convert';
import 'dart:developer';

import 'package:amverton/Model/bottom_bar.dart';
import 'package:amverton/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/home.dart';
import 'package:amverton/Notifier/notifiers.dart';
import 'package:amverton/Provider/providers.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/loading1.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

late HomeNotifier homeNotifier;

class HomePage extends ConsumerStatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  HomePageState createState() => HomePageState();

  static void onRefresh(BuildContext context) {
    HomePageState? state = context.findAncestorStateOfType<HomePageState>();
    state?.onRefresh();
  }
}

class HomePageState extends ConsumerState<HomePage>
    with SingleTickerProviderStateMixin {
  Box boxGlobal = Hive.box("boxGlobal");
  late Future future;
  late bool showLoading;

  @override
  void initState() {
    super.initState();
    getHome();
    showLoading = true;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Home? home = ref.watch(homeProvider);
    homeNotifier = ref.read(homeProvider.notifier);

    if (home == null) {
      if (showLoading) {
        return Loading1();
      }
    } else {
      showLoading = false;
      // return Scaffold(
      //   body: RefreshIndicator(
      //     color: ThemeColors.primaryDark,
      //     onRefresh: () {
      //       return Future.delayed(const Duration(seconds: 1), () {
      //         setState(() {
      //           showLoading = true;
      //           getHome();
      //         });
      //       });
      //     },
      //     child: Container(
      //       //todo need backed give assets
      //       decoration: BoxDecoration(
      //           image: DecorationImage(
      //               image: AssetImage("assets/images/home_background.png"),
      //               fit: BoxFit.cover)),
      //       height: phoneHeight,
      //       child: SingleChildScrollView(
      //         physics: const AlwaysScrollableScrollPhysics(),
      //         padding: EdgeInsets.only(top: topPaddingWithoutBar),
      //         child: Column(children: homeWidget),
      //       ),
      //     ),
      //   ),
        
      // );
      return Scaffold(
        body: RefreshIndicator(
          color: ThemeColors.primaryDark,
          onRefresh: () {
            return Future.delayed(const Duration(seconds: 1), () {
              setState(() {
                showLoading = true;
                getHome();
              });
            });
          },
          child: Stack(
            children: [
              // Background image at the top portion
              Container(
                height: 240, // Adjust height as needed
                width: double.infinity,
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage("assets/images/home_background_2.png"),
                    fit: BoxFit.fill,
                  ),
                ),
              ),

              // Scrollable content overlaid on top
              SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: EdgeInsets.only(top: topPaddingWithoutBar),
                child: Column(
                  children: [
                    const SizedBox(height: 10), // Same as background height to push content below image
                    ...homeWidget,
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }
    return Scaffold(
      body: SizedBox(
        height: phoneHeight,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: EdgeInsets.only(top: topPaddingWithoutBar),
          child: Column(children: homeWidget),
        ),
      ),
    );
  }

  Future<Map?> getHome() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.home();
    if (data != null && data['data'] != null) {
      Home home = Home.fromJson(data['data']);
      homeNotifier.updateHome(home);

      Box box = await Hive.openBox('box');
    }
    return data;
  }

  onRefresh() {
    setState(() {
      getHome();
    });
  }
}
