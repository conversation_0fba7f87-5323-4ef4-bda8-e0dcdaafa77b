import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/check_in_event.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class CheckIn extends StatefulWidget {
  final CheckInEvent checkInEvent;

  const CheckIn({Key? key, required this.checkInEvent}) : super(key: key);

  @override
  _CheckInState createState() => _CheckInState();
}

class _CheckInState extends State<CheckIn> {
  late Future future;
  late Box boxGlobal;
  bool enlarge = false;

  @override
  void initState() {
    super.initState();
    future = openBox();
    checkIn();
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    // return await Hive.openBox<AssetTest>('ACCOUNT+REFERRAL+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            return Scaffold(
              appBar: AppBar(
                title: const Text("Check In"),
              ),
              body: SingleChildScrollView(
                child: Column(
                  children: [
                    Image.asset(
                      "assets/images/check_in_top.png",
                      fit: BoxFit.fitWidth,
                      width: phoneWidth,
                    ),
                    // CachedNetworkImage(
                    //   imageUrl: "https://via.placeholder.com/600x360", // 5:4
                    //   width: phoneWidth,
                    //   fit: BoxFit.fitWidth,
                    // ),
                    Container(
                      decoration: const BoxDecoration(
                        image: DecorationImage(
                            image: AssetImage("assets/images/check_in_bg.png"),
                            fit: BoxFit.cover),
                      ),
                      padding: EdgeInsets.all(defaultPadding),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: const [
                              Text("Daily Check in"),
                              Text("Daily Check in"),
                            ],
                          ),
                          Container(
                            margin: EdgeInsets.symmetric(
                                vertical: defaultInnerPadding),
                            padding: EdgeInsets.symmetric(
                              horizontal: defaultInnerPadding,
                              vertical: defaultPadding,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xfffd7e1c),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Column(
                              children: [
                                GridView.builder(
                                  physics: const NeverScrollableScrollPhysics(),
                                  gridDelegate:
                                      const SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 4,
                                    // mainAxisExtent: phoneHeight / 30,
                                    // mainAxisSpacing: 10,
                                  ),
                                  shrinkWrap: true,
                                  itemCount: enlarge
                                      ? widget.checkInEvent.rewards!.length
                                      : 7,
                                  itemBuilder: (BuildContext context, index) {
                                    return Column(
                                      children: [
                                        Stack(
                                          children: [
                                            Container(
                                              width: phoneWidth / 5.5,
                                              height: phoneWidth / 6.5,
                                              decoration: BoxDecoration(
                                                color: ThemeColors.light,
                                                borderRadius:
                                                    BorderRadius.circular(15),
                                                image: DecorationImage(
                                                  image:
                                                      CachedNetworkImageProvider(
                                                    widget
                                                            .checkInEvent
                                                            .rewards![index]
                                                            .image ??
                                                        "",
                                                    scale: 1.2,
                                                  ),
                                                ),
                                              ),
                                            ),
                                            if (widget
                                                    .checkInEvent
                                                    .rewards![index]
                                                    .isClaimed ==
                                                1)
                                              Container(
                                                width: phoneWidth / 5.5,
                                                height: phoneWidth / 6.5,
                                                decoration: BoxDecoration(
                                                  color: ThemeColors.disabled
                                                      .withOpacity(0.7),
                                                  borderRadius:
                                                      BorderRadius.circular(15),
                                                ),
                                              ),
                                          ],
                                        ),
                                        SizedBox(
                                          width: phoneWidth / 5.5,
                                          child: Text(
                                            widget.checkInEvent.rewards![index]
                                                .label,
                                            style: TextStyle(
                                                color: ThemeColors.light,
                                                fontSize: h4),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ],
                                    );
                                  },
                                ),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: defaultPadding),
                                  width: phoneWidth,
                                  child: DefaultButton(
                                    text: enlarge ? "Collapse" : "Load more",
                                    buttonColor: ThemeColors.primaryDark,
                                    onPressed: () {
                                      setState(() {
                                        enlarge = !enlarge;
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      color: ThemeColors.disabled,
                      height: defaultInnerPadding,
                    ),
                    Padding(
                      padding: EdgeInsets.all(defaultPadding),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "How it works",
                            style: TextStyle(
                              color: ThemeColors.dark,
                              fontSize: h1,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: spacingHeightMedium),
                          const Text("Points"),
                          SizedBox(height: spacingHeightSmall),
                          const Text(
                            "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
                          ),
                        ],
                      ),
                    ),
                    Container(
                      color: ThemeColors.disabled,
                      height: defaultInnerPadding,
                    ),
                    ListTile(
                      onTap: () {
                        Navigator.pushNamed(context,
                            "/faq/${accountAsset.firstWhereOrNull((element) => element.sectionName == "QUESTIONS")?.template ?? ""}",
                            arguments: {
                              "theme": accountAsset
                                      .firstWhereOrNull((element) =>
                                          element.sectionName == "QUESTIONS")
                                      ?.theme ??
                                  "L",
                            });
                      },
                      title: const Text("FAQ"),
                      trailing: ImageIcon(AssetImage(
                          "assets/icons/${boxGlobal.get("iconSet")}/Standard_Moreleft.png")),
                    ),
                    SizedBox(height: defaultPadding),
                  ],
                ),
              ),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }

  void checkIn() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    Map? data = await apiPost.checkIn();
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          CheckInReward reward = CheckInReward.fromJson(data['data']['reward']);
          int index = widget.checkInEvent.rewards!
              .indexWhere((element) => element.id == reward.id);
          setState(() {
            widget.checkInEvent.rewards![index] = reward;
          });
          Navigator.pop(context);
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }
}
