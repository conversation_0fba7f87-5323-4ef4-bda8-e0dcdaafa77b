import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/bottom_bar.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Model/product.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Custom%20Widgets/empty_list.dart';
import 'package:amverton/View/Menu/menu_detail_template1.dart';
import 'package:amverton/View/OrderMenu/order_menu_detail_template1.dart';
import 'package:hive/hive.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class ProductList extends StatefulWidget {
  final String? appBarTitle;
  final List<Product>? products;
  final Outlet? outlet;

  const ProductList({
    Key? key,
    this.appBarTitle,
    required this.products,
    this.outlet,
  }) : super(key: key);

  @override
  _ProductListState createState() => _ProductListState();
}

class _ProductListState extends State<ProductList> {
  Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeColors.light,
      appBar: AppBar(title: Text(widget.appBarTitle ?? "")),
      body: widget.products == null
          ? EmptyList(theme: "L")
          : GridView.builder(
              padding: EdgeInsets.fromLTRB(
                defaultPadding,
                defaultPadding,
                defaultPadding,
                bottomPaddingWithoutBar,
              ),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: spacingWidth,
                mainAxisExtent: phoneHeight / 4.25,
                mainAxisSpacing: 10,
              ),
              shrinkWrap: true,
              itemCount: widget.products!.length,
              itemBuilder: (BuildContext ctx, index) {
                return GestureDetector(
                  onTap: () {
                    getProductDetail(
                        widget.products![index], "product_listing/");
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: ThemeColors.light,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: ThemeColors.disabled),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Hero(
                          tag: "product_listing/${widget.products![index].id}",
                          child: ClipRRect(
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(20),
                              topRight: Radius.circular(20),
                            ),
                            child: CachedNetworkImage(
                              imageUrl: widget.products![index].image ?? "",
                              height: phoneWidth / 3.5,
                              width: phoneWidth / 3.5,
                              fit: BoxFit.cover,
                              placeholder: (context, url) {
                                return Center(
                                  child: CircularProgressIndicator(
                                    color: ThemeColors.primaryDark,
                                  ),
                                );
                              },
                              errorWidget: (context, url, error) {
                                return Icon(
                                  Icons.error_outline_outlined,
                                  color: ThemeColors.primaryDark,
                                );
                              },
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AutoSizeText(
                                widget.products![index].name,
                                style: TextStyle(
                                    color: ThemeColors.dark, fontSize: h4),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              SizedBox(height: spacingHeightSmall),
                              Text(
                                important_variables.currency +
                                    widget.products![index].price!,
                                style: TextStyle(
                                    color: ThemeColors.primaryDark,
                                    fontSize: h3),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                );
              },
            ),
    );
  }

  getProductDetail(Product product, String tag) async {
    EasyLoading.show();
    ApiGet apiGet = ApiGet();

    Outlet? outlet;
    if (product.outlet == null) {
      outlet = widget.outlet;
    } else {
      outlet = product.outlet!;
    }

    Map? data = await apiGet.productDetail(product.id);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        Product product = Product.fromJson(data['data']['product']);
        List<Product> recommended =
            (data['data']['recommended_products'] as List)
                .map((data) => Product.fromJson(data))
                .toList();
        String? inviteMsg = data['data']['invite_message'] ?? null;
        String? inviteUrl = data['data']['invite_url'] ?? null;

        List<BottomBar> bottomBarList = boxGlobal.get("bottomBar") ?? [];
        if (bottomBarList.isNotEmpty) {
          int orderIndex =
              bottomBarList.indexWhere((element) => element.name == "ORDER");
          if (orderIndex != -1) {
            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (BuildContext context) => OrderMenuDetailTemplate1(
                          outlet: outlet!,
                          product: product,
                          recommended: recommended,
                          inviteMsg: inviteMsg,
                          inviteUrl: inviteUrl,
                          tag: "$tag${product.id}",
                        )));
          } else {
            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (BuildContext context) => MenuDetailTemplate1(
                          product: product,
                          recommended: recommended,
                          tag: "$tag${product.id}",
                        )));
          }
        }
      }
    }
  }
}
