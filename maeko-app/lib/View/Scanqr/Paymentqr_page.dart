import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:ui';
import 'package:amverton/View/Scanqr/Section/paymentqr_code_template1.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Model/scanqr.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:hive/hive.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/points_history.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Notifier/notifiers.dart';
import 'package:amverton/View/Custom%20Widgets/loading1.dart';
import 'package:collection/collection.dart';
import 'package:amverton/View/Scanqr/Section/qr_code_template1.dart';
import 'package:amverton/View/Scanqr/Section/recent_transaction_template1.dart';
import 'package:amverton/View/Scanqr/Section/transaction_item_template1.dart';
import 'package:provider/provider.dart';
import 'package:amverton/Model/points_model.dart';
import 'package:rxdart/rxdart.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_button2.dart';

Scanqr? scanqr;

class PaymentqrPage extends StatefulWidget {
  const PaymentqrPage({Key? key}) : super(key: key);

  @override
  PaymentqrPageState createState() => PaymentqrPageState();

  static void onRefresh(BuildContext context) {
    PaymentqrPageState? state =
        context.findAncestorStateOfType<PaymentqrPageState>();
    state?.onRefresh();
  }
}

class PaymentqrPageState extends State<PaymentqrPage> {
  Box boxGlobal = Hive.box("boxGlobal");
  Box box = Hive.box('box');

  late bool isLogin;
  late Future future;
  final ScrollController receivedScrollController = ScrollController();
  bool receivedHasMorePages = false;
  int receivedNextPage = 0;
  
  StreamController receivedStreamController = BehaviorSubject();
  List<PointsHistory> receivedList = [];

  @override
  void initState() {
    super.initState();
    future = getPointsHistory(receivedNextPage);
    isLogin = box.get("is_login") ?? false;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Ensure rewards are refreshed each time the page is visited
    setState(() {
      receivedList.clear();
      receivedNextPage = 0;
      receivedHasMorePages = false;
      future = getPointsHistory(receivedNextPage);
    });
  }

  void _showPaymentModal(BuildContext context) {
    final TextEditingController amountController = TextEditingController();
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          padding: EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Enter Amount",
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: ThemeColors.primaryDark,
                ),
              ),
              SizedBox(height: 16),
              TextField(
                controller: amountController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: "Amount",
                  border: OutlineInputBorder(),
                  prefixText: "\$ ",
                ),
              ),
              SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: DefaultButton(
                      text: "Confirm",
                      buttonColor: ThemeColors.primaryDark,
                      onPressed: () {
                        // Handle payment logic here
                        Navigator.pop(context);
                        // You can add your payment processing logic
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        FutureBuilder(
          future: future,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
           
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Loading1();
            } else if (snapshot.connectionState == ConnectionState.done &&
                snapshot.hasData) {
              List<PointsHistory> newList = (snapshot.data['data']['points'] as List)
                  .map((data) => PointsHistory.fromJson(data))
                  .toList();

              // Update PointsModel with the new list
              return Scaffold(
                  backgroundColor: Colors.white,
                  appBar: AppBar(
                     backgroundColor: Colors.white,
                    title: Text(() {
                      List bottomBarList = boxGlobal.get("bottomBar") ?? [];
                      if (bottomBarList.isNotEmpty) {
                        return bottomBarList
                                .firstWhereOrNull(
                                    (element) => element.name == "SCANQR")
                                ?.title ??
                            "";
                      }
                      return "";
                    }(), style: TextStyle(
                        color: ThemeColors.primaryDark,
                        fontSize: h2,
                        fontWeight: FontWeight.w600,
                        fontFamily: "Poppins",
                      ),
                    ),
                    iconTheme: IconThemeData(
                      color: ThemeColors.primaryDark,
                    ),
                  
                  ),
                  body: RefreshIndicator(
                    color: ThemeColors.primaryDark,
                    onRefresh: () {
                      return getPointsHistory(receivedNextPage);
                    },
                    child: SizedBox(
                      height: phoneHeight,
                      child: Container(
                        color: Colors.white, // Set your desired background color here
                        child: SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              QRCodeTemplate1(
                                innerBackground: null,
                                innerBackgroundType: null,
                              ),
                              SizedBox(height: spacingHeightSmall),
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 16),
                                child: DefaultButton(
                                  text: "Enter Another Amount",
                                  buttonColor: ThemeColors.primaryDark,
                                  onPressed: () => _showPaymentModal(context),
                                ),
                              ),
                              SizedBox(height: spacingHeightSmall),
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 16),
                                child: DefaultButton(
                                  text: "Cancel",
                                  buttonColor: Colors.grey,
                                  onPressed: () => Navigator.pop(context),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                );
            }
            return Scaffold(
              backgroundColor: Colors.white,
              appBar: AppBar(
                  backgroundColor: Colors.white, 
                title: Text(() {
                  List bottomBarList = boxGlobal.get("bottomBar") ?? [];
                  if (bottomBarList.isNotEmpty) {
                    return bottomBarList
                            .firstWhereOrNull(
                                (element) => element.name == "SCANQR")
                            ?.title ??
                        "";
                  }
                  return "";
                }(), style: TextStyle(
                      color: ThemeColors.primaryDark,
                      fontSize: h2,
                      fontWeight: FontWeight.w600,
                      fontFamily: "Poppins",
                    ),),
              ),
              body: RefreshIndicator(
                color: ThemeColors.primaryDark,
                onRefresh: () {
                  return getPointsHistory(receivedNextPage);
                },
                child: SizedBox(
                  height: phoneHeight,

                  child: Container( 
                    color: Colors.white,
                    child:SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: EdgeInsets.only(top: topPaddingWithoutBar),
                    child: Column(
                      
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        PaymentQRCodeTemplate1(
                          innerBackground: null, // Pass default or null
                          innerBackgroundType: null, // Pass default or null
                        ),
                        SizedBox(height: spacingHeightSmall),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: DefaultButton(
                            text: "Enter Another Amount",
                            buttonColor: ThemeColors.primaryDark,
                            onPressed: () => _showPaymentModal(context),
                          ),
                        ),
                        SizedBox(height: spacingHeightSmall),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: DefaultButton2(
                            text: "Cancel",
                            buttonColor: Colors.white,
                            textColor: ThemeColors.primaryDark,
                            onPressed: () => Navigator.pop(context),
                          ),
                        ),
                      ],
                    ),
                  )),
                ),
              ),
            );       
          },
        ),
      ],
    );
  }

  Future<Map?> getPointsHistory(int nextPage) async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.pointHistory("gained", nextPage);

    if (data != null) {
      List<PointsHistory> newList = (data['data']['points'] as List)
          .map((data) => PointsHistory.fromJson(data))
          .toList();

      receivedHasMorePages = data["data"]["has_more_pages"];
      if (receivedHasMorePages) {
        receivedNextPage = data["data"]["next_page"] ?? 0;
      }
      receivedList.addAll(newList);
      receivedStreamController.add(data);
    }
    return data;
  }

  @override
  void dispose() {
    receivedStreamController.close();
    receivedScrollController.dispose();
    super.dispose();
  }

  onRefresh() {
    setState(() {
      receivedList.clear();
      receivedNextPage = 0;
      receivedHasMorePages = false;
      getPointsHistory(receivedNextPage);
    });
  }
}
