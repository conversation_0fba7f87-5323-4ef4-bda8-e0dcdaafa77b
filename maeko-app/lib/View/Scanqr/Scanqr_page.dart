// import 'dart:async';
// import 'dart:convert';
// import 'dart:developer';
// import 'package:flutter/material.dart';
// import 'package:amverton/Model/scanqr.dart';
// import 'package:amverton/Repository/api_get.dart';
// import 'package:hive/hive.dart';
// import 'package:amverton/Model/pages.dart';
// import 'package:amverton/Model/points_history.dart';
// import 'package:amverton/Constant/theme_colors.dart';
// import 'package:amverton/Constant/theme_size.dart';
// import 'package:amverton/Notifier/notifiers.dart';
// import 'package:amverton/View/Custom%20Widgets/loading1.dart';
// import 'package:collection/collection.dart';
// import 'package:amverton/View/Scanqr/Section/qr_code_template1.dart';
// import 'package:amverton/View/Scanqr/Section/recent_transaction_template1.dart';
// import 'package:amverton/View/Scanqr/Section/transaction_item_template1.dart';
// import 'package:provider/provider.dart';
// import 'package:amverton/Model/points_model.dart';
// import 'package:rxdart/rxdart.dart';
// import 'package:amverton/Repository/modify_assets.dart';
// import 'package:amverton/View/Custom%20Widgets/default_button.dart';

// // late ScanQRNotifier scanQRNotifier;
// // Scanqr? scanqr;

// class ScanqrPage extends StatefulWidget {
//   const ScanqrPage({Key? key}) : super(key: key);

//   @override
//   ScanqrPageState createState() => ScanqrPageState();

//   static void onRefresh(BuildContext context) {
//     ScanqrPageState? state =
//         context.findAncestorStateOfType<ScanqrPageState>();
//     state?.onRefresh();
//   }
// }

// class ScanqrPageState extends State<ScanqrPage> {
//   Box boxGlobal = Hive.box("boxGlobal");
//   Box box = Hive.box('box');

//   late bool isLogin;
//   late Future future;
//   final ScrollController receivedScrollController = ScrollController();
//   bool receivedHasMorePages = false;
//   int receivedNextPage = 0;
  
//   StreamController receivedStreamController = BehaviorSubject();
//   List<PointsHistory> receivedList = [];

//   @override
//   void initState() {
//     super.initState();
//     future = getPointsHistory(receivedNextPage);
//     isLogin = box.get("is_login") ?? false;
  
//   }

//   @override
//   void didChangeDependencies() {
//     super.didChangeDependencies();
//     // Ensure rewards are refreshed each time the page is visited
//     setState(() {
//       receivedList.clear();
//       receivedNextPage = 0;
//       receivedHasMorePages = false;
//       future = getPointsHistory(receivedNextPage);
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Stack(
//       children: [
//         FutureBuilder(
//           future: future,
//           builder: (BuildContext context, AsyncSnapshot snapshot) {
           
//             if (snapshot.connectionState == ConnectionState.waiting) {
//               return Loading1();
//             } else if (snapshot.connectionState == ConnectionState.done &&
//                 snapshot.hasData) {
//               List<PointsHistory> newList = (snapshot.data['data']['points'] as List)
//                   .map((data) => PointsHistory.fromJson(data))
//                   .toList();

//               // Update PointsModel with the new list
//               //return SizedBox();
//               return ChangeNotifierProvider(
//                 create: (context) => PointsModel(newList),
//                 child: Scaffold(
//                   backgroundColor: Colors.white,
//                   appBar: AppBar(
//                      backgroundColor: Colors.white,
//                     title: Text(() {
//                       List bottomBarList = boxGlobal.get("bottomBar") ?? [];
//                       if (bottomBarList.isNotEmpty) {
//                         return bottomBarList
//                                 .firstWhereOrNull(
//                                     (element) => element.name == "SCANQR")
//                                 ?.title ??
//                             "";
//                       }
//                       return "";
//                     }(), style: TextStyle(
//                         color: ThemeColors.primaryDark,
//                         fontSize: h2,
//                         fontWeight: FontWeight.w600,
//                         fontFamily: "Poppins",
//                       ),
//                     ),
//                     iconTheme: IconThemeData(
//                       color: ThemeColors.primaryDark,
//                     ),
                  
//                   ),
//                   body: RefreshIndicator(
//                     color: ThemeColors.primaryDark,
//                     onRefresh: () {
//                       return getPointsHistory(receivedNextPage);
//                     },
//                     child: SizedBox(
//                       height: phoneHeight,
//                       child: Container(
//                         color: Colors.white, // Set your desired background color here
//                         child: SingleChildScrollView(
//                           physics: const AlwaysScrollableScrollPhysics(),
//                           child: Column(
//                             crossAxisAlignment: CrossAxisAlignment.start,
//                             children: [
//                               QRCodeTemplate1(
//                                 innerBackground: null,
//                                 innerBackgroundType: null,
//                               ),
//                                SizedBox(height: spacingHeightSmall),
//                                const RecentTransactionsTemplate1(),
//                             ],
//                           ),
//                         ),
//                       ),
//                     ),
//                   ),
//                 ),
//               );
//             }
//             if(isLogin){
//               return Scaffold(
//                 backgroundColor: Colors.white,
//                 appBar: AppBar(
//                    backgroundColor: Colors.white, 
//                   title: Text(() {
//                     List bottomBarList = boxGlobal.get("bottomBar") ?? [];
//                     if (bottomBarList.isNotEmpty) {
//                       return bottomBarList
//                               .firstWhereOrNull(
//                                   (element) => element.name == "SCANQR")
//                               ?.title ??
//                           "";
//                     }
//                     return "";
//                   }(), style: TextStyle(
//                         color: ThemeColors.primaryDark,
//                         fontSize: h2,
//                         fontWeight: FontWeight.w600,
//                         fontFamily: "Poppins",
//                       ),),
//                 ),
//                 body: RefreshIndicator(
//                   color: ThemeColors.primaryDark,
//                   onRefresh: () {
//                     return getPointsHistory(receivedNextPage);
//                   },
//                   child: SizedBox(
//                     height: phoneHeight,

//                     child: Container( 
//                       color: Colors.white,
//                       child:SingleChildScrollView(
//                       physics: const AlwaysScrollableScrollPhysics(),
//                       padding: EdgeInsets.only(top: topPaddingWithoutBar),
//                       child: Column(
                        
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           QRCodeTemplate1(
//                             innerBackground: null, // Pass default or null
//                             innerBackgroundType: null, // Pass default or null
//                           ),
//                           SizedBox(height: spacingHeightSmall),
//                           const RecentTransactionsTemplate1(),
//                         ],
//                       ),
//                     )),
//                   ),
//                 ),
//               );
//             }
//             else{
//               return Scaffold(
//                 backgroundColor: Colors.white,
//                 appBar: AppBar(
//                    backgroundColor: Colors.white, 
//                   title: Text(() {
//                     List bottomBarList = boxGlobal.get("bottomBar") ?? [];
//                     if (bottomBarList.isNotEmpty) {
//                       return bottomBarList
//                               .firstWhereOrNull(
//                                   (element) => element.name == "SCANQR")
//                               ?.title ??
//                           "";
//                     }
//                     return "";
//                   }()),
//                 ),
//                 body: GestureDetector(
//                   onTap: () {
//                     // Navigate to the login page
//                     Navigator.of(context, rootNavigator: true)
//                         .pushNamed("/login/${loginAsset.template}");
//                   },
//                   child: Center(
//                     child: Container(
//                       color: Colors.white,
//                       alignment: Alignment.center,
//                       margin:
//                           EdgeInsets.only(top: defaultInnerPadding),
//                       padding: EdgeInsets.symmetric(
//                           horizontal: defaultPadding),
//                       child: DefaultButton(
//                         buttonColor: ThemeColors.primaryDark,
//                         text: "Login or Register",
//                         textColor: Colors.white,
//                        onPressed: () {
//                           Navigator.of(context, rootNavigator: true)
//                             .pushNamed("/login/${loginAsset.template}");
//                         },
//                       )),
//                   ),
//                 ),
//               );
//             }          
//           },
//         ),
//       ],
//     );
//   }

//   Future<Map?> getPointsHistory(int nextPage) async {
//     ApiGet apiGet = ApiGet();
//     Map? data = await apiGet.pointHistory("gained", nextPage);

//     if (data != null) {
//       List<PointsHistory> newList = (data['data']['points'] as List)
//           .map((data) => PointsHistory.fromJson(data))
//           .toList();

//       receivedHasMorePages = data["data"]["has_more_pages"];
//       if (receivedHasMorePages) {
//         receivedNextPage = data["data"]["next_page"] ?? 0;
//       }
//       receivedList.addAll(newList);
//       receivedStreamController.add(data);
//     }
//     return data;
//   }

//   @override
//   void dispose() {
//     receivedStreamController.close();
//     receivedScrollController.dispose();
//     super.dispose();
//   }

//   onRefresh() {
//     setState(() {
//       receivedList.clear();
//       receivedNextPage = 0;
//       receivedHasMorePages = false;
//       getPointsHistory(receivedNextPage);
//     });
//   }
// }

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:amverton/Constant/theme.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Model/scanqr.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:hive/hive.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/points_history.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Notifier/notifiers.dart';
import 'package:amverton/View/Custom%20Widgets/loading1.dart';
import 'package:collection/collection.dart';
import 'package:amverton/View/Scanqr/Paymentqr_page.dart';
import 'package:amverton/View/Scanqr/Section/payment_template1.dart';
import 'package:amverton/View/Scanqr/Section/balance_template1.dart';
import 'package:amverton/View/Scanqr/Section/qr_code_template1.dart';
import 'package:amverton/View/Scanqr/Section/recent_transaction_template1.dart';
import 'package:amverton/View/Scanqr/Section/transaction_item_template1.dart';
import 'package:provider/provider.dart';
import 'package:amverton/Model/points_model.dart';
import 'package:rxdart/rxdart.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';

class ScanqrPage extends StatefulWidget {
  const ScanqrPage({Key? key}) : super(key: key);

  @override
  ScanqrPageState createState() => ScanqrPageState();

  static void onRefresh(BuildContext context) {
    ScanqrPageState? state =
        context.findAncestorStateOfType<ScanqrPageState>();
    state?.onRefresh();
  }
}

class ScanqrPageState extends State<ScanqrPage> {
  Box boxGlobal = Hive.box("boxGlobal");
  Box box = Hive.box('box');

  late bool isLogin;
  late Future future;
  final ScrollController receivedScrollController = ScrollController();
  bool receivedHasMorePages = false;
  int receivedNextPage = 0;
    // Add this flag to control whether to use dummy data
  final bool useDummyData = true; // Set to false to use real API

  StreamController receivedStreamController = BehaviorSubject();
  List<PointsHistory> receivedList = [];

  @override
  void initState() {
    super.initState();
    future = useDummyData ? getDummyPointsHistory() : getPointsHistory(receivedNextPage);
     isLogin = box.get("is_login") ?? false;
     isLogin = true;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Ensure rewards are refreshed each time the page is visited
    setState(() {
      receivedList.clear();
      receivedNextPage = 0;
      receivedHasMorePages = false;
      future = useDummyData ? getDummyPointsHistory() : getPointsHistory(receivedNextPage);
    });
  }

  // Add this method to create dummy data
  Future<Map> getDummyPointsHistory() async {
    // Simulate network delay (optional)
    await Future.delayed(Duration(milliseconds: 500));
    
    return {
      "data": {
        "points": [
          {
            "id": 1,
            "before": "500",
            "difference": "+150",
            "after": "650",
            "description": "Purchase at Coffee Shop",
            "created_at": "2024-01-15T10:30:00Z",
            "type": "earned"
          },
          {
            "id": 2,
            "before": "425",
            "difference": "+75",
            "after": "500",
            "description": "Restaurant Visit",
            "created_at": "2024-01-14T19:45:00Z",
            "type": "earned"
          },
          {
            "id": 3,
            "before": "525",
            "difference": "-100",
            "after": "425",
            "description": "Redeemed Coffee Voucher",
            "created_at": "2024-01-13T14:20:00Z",
            "type": "redeemed"
          },
          {
            "id": 4,
            "before": "325",
            "difference": "+200",
            "after": "525",
            "description": "Shopping at Mall",
            "created_at": "2024-01-12T16:15:00Z",
            "type": "earned"
          },
          {
            "id": 5,
            "before": "275",
            "difference": "+50",
            "after": "325",
            "description": "Gas Station Purchase",
            "created_at": "2024-01-11T09:30:00Z",
            "type": "earned"
          },
          {
            "id": 6,
            "before": "375",
            "difference": "-100",
            "after": "275",
            "description": "Redeemed Discount Voucher",
            "created_at": "2024-01-10T14:00:00Z",
            "type": "redeemed"
          },
          {
            "id": 7,
            "before": "250",
            "difference": "+125",
            "after": "375",
            "description": "Online Purchase",
            "created_at": "2024-01-09T16:30:00Z",
            "type": "earned"
          }
        ],
        "has_more_pages": false,
        "next_page": null
      }
    };
  }

  void _showPaymentModal(BuildContext context) {
  final TextEditingController amountController = TextEditingController();
  
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
        ),
        padding: EdgeInsets.all(20),
        child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(bottom: 24),
                width: 48,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              // Balance section
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Balance',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      const Text(
                        'RM 200',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      Text(
                        '100 points = MYR 5 i.e 5%',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 32),
              
              // Amount input field
              TextField(
                controller: amountController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: "Amount",
                  border: OutlineInputBorder(),
                  prefixText: "\$ ",
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Confirm button
              Row(
                children: [

                  Expanded(
                    child: DefaultButton(
                      text: "Confirm",
                      buttonColor: ThemeColors.primaryDark,
                      onPressed: () {
                        // Handle payment logic here
                          Navigator.push(
                              context,
                                MaterialPageRoute(
                                  builder: (BuildContext context) => const PaymentqrPage()));
                                      // You can add your payment processing logic
                      },
                    ),
                  ),
                ],
              ),
                
              const SizedBox(height: 16),
            ],
          ),
      ),
      // Container(
      //   decoration: const BoxDecoration(
      //     color: Colors.white,
      //     borderRadius: BorderRadius.only(
      //       topLeft: Radius.circular(24),
      //       topRight: Radius.circular(24),
      //     ),
      //   ),
      //   child: Padding(
      //     padding: EdgeInsets.only(
      //       left: 24,
      //       right: 24,
      //       top: 12,
      //       bottom: MediaQuery.of(context).viewInsets.bottom + 24,
      //     ),
      //     child: 
      //   ),
      // ),
    ),
  );
}

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        FutureBuilder(
          future: future,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Loading1();
            } else if (snapshot.connectionState == ConnectionState.done &&
                snapshot.hasData) {
              List<PointsHistory> newList = (snapshot.data['data']['points'] as List)
                  .map((data) => PointsHistory.fromJson(data))
                  .toList();

              return ChangeNotifierProvider(
                create: (context) => PointsModel(newList),
                child: DefaultTabController(
                  length: 2,
                  child: Scaffold(
                    backgroundColor: Colors.white,
                    appBar: AppBar(
                      backgroundColor: Colors.white,
                      title: Text(() {
                        List bottomBarList = boxGlobal.get("bottomBar") ?? [];
                        if (bottomBarList.isNotEmpty) {
                          return bottomBarList
                                  .firstWhereOrNull(
                                      (element) => element.name == "SCANQR")
                                  ?.title ??
                              "";
                        }
                        return "";
                      }(), style: TextStyle(
                          color: ThemeColors.primaryDark,
                          fontSize: h2,
                          fontWeight: FontWeight.w600,
                          fontFamily: fontFamily2,
                        ),
                      ),
                      iconTheme: IconThemeData(
                        color: ThemeColors.primaryDark,
                      ),
                      bottom: PreferredSize(
                        preferredSize: Size.fromHeight(60),
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16),
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(30),
                            ),
                            child: TabBar(
                              indicator: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Color(0xFF818592),
                                    Color(0xFF595C68),
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                borderRadius: BorderRadius.circular(30),
                              ),
                              indicatorSize: TabBarIndicatorSize.tab,
                              labelColor: Colors.white,
                              unselectedLabelColor: ThemeColors.gray,
                              indicatorWeight: 0,
                              indicatorPadding: EdgeInsets.zero,
                              dividerColor: Colors.transparent,
                              tabs: [
                                Tab(child: Container(
                                  width: double.infinity, 
                                  child: Center(
                                    child: Text(
                                      "Collect Points", 
                                      style: TextStyle(
                                        fontSize: h2,
                                        fontWeight: FontWeight.w600,
                                        fontFamily: fontFamily2
                                      ),
                                      textAlign: TextAlign.center
                                    )
                                  )
                                )),
                                Tab(child: Container(
                                  width: double.infinity, 
                                  child: Center(
                                    child: Text(
                                      "Use Points", 
                                      style: TextStyle(
                                        fontSize: h2,
                                        fontWeight: FontWeight.w600,
                                        fontFamily: fontFamily2
                                      ),
                                      textAlign: TextAlign.center
                                    )
                                  )
                                )),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    body: TabBarView(
                      children: [
                        qrScanningTab(),
                        transactionHistoryTab(),
                      ],
                    ),
                  ),
                ),
              );
            }
            
            if(isLogin){
              return DefaultTabController(
                length: 2,
                child: Scaffold(
                  backgroundColor: Colors.white,
                  appBar: AppBar(
                    backgroundColor: Colors.white, 
                    title: Text(() {
                      List bottomBarList = boxGlobal.get("bottomBar") ?? [];
                      if (bottomBarList.isNotEmpty) {
                        return bottomBarList
                                .firstWhereOrNull(
                                    (element) => element.name == "SCANQR")
                                ?.title ??
                            "";
                      }
                      return "";
                    }(), style: TextStyle(
                          color: ThemeColors.primaryDark,
                          fontSize: h2,
                          fontWeight: FontWeight.w600,
                          fontFamily: fontFamily2,
                        ),
                    ),
                    iconTheme: IconThemeData(
                      color: ThemeColors.primaryDark,
                    ),
                    bottom: PreferredSize(
                      preferredSize: Size.fromHeight(60),
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(30),
                          ),
                          child: TabBar(
                            indicator: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Color(0xFF818592),
                                  Color(0xFF595C68),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(30),
                            ),
                            indicatorSize: TabBarIndicatorSize.tab,
                            labelColor: Colors.white,
                            unselectedLabelColor: ThemeColors.gray,
                            indicatorWeight: 0,
                            indicatorPadding: EdgeInsets.zero,
                            dividerColor: Colors.transparent,
                            tabs: [
                              Tab(child: Container(
                                width: double.infinity, 
                                child: Center(
                                  child: Text(
                                    "Collect Points", 
                                    style: TextStyle(
                                      fontSize: h2,
                                      fontWeight: FontWeight.w600,
                                      fontFamily: fontFamily2
                                    ),
                                    textAlign: TextAlign.center
                                  )
                                )
                              )),
                              Tab(child: Container(
                                width: double.infinity, 
                                child: Center(
                                  child: Text(
                                    "Use Points", 
                                    style: TextStyle(
                                      fontSize: h2,
                                      fontWeight: FontWeight.w600,
                                      fontFamily: fontFamily2
                                    ),
                                    textAlign: TextAlign.center
                                  )
                                )
                              )),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  body: TabBarView(
                    children: [
                      qrScanningTab(),
                      transactionHistoryTab(),
                    ],
                  ),
                ),
              );
            } else {
              return Scaffold(
                backgroundColor: Colors.white,
                appBar: AppBar(
                  backgroundColor: Colors.white, 
                  title: Text(() {
                    List bottomBarList = boxGlobal.get("bottomBar") ?? [];
                    if (bottomBarList.isNotEmpty) {
                      return bottomBarList
                              .firstWhereOrNull(
                                  (element) => element.name == "SCANQR")
                              ?.title ??
                          "";
                    }
                    return "";
                  }()),
                ),
                body: GestureDetector(
                  onTap: () {
                    // Navigate to the login page
                    Navigator.of(context, rootNavigator: true)
                        .pushNamed("/login/${loginAsset.template}");
                  },
                  child: Center(
                    child: Container(
                      color: Colors.white,
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(top: defaultInnerPadding),
                      padding: EdgeInsets.symmetric(horizontal: defaultPadding),
                      child: DefaultButton(
                        buttonColor: ThemeColors.primaryDark,
                        text: "Login or Register",
                        textColor: Colors.white,
                        onPressed: () {
                          Navigator.of(context, rootNavigator: true)
                            .pushNamed("/login/${loginAsset.template}");
                        },
                      )
                    ),
                  ),
                ),
              );
            }          
          },
        ),
      ],
    );
  }

  Widget qrScanningTab() {
    return RefreshIndicator(
      color: ThemeColors.primaryDark,
      onRefresh: () {
        return getPointsHistory(receivedNextPage);
      },
      child: Container(
        height: phoneHeight,
        color: Colors.white,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              QRCodeTemplate1(
                innerBackground: null,
                innerBackgroundType: null,
              ),
              SizedBox(height: spacingHeightSmall),
              const RecentTransactionsTemplate1(),
            ],
          ),
        ),
      ),
    );
  }

  Widget transactionHistoryTab() {
    return RefreshIndicator(
      color: ThemeColors.primaryDark,
      onRefresh: () {
        return getPointsHistory(receivedNextPage);
      },
      child: Container(
        height: phoneHeight,
        color: Colors.white,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              BalanceTemplate1(theme: 'L'),
              SizedBox(height: spacingHeightSmall),
                       // Enter amount button
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: DefaultButton(
                  text: "Enter Certain Amount",
                  onPressed: () => _showPaymentModal(context),
                ),
              ),
              SizedBox(height: spacingHeightSmall),
              const RecentTransactionsTemplate1(),
            ],
          ),
        ),
      ),
    );
  }

  Future<Map?> getPointsHistory(int nextPage) async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.pointHistory("gained", nextPage);

    if (data != null) {
      List<PointsHistory> newList = (data['data']['points'] as List)
          .map((data) => PointsHistory.fromJson(data))
          .toList();

      receivedHasMorePages = data["data"]["has_more_pages"];
      if (receivedHasMorePages) {
        receivedNextPage = data["data"]["next_page"] ?? 0;
      }
      receivedList.addAll(newList);
      receivedStreamController.add(data);
    }
    return data;
  }

  @override
  void dispose() {
    receivedStreamController.close();
    receivedScrollController.dispose();
    super.dispose();
  }

  onRefresh() {
    setState(() {
      receivedList.clear();
      receivedNextPage = 0;
      receivedHasMorePages = false;
      getPointsHistory(receivedNextPage);
    });
  }
}