import 'dart:math';
import 'dart:convert';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Model/pages.dart';

class QRCodeTemplate1 extends StatefulWidget {
  final Asset? innerBackground;
  final String? innerBackgroundType;
  
  const QRCodeTemplate1({
    Key? key,
    this.innerBackground,
    this.innerBackgroundType,
  }) : super(key: key);

  @override
  State<QRCodeTemplate1> createState() => _QRCodeTemplate1State();
}

class _QRCodeTemplate1State extends State<QRCodeTemplate1> {
  late String qrData = "";  // Default empty string
  late Timer timer;

  @override
  void initState() {
    super.initState();
    _fetchQRData(); // Fetch once on open
    timer = Timer.periodic(const Duration(seconds: 60), (timer) {
      _fetchQRData(); // Fetch every 60 seconds
    });
  }

  Future<void> _fetchQRData() async {
    ApiGet apiGet = ApiGet(); // Create API instance
    Map? data = await apiGet.qrObtain();

     if (data != null && data['data'] != null && mounted) {
      setState(() {
        qrData = data['data']['qr_code'] ?? "";
      });
    } else {
      // Optional: Handle the null case
      setState(() {
        qrData = ""; // or show error message
      });
    }
  }

  @override
  void dispose() {
    timer.cancel();
    super.dispose();
  }

  BoxDecoration _buildBackgroundDecoration() {
    if (widget.innerBackground == null) {
      return BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: ThemeColors.disabled,
            offset: const Offset(0.0, 5.0),
            blurRadius: 5.0,
            spreadRadius: 0.5,
          ),
        ],
      );
    } else {
      return BoxDecoration(
        image: widget.innerBackgroundType == "IMAGE"
            ? DecorationImage(
                image: CachedNetworkImageProvider(
                  widget.innerBackground?.data ?? "",
                ),
                fit: BoxFit.cover,
              )
            : null,
        color: widget.innerBackgroundType == "COLOR"
            ? Color(
                int.parse("0xff${widget.innerBackground?.data ?? "FFFFFF"}"),
              )
            : null,
        borderRadius: BorderRadius.circular(20),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center( child:
      Container(
        padding: EdgeInsets.all(defaultPadding),
        width: double.infinity,
        // decoration: BoxDecoration(
        //             image: DecorationImage(
        //                 image: AssetImage("assets/images/qr_background.png"),
        //                 fit: BoxFit.cover),
        //           ),// Main background
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Inner White Container
            Container(
              padding: EdgeInsets.all(defaultPadding),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                // border: Border.all(
                //   color: ThemeColors.gray,
                // ),
              ),
              child: Column(
                children: [
                  if (qrData.isNotEmpty)
                  Container(
                    width: 300,
                    height: 300,
                    child: FittedBox(
                      fit: BoxFit.contain,
                      child: Image.memory(
                        base64Decode(qrData.split(',')[1]), // Extract base64 part after comma
                      ),
                    ),
                  )
                  else
                     Container(
                    width: 300,
                    height: 300,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage("assets/images/temp_qr.png"),
                        fit: BoxFit.cover,
                      ),
                    ),
                    
                  )
                ],
              ),
            ),

            SizedBox(height: spacingHeightSmall),

            // Info Text outside
            Text(
              '2007 Points',
              style: TextStyle(
                fontSize: h1,
                color: ThemeColors.primaryDark,
                fontFamily: 'Poppins',
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: spacingHeightSmall),

            // Info Text outside
            Text(
              '100 Points = MYR 5',
              style: TextStyle(
                fontSize: h2,
                color: ThemeColors.gray,
                fontFamily: 'Poppins',
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      )
    );
  }
}
