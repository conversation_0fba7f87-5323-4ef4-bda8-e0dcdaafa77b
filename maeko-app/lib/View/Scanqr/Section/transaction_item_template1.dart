import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/points_history.dart';  // Assuming PointsHistory is imported from the correct path
import 'package:auto_size_text/auto_size_text.dart';
import 'package:amverton/Repository/modify_assets.dart';

class TransactionItemTemplate1 extends StatelessWidget {
  final PointsHistory pointsHistory;  // Updated to accept PointsHistory instead of Transaction

  const TransactionItemTemplate1({super.key, required this.pointsHistory});

  @override
  Widget build(BuildContext context) {
    final double differentPoint = double.tryParse(pointsHistory.difference.toString()) ?? 0;

    // Format it to 2 decimal places
    final String formattedPoints = differentPoint.toStringAsFixed(2);

    return Container(
            margin: EdgeInsets.only(bottom: defaultInnerPadding),
            padding: EdgeInsets.all(defaultInnerPadding),
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AutoSizeText(
                      '${formattedPoints} Points earned',
                      style: TextStyle(
                        color: ThemeColors.primaryDark,
                        fontSize: h3,
                        fontFamily: "Poppins",
                      ),
                      maxLines: 2,
                    ),
                    SizedBox(height: spacingHeightSmall),
                    Text(
                      DateFormat('d MMM yyyy HH:mm')
                          .format(pointsHistory.createdAt),
                      style: TextStyle(
                          color: ThemeColors.gray, fontSize: h4, fontFamily: "Poppins"),
                    ),
                  ],
                ),
                const Spacer(),
                Text(
                  "${formattedPoints} points",
                  style: TextStyle(
                    color: ThemeColors.primaryDark,
                    fontSize: h3,
                    fontFamily: "Poppins",
                  ),
                ),
              ],
            ),
          );
    
  }
}
