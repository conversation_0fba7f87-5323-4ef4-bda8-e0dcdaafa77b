import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:amverton/Model/points_model.dart';
import 'package:amverton/Model/points_history.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/View/Scanqr/Section/transaction_item_template1.dart'; // Ensure this accepts PointsHistory
import 'package:amverton/Repository/modify_assets.dart';

class RecentTransactionsTemplate1 extends StatefulWidget {
  const RecentTransactionsTemplate1({super.key});

  @override
  _RecentTransactionsTemplate1State createState() =>
      _RecentTransactionsTemplate1State();
}

class _RecentTransactionsTemplate1State
    extends State<RecentTransactionsTemplate1> {
  bool _showAllTransactions = false;  // Flag to toggle between recent and all transactions

  @override
  Widget build(BuildContext context) {
    // Access the PointsModel through Provider
    final pointsModel = Provider.of<PointsModel>(context);

        // Check if pointsModel is null
    if (pointsModel == null) {
      // Return a GestureDetector with CircularProgressIndicator inside it
      return GestureDetector(
        onTap: () {
          // Navigate to the login page
          Navigator.of(context, rootNavigator: true)
              .pushNamed("/login/${loginAsset.template}");
        },
        child: Center(
          child: CircularProgressIndicator(), // Your loading spinner
        ),
      );
    }


    // Get the transactions based on the flag (_showAllTransactions)
    final transactions = _showAllTransactions
        ? pointsModel.allTransactions
        : pointsModel.recentTransactions;

    
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(bottom: defaultInnerPadding),
      padding: EdgeInsets.all(defaultInnerPadding), 
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and Show All / Show Recent Button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Transactions',
                style: TextStyle(
                  fontSize: h2,
                  fontWeight: FontWeight.w600,
                  color: ThemeColors.primaryDark,
                  fontFamily: 'Poppins',
                ),
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    _showAllTransactions = !_showAllTransactions;
                  });
                },
                child: Text(
                  _showAllTransactions ? 'Show Recent' : 'Show All',
                  style: TextStyle(
                    fontSize: h3,
                    fontWeight: FontWeight.w600,
                    color: ThemeColors.primaryDark,
                    fontFamily: 'Poppins',
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: spacingHeightSmall),
          // Transaction list
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _showAllTransactions
                ? transactions.length
                : (transactions.length > 3 ? 3 : transactions.length),
            itemBuilder: (context, index) {
              final pointsHistory = transactions[index];
              return TransactionItemTemplate1(pointsHistory: pointsHistory);
            },
          ),
        ],
      ),
    );
  }
}
