import 'dart:math';
import 'dart:convert';
import 'dart:async';
import 'package:amverton/Constant/theme.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Model/pages.dart';

class PaymentQRCodeTemplate1 extends StatefulWidget {
  final Asset? innerBackground;
  final String? innerBackgroundType;
  
  const PaymentQRCodeTemplate1({
    Key? key,
    this.innerBackground,
    this.innerBackgroundType,
  }) : super(key: key);

  @override
  State<PaymentQRCodeTemplate1> createState() => _PaymentQRCodeTemplate1State();
}

class _PaymentQRCodeTemplate1State extends State<PaymentQRCodeTemplate1> {
  late String qrData = "";  // Default empty string
  late Timer timer;

  @override
  void initState() {
    super.initState();
    _fetchQRData(); // Fetch once on open
    timer = Timer.periodic(const Duration(seconds: 60), (timer) {
      _fetchQRData(); // Fetch every 60 seconds
    });
  }

  Future<void> _fetchQRData() async {
    ApiGet apiGet = ApiGet(); // Create API instance
    Map? data = await apiGet.pointHistory("gained", 0);

    if (data != null && mounted) {
      setState(() {
        qrData = data['data']['qrcode'] ?? "";
      });
    }
  }

  @override
  void dispose() {
    timer.cancel();
    super.dispose();
  }

  BoxDecoration _buildBackgroundDecoration() {
    if (widget.innerBackground == null) {
      return BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: ThemeColors.disabled,
            offset: const Offset(0.0, 5.0),
            blurRadius: 5.0,
            spreadRadius: 0.5,
          ),
        ],
      );
    } else {
      return BoxDecoration(
        image: widget.innerBackgroundType == "IMAGE"
            ? DecorationImage(
                image: CachedNetworkImageProvider(
                  widget.innerBackground?.data ?? "",
                ),
                fit: BoxFit.cover,
              )
            : null,
        color: widget.innerBackgroundType == "COLOR"
            ? Color(
                int.parse("0xff${widget.innerBackground?.data ?? "FFFFFF"}"),
              )
            : null,
        borderRadius: BorderRadius.circular(20),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center( child:
      Container(
        padding: EdgeInsets.all(defaultPadding),
        width: double.infinity,
        // decoration: BoxDecoration(
        //             image: DecorationImage(
        //                 image: AssetImage("assets/images/qr_background.png"),
        //                 fit: BoxFit.cover),
        //           ),// Main background
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Inner White Container
            Container(
              padding: EdgeInsets.all(defaultPadding),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                // border: Border.all(
                //   color: ThemeColors.gray,
                // ),
              ),
              child: Column(
                children: [
                  if (qrData.isNotEmpty)
                   Container(
                    width: 300,
                    height: 300,
                    child: FittedBox(
                      fit: BoxFit.contain, // or BoxFit.fill, BoxFit.cover
                      child: Image.memory(base64Decode(qrData)),
                    ),
                  )
                  else
                     Container(
                    width: 300,
                    height: 300,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage("assets/images/temp_qr.png"),
                        fit: BoxFit.cover,
                      ),
                    ),
                    
                  )
                ],
              ),
            ),

            SizedBox(height: spacingHeightSmall),

            // Info Text outside
            Text(
              'RM50.00',
              style: TextStyle(
                fontSize: h0,
                color: ThemeColors.primaryDark,
                fontFamily: fontFamily2,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: spacingHeightSmall),

            // Info Text outside
            Text(
              'Show this QR Code to the counter to use your points',
              style: TextStyle(
                fontSize: h2,
                color: ThemeColors.gray,
                fontFamily: fontFamily2,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      )
    );
  }
}
