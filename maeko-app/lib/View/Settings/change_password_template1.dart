import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class ChangePasswordTemplate1 extends StatefulWidget {
  static String routeName = "/change_password/1";

  const ChangePasswordTemplate1({Key? key}) : super(key: key);

  @override
  _ChangePasswordTemplate1State createState() =>
      _ChangePasswordTemplate1State();
}

class _ChangePasswordTemplate1State extends State<ChangePasswordTemplate1> {
  late Future future;
  late Box boxGlobal;
  String theme = resetPassAsset.theme;

  TextEditingController currentPassController = TextEditingController();
  TextEditingController newPassController = TextEditingController();
  TextEditingController confirmPassController = TextEditingController();

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    boxGlobal = await Hive.openBox("boxGlobal");
    return await Hive.openBox<Asset>('RESET_PASSWORD+MAIN+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                extendBodyBehindAppBar: true,
                appBar: AppBar(
                  title: Text(
                    "Change Password",
                    style: TextStyle(
                      color:
                          theme == "L" ? ThemeColors.dark : ThemeColors.light,
                    ),
                  ),
                  iconTheme: IconThemeData(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                ),
                body: Container(
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  height: phoneHeight,
                  child: SingleChildScrollView(
                    padding: EdgeInsets.fromLTRB(defaultPadding,
                        verticalPaddingLarge, defaultPadding, defaultPadding),
                    physics: const ClampingScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: spacingHeightMedium),
                        Text(
                          "Change Password",
                          style: TextStyle(
                            color: theme == "L"
                                ? ThemeColors.dark
                                : ThemeColors.light,
                            fontSize: h3,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: spacingHeightMedium),
                        Text(
                          "Fill in the blanks",
                          style: TextStyle(
                              color: theme == "L"
                                  ? ThemeColors.dark
                                  : ThemeColors.light,
                              fontSize: h4),
                        ),
                        SizedBox(height: spacingHeightMedium),
                        DefaultTextField1(
                          controller: currentPassController,
                          obscureText: true,
                          hintText: "Current Password",
                        ),
                        DefaultTextField1(
                          controller: newPassController,
                          obscureText: true,
                          hintText: "New Password",
                        ),
                        DefaultTextField1(
                          controller: confirmPassController,
                          obscureText: true,
                          hintText: "New Password Confirmation",
                        ),
                      ],
                    ),
                  ),
                ),
                bottomNavigationBar: Padding(
                  padding: EdgeInsets.fromLTRB(
                    defaultInnerPadding,
                    defaultInnerPadding,
                    defaultInnerPadding,
                    bottomPaddingWithoutBar,
                  ),
                  child: DefaultButton(
                    text: list.values
                            .firstWhereOrNull((element) =>
                                element.name == "SUBMIT_BUTTON_TEXT")
                            ?.data ??
                        "",
                    buttonColor: ThemeColors.primaryDark,
                    onPressed: () {
                      changePassword();
                    },
                  ),
                ),
              ),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }

  void changePassword() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    Map? data = await apiPost.meUpdatePassword(currentPassController.text,
        newPassController.text, confirmPassController.text);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          Navigator.of(context).pop();
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }
}
