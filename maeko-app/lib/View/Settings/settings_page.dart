import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/app_settings.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/settings.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/main.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class SettingsPage extends StatefulWidget {
  final String theme;

  const SettingsPage({Key? key, required this.theme}) : super(key: key);

  @override
  _SettingsPageState createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  late Future boxFuture;
  late Future future;
  late Box box;
  late Box boxGlobal;
  AppSettings? appSettings;

  bool pushNotification = false;
  bool sms = false;
  bool email = false;

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();
    future = getSettings();
  }

  openBox() async {
    box = await Hive.openBox("box");
    boxGlobal = await Hive.openBox("boxGlobal");
    appSettings = boxGlobal.get("appSettings") ?? null;

    return await Hive.openBox<Asset>('ACCOUNT+SETTINGS+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            bool isLogin = box.get("is_login") ?? false;

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return Scaffold(
              extendBodyBehindAppBar: true,
              appBar: AppBar(
                title: Text(
                  list.values
                          .firstWhereOrNull(
                              (element) => element.name == "TITLE")
                          ?.data ??
                      "",
                  style: TextStyle(
                    color: widget.theme == "L"
                        ? ThemeColors.dark
                        : ThemeColors.light,
                  ),
                ),
                iconTheme: IconThemeData(
                  color: widget.theme == "L"
                      ? ThemeColors.dark
                      : ThemeColors.light,
                ),
              ),
              body: Container(
                height: phoneHeight,
                decoration: background == null
                    ? null
                    : BoxDecoration(
                        image: backgroundType == "IMAGE"
                            ? DecorationImage(
                                image: CachedNetworkImageProvider(
                                    background.data ?? ""),
                                fit: BoxFit.cover,
                              )
                            : null,
                        color: backgroundType == "COLOR"
                            ? Color(
                                int.parse("0xff${background.data ?? "FFFFFF"}"))
                            : null,
                      ),
                child: FutureBuilder(
                    future: future,
                    builder: (BuildContext context, AsyncSnapshot snapshot) {
                      if (snapshot.connectionState == ConnectionState.done) {
                        return SafeArea(
                          child: SingleChildScrollView(
                            padding: EdgeInsets.all(defaultPadding),
                            child: Column(
                              children: [
                                switchSelection(
                                  "Push Notification",
                                  pushNotification,
                                  (value) {
                                    setState(() {
                                      pushNotification = value;
                                      updateSettings();
                                    });
                                  },
                                ),
                                switchSelection(
                                  "Email Alert",
                                  email,
                                  (value) {
                                    setState(() {
                                      email = value;
                                      updateSettings();
                                    });
                                  },
                                ),
                                switchSelection(
                                  "SMS Alert",
                                  sms,
                                  (value) {
                                    setState(() {
                                      sms = value;
                                      updateSettings();
                                    });
                                  },
                                ),
                                selection(
                                  "Edit Profile",
                                  "assets/icons/${boxGlobal.get("iconSet")}/tabler_edit.png",
                                  () {
                                    Navigator.pushNamed(context,
                                        "/edit_profile/${editProfileAsset.template}",
                                        arguments: {
                                          "theme": accountAsset
                                                  .firstWhereOrNull((element) =>
                                                      element.sectionName ==
                                                      "EDIT_PROFILE")
                                                  ?.theme ??
                                              "L",
                                        });
                                  },
                                ),
                                if (appSettings?.logins?.hasPasswordLogin == 1)
                                  selection(
                                    "Change Password",
                                    "assets/icons/${boxGlobal.get("iconSet")}/tabler_password.png",
                                    () => Navigator.pushNamed(
                                      context,
                                      "/change_password/${resetPassAsset.template}",
                                    ),
                                  ),
                                warningSelection("Delete Account",
                                    "assets/icons/${boxGlobal.get("iconSet")}/delete_user.png",
                                    () {
                                  deleteAccDialog();
                                }),
                                if (isLogin)
                                  warningSelection("Logout",
                                      "assets/icons/${boxGlobal.get("iconSet")}/logout.png",
                                      () {
                                    logoutDialog();
                                  }),
                              ],
                            ),
                          ),
                        );
                      }
                      return Container();
                    }),
              ),
            );
          }
          return Container();
        });
  }

  Widget switchSelection(String title, bool value, Function(bool) onToggle) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          color: widget.theme == "L" ? ThemeColors.dark : ThemeColors.light,
          fontSize: h3,
          fontWeight: FontWeight.w600,
        ),
      ),
      trailing: Container(
        height: phoneHeight / 35,
        width: phoneWidth / 8,
        child: FlutterSwitch(
            toggleSize: 18,
            valueFontSize: h4,
            showOnOff: true,
            activeColor: ThemeColors.primaryDark,
            value: value,
            onToggle: onToggle),
      ),
    );
  }

  Widget selection(String title, String image, Function() onTap) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          color: widget.theme == "L" ? ThemeColors.dark : ThemeColors.light,
          fontSize: h3,
          fontWeight: FontWeight.w600,
        ),
      ),
      trailing: ImageIcon(
        AssetImage(
            "assets/icons/${boxGlobal.get("iconSet")}/Standard_Moreleft.png"),
        color: widget.theme == "L" ? ThemeColors.dark : ThemeColors.light,
      ),
      onTap: onTap,
    );
  }

  Widget warningSelection(String title, String image, Function() onTap) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          color: Colors.red,
          fontSize: h3,
          fontWeight: FontWeight.w600,
        ),
      ),
      trailing: ImageIcon(AssetImage(
          "assets/icons/${boxGlobal.get("iconSet")}/Standard_Moreleft.png")),
      onTap: onTap,
    );
  }

  void deleteAccDialog() {
    AwesomeDialog(
      context: context,
      dialogType: DialogType.question,
      animType: AnimType.bottomSlide,
      dialogBackgroundColor: Colors.white,
      padding: EdgeInsets.all(defaultPadding),
      body: const Center(
        child: Text(
          "Are you sure you want to delete your account?",
          style: TextStyle(fontStyle: FontStyle.italic),
        ),
      ),
      btnOkColor: important_variables.projectName == "obriens"
          ? ThemeColors.secondaryDark
          : ThemeColors.primaryDark,
      btnOkOnPress: () {
        deleteAccount();
      },
      btnCancelColor: ThemeColors.disabled,
      btnCancelOnPress: () {},
    ).show();
  }

  void logoutDialog() {
    AwesomeDialog(
      context: context,
      dialogType: DialogType.question,
      animType: AnimType.bottomSlide,
      dialogBackgroundColor: Colors.white,
      padding: EdgeInsets.all(defaultPadding),
      body: const Center(
        child: Text(
          "Are you sure you want to logout?",
          style: TextStyle(fontStyle: FontStyle.italic),
        ),
      ),
      btnOkColor: important_variables.projectName == "obriens"
          ? ThemeColors.secondaryDark
          : ThemeColors.primaryDark,
      btnOkOnPress: () {
        logout();
      },
      btnCancelColor: ThemeColors.disabled,
      btnCancelOnPress: () {},
    ).show();
  }

  void deleteAccount() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    Map? data = await apiPost.meDeleteAccount();

    if (data != null) {
      if (data['code'] == 200) {
        Box box = await Hive.openBox('box');
        await box.deleteFromDisk();
        await Hive.openBox('box');
        EasyLoading.dismiss();

        defaultDialog(context, data['code'], data['message'], () {
          Navigator.pushNamedAndRemoveUntil(
            context,
            "/my_home_page",
            (Route<dynamic> route) => false,
            arguments: {'dailyPopUp': true},
          );
        }).show();
      } else {
        EasyLoading.dismiss();
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      EasyLoading.dismiss();
      defaultErrorDialog(context).show();
    }
  }

  void logout() async {
    if (Hive.isBoxOpen('box')) {
      Box box = Hive.box('box');
      await box.deleteFromDisk();
    }
    await Hive.openBox('box');

    if (Hive.isBoxOpen('boxMenu')) {
      Box boxMenu = Hive.box('boxMenu');
      await boxMenu.deleteFromDisk();
    }
    await Hive.openBox('boxMenu');

    if (context.mounted) {
      Navigator.pushNamedAndRemoveUntil(
        context,
        "/my_home_page",
        (Route<dynamic> route) => false,
        arguments: {'dailyPopUp': true},
      );
    }
  }

  Future<Map?> getSettings() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.settings();

    if (data != null && data['data'] != null) {
      Settings settings = Settings.fromJson(data["data"]["setting"]);
      pushNotification = settings.pushNotification == 1 ? true : false;
      sms = settings.sms == 1 ? true : false;
      email = settings.email == 1 ? true : false;
    }
    return data;
  }

  void updateSettings() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    Map? data = await apiPost.updateSettings(pushNotification, email, sms);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] != 200) {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
      setState(() {
        future = getSettings();
      });
    } else {
      defaultErrorDialog(context).show();
    }
  }
}
