import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield2.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class ChangePasswordTemplate2 extends StatefulWidget {
  static String routeName = "/change_password/2";

  const ChangePasswordTemplate2({Key? key}) : super(key: key);

  @override
  _ChangePasswordTemplate2State createState() =>
      _ChangePasswordTemplate2State();
}

class _ChangePasswordTemplate2State extends State<ChangePasswordTemplate2> {
  late Future future;
  late Box boxGlobal;
  String theme = resetPassAsset.theme;

  TextEditingController currentPassController = TextEditingController();
  TextEditingController newPassController = TextEditingController();
  TextEditingController confirmPassController = TextEditingController();

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    boxGlobal = await Hive.openBox("boxGlobal");
    return await Hive.openBox<Asset>('RESET_PASSWORD+MAIN+2');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                extendBodyBehindAppBar: true,
                appBar: AppBar(
                  title: Text(
                    "Change Password",
                    style: TextStyle(
                      color:
                          theme == "L" ? ThemeColors.dark : ThemeColors.light,
                    ),
                  ),
                  iconTheme: IconThemeData(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                ),
                body: Container(
                  height: phoneHeight,
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  child: SingleChildScrollView(
                    padding: EdgeInsets.fromLTRB(defaultPadding,
                        verticalPaddingLarge, defaultPadding, defaultPadding),
                    physics: const ClampingScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: spacingHeightMedium),
                        Text(
                          "Change Password",
                          style: TextStyle(
                            color: theme == "L"
                                ? ThemeColors.dark
                                : ThemeColors.light,
                            fontSize: h1,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: spacingHeightSmall),
                        Text(
                          "Fill in the blanks",
                          style: TextStyle(
                              color: theme == "L"
                                  ? ThemeColors.dark
                                  : ThemeColors.light,
                              fontSize: h3),
                        ),
                        SizedBox(height: spacingHeightMedium),
                        DefaultTextField2(
                          controller: currentPassController,
                          passwordField: true,
                          labelText: "Current Password",
                          hintText: "Please fill in your current password",
                        ),
                        DefaultTextField2(
                          controller: newPassController,
                          passwordField: true,
                          labelText: "New Password",
                          hintText: "Please fill in a new password",
                          helperText:
                              "At least 12 characters long but 14 or more, A combination of uppercase letters, lowercase letters, numbers, and symbols.",
                        ),
                        DefaultTextField2(
                          controller: confirmPassController,
                          passwordField: true,
                          labelText: "New Password Confirmation",
                          hintText: "Please confirm your password",
                        ),
                      ],
                    ),
                  ),
                ),
                bottomNavigationBar: Padding(
                  padding: EdgeInsets.fromLTRB(
                    defaultInnerPadding,
                    defaultInnerPadding,
                    defaultInnerPadding,
                    bottomPaddingWithoutBar,
                  ),
                  child: DefaultButton(
                    text: list.values
                            .firstWhereOrNull((element) =>
                                element.name == "SUBMIT_BUTTON_TEXT")
                            ?.data ??
                        "",
                    buttonColor: ThemeColors.primaryDark,
                    borderRadius: 15,
                    onPressed: () {
                      changePassword();
                    },
                  ),
                ),
              ),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }

  void changePassword() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    Map? data = await apiPost.meUpdatePassword(currentPassController.text,
        newPassController.text, confirmPassController.text);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          Navigator.of(context).pop();
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }
}
