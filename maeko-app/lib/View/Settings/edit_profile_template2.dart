import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/user.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_datetimefield1.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_dropdown1.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';
import 'package:collection/collection.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class EditProfileTemplate1 extends StatefulWidget {
  static String routeName = "/edit_profile/1";
  final dynamic args;

  const EditProfileTemplate1({Key? key, this.args}) : super(key: key);

  @override
  _EditProfileTemplate1State createState() => _EditProfileTemplate1State();
}

class _EditProfileTemplate1State extends State<EditProfileTemplate1> {
  late Future future;
  late Box box;
  late Box boxGlobal;
  Box? registerBox;
  late User user;
  String theme = "L";

  List<Gender> genderList = [
    Gender("F", "Female"),
    Gender("M", "Male"),
    Gender("-", "Rather not say"),
  ];
  String selectedGender = "-";
  TextEditingController phoneController = TextEditingController();
  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController dateController = TextEditingController();

  //race
  List<Race> raceList = [
    Race("CHINESE", "CHINESE"),
    Race("INDIAN", "INDIAN"),
    Race("MALAY", "MALAY"),
    Race("OTHERS", "OTHERS"),
  ];
  String selectedRace = "OTHERS";

  @override
  void initState() {
    super.initState();
    theme = widget.args["theme"];
    future = openBox();
  }

  openBox() async {
    box = await Hive.openBox("box");
    boxGlobal = await Hive.openBox("boxGlobal");

    user = box.get("user");
    selectedGender = user.gender ?? "-";
    selectedRace = user.race ?? "OTHERS";
    // phoneController.text = "${user.countryCode}${user.phone}";
    // nameController.text = user.name;
    // emailController.text = user.email ?? "";
    // dateController.text =
    //     user.dob == null ? "" : DateFormat('yyyy-MM-dd').format(user.dob!);

    int index =
        registerAsset.indexWhere((element) => element.sectionName == "STEP_1");
    // registerAsset.indexWhere((element) => element.sectionName == "STEP_3");
    registerBox = await Hive.openBox<Asset>(
        'REGISTER+STEP_1+${registerAsset[index].template}');
    // 'REGISTER+STEP_3+${registerAsset[index].template}');

    return await Hive.openBox<Asset>('ACCOUNT+EDIT_PROFILE+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                extendBodyBehindAppBar: true,
                appBar: AppBar(
                  title: Text(
                    list.values
                            .firstWhereOrNull(
                                (element) => element.name == "TITLE")
                            ?.data ??
                        "",
                    style: TextStyle(
                      color:
                          theme == "L" ? ThemeColors.dark : ThemeColors.light,
                    ),
                  ),
                  iconTheme: IconThemeData(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                ),
                body: Container(
                  height: phoneHeight,
                  decoration: background == null
                      ? BoxDecoration(
                          color: ThemeColors.light,
                        )
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  child: SingleChildScrollView(
                    padding: EdgeInsets.fromLTRB(defaultPadding,
                        verticalPaddingLarge, defaultPadding, defaultPadding),
                    physics: const ClampingScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: spacingHeightMedium),
                        Text(
                          "Edit Profile",
                          style: TextStyle(
                            color: ThemeColors.dark,
                            fontSize: h3,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: spacingHeightMedium),
                        Text(
                          "Fill in the blanks",
                          style:
                              TextStyle(color: ThemeColors.dark, fontSize: h4),
                        ),
                        SizedBox(height: spacingHeightMedium),
                        DefaultTextField1(
                          readOnly: true,
                          controller: phoneController,
                          hintText: "Phone Number",
                        ),
                        DefaultTextField1(
                          controller: nameController,
                          hintText: "Name",
                        ),
                        if (registerBox?.values
                                .firstWhereOrNull(
                                    (element) => element.name == "EMAIL")
                                .data ==
                            "1")
                          DefaultTextField1(
                            controller: emailController,
                            keyBoardType: TextInputType.emailAddress,
                            hintText: "Email (Optional)",
                          ),
                        if (registerBox?.values
                                .firstWhereOrNull(
                                    (element) => element.name == "GENDER")
                                .data ==
                            "1")
                          DefaultDropdown1(
                            selectedValue: selectedGender,
                            items: genderList.map((Gender value) {
                              return DropdownMenuItem<String>(
                                value: value.id,
                                child: Text(
                                  value.value,
                                  style: TextStyle(color: ThemeColors.dark),
                                ),
                              );
                            }).toList(),
                            onChanged: (String? value) {
                              setState(() {
                                selectedGender = value!;
                              });
                            },
                          ),
                        DefaultDropdown1(
                          selectedValue: selectedRace,
                          items: raceList.map((Race value) {
                            return DropdownMenuItem<String>(
                              value: value.id,
                              child: Text(
                                value.value,
                                style: TextStyle(color: ThemeColors.dark),
                              ),
                            );
                          }).toList(),
                          onChanged: (String? value) {
                            setState(() {
                              selectedRace = value!;
                            });
                          },
                        ),
                        if (registerBox?.values
                                .firstWhereOrNull((element) =>
                                    element.name == "DATE_OF_BIRTH")
                                .data ==
                            "1")
                          DefaultDateTimeField1(
                            hintText: "Date of Birth",
                            controller: dateController,
                            onTap: () {
                              selectDate();
                            },
                          ),
                        if (registerBox?.values
                                .firstWhereOrNull((element) =>
                                    element.name == "DATE_OF_BIRTH")
                                .data ==
                            "1")
                          Text(
                              "For exclusive promotions, please provide your email, gender and birthday",
                              style: TextStyle(
                                  color: ThemeColors.primaryDark,
                                  fontSize: h4)),
                      ],
                    ),
                  ),
                ),
                bottomNavigationBar: Padding(
                  padding: EdgeInsets.fromLTRB(
                    defaultInnerPadding,
                    defaultInnerPadding,
                    defaultInnerPadding,
                    bottomPaddingWithoutBar,
                  ),
                  child: DefaultButton(
                    text: list.values
                            .firstWhereOrNull(
                                (element) => element.name == "SAVE_BUTTON_TEXT")
                            ?.data ??
                        "Submit",
                    buttonColor: important_variables.projectName == "obriens"
                        ? ThemeColors.secondaryDark
                        : ThemeColors.primaryDark,
                    onPressed: () {
                      editProfile();
                    },
                  ),
                ),
              ),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }

  void editProfile() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    String date = dateController.text;
    if (date == DateFormat('yyyy-MM-dd').format(DateTime.now())) {
      date = "";
    }

    Map? data = await apiPost.meUpdate(nameController.text, selectedGender,
        selectedRace, date, emailController.text);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        User user = User.fromJson(data['data']['me']);
        await box.put("user", user);

        defaultDialog(context, data['code'], data['message'], () {
          Navigator.of(context).pop();
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  void selectDate() async {
    if (dateController.text.isEmpty)
      dateController.text = DateFormat('yyyy-MM-dd').format(DateTime.now());

    final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: DateTime.parse(dateController.text),
        firstDate: DateTime(DateTime.now().year - 100),
        lastDate: DateTime.now());
    if (picked != null) {
      setState(() {
        dateController.text = DateFormat('yyyy-MM-dd').format(picked);
      });
    }
  }
}

class Gender {
  String id;
  String value;

  Gender(this.id, this.value);
}

class Race {
  String id;
  String value;

  Race(this.id, this.value);
}
