import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/points_history.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Custom%20Widgets/empty_list.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';
import 'package:rxdart/rxdart.dart';

class PointHistoryPage extends StatefulWidget {
  const PointHistoryPage({Key? key}) : super(key: key);

  @override
  _PointsHistoryPageState createState() => _PointsHistoryPageState();
}

class _PointsHistoryPageState extends State<PointHistoryPage> {
  Box boxGlobal = Hive.box("boxGlobal");

  final ScrollController receivedScrollController = ScrollController();
  bool receivedHasMorePages = false;
  int receivedNextPage = 0;
  StreamController receivedStreamController = BehaviorSubject();
  List<PointsHistory> receivedList = [];

  final ScrollController usedScrollController = ScrollController();
  bool usedHasMorePages = false;
  int usedNextPage = 0;
  final StreamController usedStreamController = BehaviorSubject();
  List<PointsHistory> usedList = [];

  final ScrollController expiredScrollController = ScrollController();
  bool expiredHasMorePages = false;
  int expiredNextPage = 0;
  final StreamController expiredStreamController = BehaviorSubject();
  List<PointsHistory> expiredList = [];

  @override
  void initState() {
    super.initState();
    getPointsHistory("gained", receivedNextPage);
    getPointsHistory("used", usedNextPage);
    getPointsHistory("expired", expiredNextPage);

    receivedScrollController.addListener(() {
      if (receivedScrollController.position.pixels ==
          receivedScrollController.position.maxScrollExtent) {
        if (receivedHasMorePages) getPointsHistory("gained", receivedNextPage);
      }
    });

    usedScrollController.addListener(() {
      if (usedScrollController.position.pixels ==
          usedScrollController.position.maxScrollExtent) {
        if (usedHasMorePages) getPointsHistory("used", usedNextPage);
      }
    });

    expiredScrollController.addListener(() {
      if (expiredScrollController.position.pixels ==
          expiredScrollController.position.maxScrollExtent) {
        if (expiredHasMorePages) getPointsHistory("expired", expiredNextPage);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
          backgroundColor: ThemeColors.light,
          //todo get from asset
          appBar: AppBar(title: const Text("Coins History")),
          body: Column(
            children: [
              SizedBox(height: defaultPadding),
              TabBar(
                padding: EdgeInsets.symmetric(horizontal: defaultPadding),
                labelColor: ThemeColors.light,
                indicator: BoxDecoration(
                  borderRadius: BorderRadius.circular(30),
                  color: ThemeColors.primaryDark,
                ),
                indicatorSize: TabBarIndicatorSize.tab,
                dividerColor: Colors.transparent,
                unselectedLabelColor: ThemeColors.dark,
                unselectedLabelStyle: TextStyle(color: ThemeColors.light),
                tabs: const [
                  //todo get from asset
                  Tab(text: "Coins received"),
                  Tab(text: "Coins used"),
                  Tab(text: "Coins expired"),
                ],
              ),
              Expanded(
                child: TabBarView(
                  children: [
                    pointsList("gained", receivedStreamController.stream,
                        receivedScrollController, receivedList),
                    pointsList("used", usedStreamController.stream,
                        usedScrollController, usedList),
                    pointsList("expired", expiredStreamController.stream,
                        expiredScrollController, expiredList),
                  ],
                ),
              ),
            ],
          )),
    );
  }

  Widget pointsList(String filter, Stream stream, ScrollController controller,
      List<PointsHistory> list) {
    return StreamBuilder(
        stream: stream,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.hasError) {
            return Container();
          } else {
            if (list.isEmpty) {
              return EmptyList(theme: "L");
            } else {
              return ListView.builder(
                controller: controller,
                padding: EdgeInsets.all(defaultPadding),
                itemCount: list.length,
                itemBuilder: (BuildContext context, int index) {
                  return Container(
                    margin: EdgeInsets.only(bottom: defaultInnerPadding),
                    padding: EdgeInsets.all(defaultInnerPadding),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: ThemeColors.disabled),
                    ),
                    child: Row(
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AutoSizeText(
                              list[index].description,
                              style: TextStyle(
                                color: ThemeColors.dark,
                                fontSize: h3,
                              ),
                              maxLines: 2,
                            ),
                            SizedBox(height: spacingHeightSmall),
                            Text(
                              DateFormat('d MMM yyyy HH:mm')
                                  .format(list[index].createdAt),
                              style: TextStyle(
                                  color: ThemeColors.gray, fontSize: h4),
                            ),
                          ],
                        ),
                        const Spacer(),
                        Text(
                          "${list[index].difference} coins",
                          style: TextStyle(
                            color:
                                filter == "gained" ? Colors.green : Colors.red,
                            fontSize: h3,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
            }
          }
        });
  }

  Future<Map?> getPointsHistory(String filter, int nextPage) async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.pointHistory(filter, nextPage);

    if (data != null) {
      List<PointsHistory> newList = (data['data']['points'] as List)
          .map((data) => PointsHistory.fromJson(data))
          .toList();
      if (filter == "gained") {
        receivedHasMorePages = data["data"]["has_more_pages"];
        if (receivedHasMorePages) {
          receivedNextPage = data["data"]["next_page"] ?? 0;
        }
        receivedList.addAll(newList);
        receivedStreamController.add(data);
      } else if (filter == "used") {
        usedHasMorePages = data["data"]["has_more_pages"];
        if (usedHasMorePages) {
          usedNextPage = data["data"]["next_page"] ?? 0;
        }
        usedList.addAll(newList);
        usedStreamController.add(data);
      } else {
        expiredHasMorePages = data["data"]["has_more_pages"];
        if (expiredHasMorePages) {
          expiredNextPage = data["data"]["next_page"] ?? 0;
        }
        expiredList.addAll(newList);
        expiredStreamController.add(data);
      }
    }
    return data;
  }
}
