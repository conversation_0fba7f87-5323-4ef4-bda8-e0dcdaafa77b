import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/product.dart';
import 'package:amverton/Repository/my_navigator_observer.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Home/product_list.dart';
import 'package:hive/hive.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class MenuDetailTemplate1 extends StatefulWidget {
  final Product product;
  final List<Product> recommended;
  final String? inviteMsg;
  final String? inviteUrl;
  final Object tag;

  const MenuDetailTemplate1({
    Key? key,
    required this.product,
    required this.recommended,
    this.inviteMsg,
    this.inviteUrl,
    required this.tag,
  }) : super(key: key);

  @override
  _MenuDetailTemplate1State createState() => _MenuDetailTemplate1State();
}

class _MenuDetailTemplate1State extends State<MenuDetailTemplate1> {
  // late Future future;
  Box boxGlobal = Hive.box("boxGlobal");

  // openBox() async {
  //   return await Hive.openBox<Asset>("MENU+MAIN+1");
  // }

  // @override
  // void initState() {
  //   super.initState();
  //   future = openBox();
  // }

  @override
  Widget build(BuildContext context) {
    // return FutureBuilder(
    //   future: future,
    //   builder: (BuildContext context, AsyncSnapshot snapshot) {
    //     if (snapshot.connectionState == ConnectionState.done) {
    //       final Box list = snapshot.data;
    //       list.watch();

    return Scaffold(
      backgroundColor: ThemeColors.light,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        iconTheme: IconThemeData(color: ThemeColors.light),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(top: verticalPaddingLarge),
              decoration: const BoxDecoration(
                  //todo temporary hardcode bg color
                  color: const Color(0xff20222B)),
              child: Container(
                margin: EdgeInsets.fromLTRB(defaultPadding, defaultPadding,
                    defaultPadding, verticalPaddingSmall),
                padding: EdgeInsets.all(defaultPadding),
                width: phoneWidth,
                decoration: BoxDecoration(
                  color: ThemeColors.light,
                  border: Border.all(color: ThemeColors.disabled),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  children: [
                    Hero(
                      tag: widget.tag,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: CachedNetworkImage(
                          imageUrl: widget.product.image ?? "",
                          placeholder: (context, url) {
                            return Padding(
                              padding: EdgeInsets.all(defaultPadding),
                              child: CircularProgressIndicator(
                                color: ThemeColors.primaryDark,
                              ),
                            );
                          },
                          errorWidget: (context, url, error) {
                            return Padding(
                              padding: EdgeInsets.all(defaultPadding),
                              child: Icon(
                                Icons.error_outline_outlined,
                                color: ThemeColors.primaryDark,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    SizedBox(height: spacingHeightMedium),
                    Text(
                      widget.product.name,
                      style: TextStyle(
                        color: ThemeColors.dark,
                        fontSize: h1,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: spacingHeightMedium),
                    Text(
                      widget.product.description!,
                      textAlign: TextAlign.center,
                      style: TextStyle(color: ThemeColors.dark, fontSize: h3),
                    ),
                    SizedBox(height: spacingHeightMedium),
                    Text(
                      important_variables.currency + widget.product.price!,
                      style: TextStyle(
                        color: ThemeColors.primaryDark,
                        fontSize: h1,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Container(
              color: ThemeColors.disabled,
              height: defaultInnerPadding,
            ),
            if (widget.recommended.isNotEmpty) recommendedProductView(),
          ],
        ),
      ),
    );
    //     }
    //     return Container();
    //   },
    // );
  }

  Widget recommendedProductView() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: defaultPadding),
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(
              left: defaultPadding,
              right: defaultPadding,
              bottom: defaultInnerPadding,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Recommended",
                  style: TextStyle(
                    color: ThemeColors.dark,
                    fontSize: h2,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (BuildContext context) => ProductList(
                            appBarTitle: "Recommended",
                            products: widget.recommended,
                          ),
                        ));
                  },
                  child: Text(
                    "View More",
                    style: TextStyle(
                      color: ThemeColors.dark,
                      fontSize: h3,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SingleChildScrollView(
            padding: EdgeInsets.only(left: defaultPadding),
            scrollDirection: Axis.horizontal,
            child: Row(
              children: List.generate(
                  widget.recommended.length > 5 ? 4 : widget.recommended.length,
                  (index) {
                return GestureDetector(
                  onTap: () {
                    getProductDetail(widget.recommended[index].id);
                  },
                  child: Container(
                    margin: EdgeInsets.only(right: spacingWidth),
                    width: phoneWidth / 3,
                    height: phoneHeight / 4.25,
                    decoration: BoxDecoration(
                      color: ThemeColors.light,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: ThemeColors.disabled),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Hero(
                          tag:
                              "${widget.tag}/recommended/${widget.recommended[index].id}",
                          child: ClipRRect(
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(20),
                              topRight: Radius.circular(20),
                            ),
                            child: CachedNetworkImage(
                              imageUrl: widget.recommended[index].image ?? "",
                              height: phoneWidth / 3,
                              width: phoneWidth / 3,
                              fit: BoxFit.cover,
                              placeholder: (context, url) {
                                return Center(
                                  child: CircularProgressIndicator(
                                    color: ThemeColors.primaryDark,
                                  ),
                                );
                              },
                              errorWidget: (context, url, error) {
                                return Icon(
                                  Icons.error_outline_outlined,
                                  color: ThemeColors.primaryDark,
                                );
                              },
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.recommended[index].name,
                                style: TextStyle(
                                    color: ThemeColors.dark, fontSize: h4),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              SizedBox(height: spacingHeightSmall),
                              Text(
                                important_variables.currency +
                                    widget.recommended[index].price!,
                                style: TextStyle(
                                    color: ThemeColors.primaryDark,
                                    fontSize: h3),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  getProductDetail(int productId) async {
    EasyLoading.show();
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.productDetail(productId);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        Product product = Product.fromJson(data['data']['product']);
        List<Product> recommended =
            (data['data']['recommended_products'] as List)
                .map((data) => Product.fromJson(data))
                .toList();
        String? inviteMsg = data['data']['invite_message'] ?? null;
        String? inviteUrl = data['data']['invite_url'] ?? null;

        MaterialPageRoute route = MaterialPageRoute(
            builder: (BuildContext context) => MenuDetailTemplate1(
                  product: product,
                  recommended: recommended,
                  inviteMsg: inviteMsg,
                  inviteUrl: inviteUrl,
                  tag: "${widget.tag}/recommended/${product.id}",
                ),
            settings:
                RouteSettings(name: "MenuDetail/recommended/${product.id}"));

        Route? routeStack = routeStacks.firstWhere(
            (element) =>
                element!.settings.name ==
                "MenuDetail/recommended/${product.id}",
            orElse: () => null);

        try {
          if (routeStack != null) {
            Navigator.removeRoute(context, routeStack);
          }
        } catch (e) {
          print(e);
          print("something wrong");
          // print("current want to go:${route}");
          // for(Route? routestack1 in routeStacks){
          //   print("in the list:${routestack1}");
          // }
          //
          // for(Route? routestack1 in routeStacks){
          //   print("in the list2:${routestack1}");
          // }
        }

        Navigator.push(context, route);
      }
    }
  }
}
