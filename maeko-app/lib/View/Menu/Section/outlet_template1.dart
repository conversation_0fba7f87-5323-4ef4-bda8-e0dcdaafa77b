import 'dart:async';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/bottom_bar.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_search_bar.dart';
import 'package:amverton/main.dart';
import 'package:hive/hive.dart';
import 'package:amverton/View/Menu/menu_outlet.dart';
import 'package:collection/collection.dart';

class OutletTemplate1 extends StatefulWidget {
  final String theme;

  const OutletTemplate1({Key? key, required this.theme}) : super(key: key);

  @override
  _OutletTemplate1State createState() => _OutletTemplate1State();
}

class _OutletTemplate1State extends State<OutletTemplate1> {
  late Future boxFuture;
  late Box boxGlobal;

  bool onSearch = false;
  List<Outlet> searchList = [];
  TextEditingController searchController = TextEditingController();
  final StreamController streamController = StreamController.broadcast();

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();

    searchController.addListener(() {
      streamController.add(searchController.text);
    });
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>("MENU+MAIN+1");
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return Container(
              width: phoneWidth,
              height: phoneHeight,
              decoration: background == null
                  ? null
                  : BoxDecoration(
                      image: backgroundType == "IMAGE"
                          ? DecorationImage(
                              image: CachedNetworkImageProvider(
                                  background.data ?? ""),
                              fit: BoxFit.cover,
                            )
                          : null,
                      color: backgroundType == "COLOR"
                          ? Color(
                              int.parse("0xff${background.data ?? "FFFFFF"}"))
                          : null,
                    ),
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: EdgeInsets.fromLTRB(
                    defaultPadding,
                    topPaddingWithoutBar,
                    defaultPadding,
                    bottomPaddingWithoutBar),
                child: Column(
                  children: [
                    DefaultSearchBar(
                      controller: searchController,
                      hintText: "Search",
                      onChanged: (value) {
                        if (value.isEmpty && onSearch) {
                          setState(() {
                            onSearch = false;
                          });
                        } else if (value.isNotEmpty && !onSearch) {
                          setState(() {
                            onSearch = true;
                          });
                        }
                      },
                    ),
                    SizedBox(height: spacingHeightMedium),
                    Visibility(
                      visible: !onSearch,
                      child: FutureBuilder(
                          future: future,
                          builder:
                              (BuildContext context, AsyncSnapshot snapshot) {
                            if (snapshot.connectionState ==
                                ConnectionState.done) {
                              return Column(
                                children: [
                                  Text(
                                    "Choose an Outlet",
                                    style: TextStyle(
                                        color: ThemeColors.dark,
                                        fontSize: h2,
                                        fontWeight: FontWeight.bold),
                                  ),
                                  SizedBox(height: spacingHeightMedium),
                                  ListView.builder(
                                    padding: EdgeInsets.zero,
                                    physics: NeverScrollableScrollPhysics(),
                                    shrinkWrap: true,
                                    itemCount: outletList.length,
                                    itemBuilder:
                                        (BuildContext context, int index) {
                                      return outletView(outletList[index]);
                                    },
                                  ),
                                ],
                              );
                            }
                            return Container();
                          }),
                    ),
                    Visibility(
                      visible: onSearch,
                      child: StreamBuilder(
                        stream: streamController.stream.asBroadcastStream(),
                        builder:
                            (BuildContext context, AsyncSnapshot snapshot) {
                          if (snapshot.hasData) {
                            return Column(
                              children: [
                                Text(
                                  "Search Result",
                                  style: TextStyle(
                                      color: ThemeColors.dark,
                                      fontSize: h2,
                                      fontWeight: FontWeight.bold),
                                ),
                                SizedBox(height: spacingHeightMedium),
                                FutureBuilder(
                                  future: getOutletSearch(snapshot.data),
                                  builder: (BuildContext context,
                                      AsyncSnapshot snapshot) {
                                    if (snapshot.connectionState ==
                                        ConnectionState.done) {
                                      searchList = (snapshot.data['data']
                                              ['outlets'] as List)
                                          .map((data) => Outlet.fromJson(data))
                                          .toList();

                                      return Column(
                                          children: List.generate(
                                              searchList.length, (index) {
                                        return outletView(searchList[index]);
                                      }));
                                    }
                                    return SizedBox(
                                      height: phoneHeight / 3,
                                      child: Center(
                                        child: CircularProgressIndicator(
                                            color: ThemeColors.primaryDark),
                                      ),
                                    );
                                  },
                                ),
                              ],
                            );
                          }
                          return Container();
                        },
                      ),
                    ),
                  ],
                ),
              ),
            );
          }
          return Container();
        });
  }

  Widget outletView(Outlet outlet) {
    return GestureDetector(
        onTap: () async {
          List<BottomBar> bottomBarList = boxGlobal.get("bottomBar") ?? [];
          if (bottomBarList.isNotEmpty) {
            int menuIndex =
                bottomBarList.indexWhere((element) => element.name == "MENU");
            if (menuIndex != -1) {
              Box boxMenu = await Hive.openBox('boxMenu');
              await boxMenu.put("selected_outlet", outlet);

              defaultBar[menuIndex] = MenuOutlet(outlet: outlet);
              final dynamic navigationBar = barGlobalKey.currentWidget;
              navigationBar.onTap(menuIndex);
            }
          }
        },
        child: Container(
          margin: EdgeInsets.only(bottom: defaultInnerPadding),
          padding: EdgeInsets.all(defaultInnerPadding),
          width: phoneWidth,
          decoration: BoxDecoration(
            color: ThemeColors.light,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: ThemeColors.disabled),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: phoneWidth / 1.8,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AutoSizeText(
                      outlet.name,
                      style: TextStyle(
                          color: ThemeColors.dark,
                          fontSize: h3,
                          fontWeight: FontWeight.bold),
                      maxLines: 2,
                    ),
                    SizedBox(height: spacingHeightMedium),
                    if (outlet.phone != null)
                      AutoSizeText(
                        outlet.phone!,
                        style: TextStyle(color: ThemeColors.dark, fontSize: h4),
                        maxLines: 1,
                      ),
                    SizedBox(height: spacingHeightSmall),
                    AutoSizeText(
                      outlet.address,
                      style: TextStyle(color: ThemeColors.gray, fontSize: h4),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
              Container(
                width: phoneWidth / 4,
                height: phoneWidth / 4,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: CachedNetworkImage(
                    imageUrl: outlet.image ?? "",
                    fit: BoxFit.cover,
                    placeholder: (context, url) {
                      return Center(
                        child: CircularProgressIndicator(
                          color: ThemeColors.primaryDark,
                        ),
                      );
                    },
                    errorWidget: (context, url, error) {
                      return Icon(
                        Icons.error_outline_outlined,
                        color: ThemeColors.primaryDark,
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ));
  }
}
