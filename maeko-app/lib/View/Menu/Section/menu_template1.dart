import 'dart:async';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/bottom_bar.dart';
import 'package:amverton/Model/product.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_search_bar.dart';
import 'package:amverton/View/Menu/menu_outlet.dart' as MenuOutlet;
import 'package:amverton/View/Menu/menu_page.dart';
import 'package:amverton/main.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class MenuTemplate1 extends StatefulWidget {
  const MenuTemplate1({Key? key}) : super(key: key);

  @override
  _MenuTemplate1State createState() => _MenuTemplate1State();
}

class _MenuTemplate1State extends State<MenuTemplate1> {
  late Future boxFuture;
  late Box box;
  late Box boxGlobal;
  late Box boxMenu;

  Outlet? outlet;
  int selectedIndex = 0;
  bool onSearch = false;
  List<Product> searchList = [];
  PageController pageController = PageController();
  TextEditingController searchController = TextEditingController();
  final StreamController streamController = StreamController.broadcast();

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();

    pageController = PageController();
    searchController.addListener(() {
      streamController.add(searchController.text);
    });
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    boxMenu = await Hive.openBox('boxMenu');
    outlet = await boxMenu.get("selected_outlet");

    return await Hive.openBox<Asset>('MENU+MAIN+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return Container(
              decoration: background == null
                  ? null
                  : BoxDecoration(
                      image: backgroundType == "IMAGE"
                          ? DecorationImage(
                              image: CachedNetworkImageProvider(
                                  background.data ?? ""),
                              fit: BoxFit.cover,
                            )
                          : null,
                      color: backgroundType == "COLOR"
                          ? Color(
                              int.parse("0xff${background.data ?? "FFFFFF"}"))
                          : null,
                    ),
              padding: EdgeInsets.only(top: topPaddingWithoutBar),
              width: phoneWidth,
              child: Column(
                children: [
                  header(list),
                  Visibility(
                    visible: !onSearch,
                    child: FutureBuilder(
                        future: future,
                        builder:
                            (BuildContext context, AsyncSnapshot snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.done) {
                            return Expanded(
                              child: Row(
                                children: [
                                  category(),
                                  item(),
                                ],
                              ),
                            );
                          }
                          return Container();
                        }),
                  ),
                  Visibility(
                    visible: onSearch,
                    child: StreamBuilder(
                      stream: streamController.stream.asBroadcastStream(),
                      builder: (BuildContext context, AsyncSnapshot snapshot) {
                        if (snapshot.hasData) {
                          return Expanded(
                            child: SingleChildScrollView(
                              child: Column(
                                children: [
                                  Text(
                                    "Search Result",
                                    style: TextStyle(
                                        color: menuTheme == "L"
                                            ? ThemeColors.dark
                                            : ThemeColors.light,
                                        fontSize: h2,
                                        fontWeight: FontWeight.bold),
                                  ),
                                  SizedBox(height: spacingHeightMedium),
                                  FutureBuilder(
                                    future: getMenuSearch(snapshot.data),
                                    builder: (BuildContext context,
                                        AsyncSnapshot snapshot) {
                                      if (snapshot.data != null &&
                                          snapshot.data['data'] != null) {
                                        searchList = (snapshot.data['data']
                                                ['products'] as List)
                                            .map((data) =>
                                                Product.fromJson(data))
                                            .toList();

                                        return searchItem();
                                      }
                                      return SizedBox(
                                        height: phoneHeight / 3,
                                        child: Center(
                                          child: CircularProgressIndicator(
                                              color: ThemeColors.primaryDark),
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ),
                          );
                        }
                        return Container();
                      },
                    ),
                  ),
                ],
              ),
            );
          }
          return Container();
        });
  }

  Widget header(Box list) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: defaultPadding),
          child: Column(
            children: [
              DefaultSearchBar(
                  controller: searchController,
                  hintText: "Search",
                  onChanged: (value) {
                    if (value.isEmpty && onSearch) {
                      setState(() {
                        onSearch = false;
                      });
                    } else if (value.isNotEmpty && !onSearch) {
                      setState(() {
                        onSearch = true;
                      });
                    }
                  }),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ImageIcon(
                    AssetImage(
                        "assets/icons/${boxGlobal.get("iconSet")}/Location 03.png"),
                    color:
                        menuTheme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.symmetric(horizontal: spacingWidth),
                      child: Text(
                        outlet?.name ?? "",
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          color: menuTheme == "L"
                              ? ThemeColors.dark
                              : ThemeColors.light,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: phoneWidth / 4,
                    child: DefaultButton(
                      text: list.values
                              .firstWhereOrNull((element) =>
                                  element.name == "CHANGE_BUTTON_TEXT")
                              ?.data ??
                          "",
                      buttonColor: ThemeColors.primaryDark,
                      onPressed: () async {
                        List<BottomBar> bottomBarList =
                            boxGlobal.get("bottomBar") ?? [];
                        if (bottomBarList.isNotEmpty) {
                          int index = bottomBarList
                              .indexWhere((element) => element.name == "MENU");

                          if (index != -1) {
                            await boxMenu.delete("selected_outlet");
                            defaultBar[index] = MenuOutlet.MenuOutlet();
                            final dynamic navigationBar =
                                barGlobalKey.currentWidget;
                            navigationBar.onTap(index);
                          }
                        }
                      },
                    ),
                  ),
                ],
              ),
              SizedBox(height: spacingHeightSmall),
            ],
          ),
        ),
      ],
    );
  }

  Widget category() {
    return Container(
        decoration: BoxDecoration(
          border: Border(
            right: BorderSide(
              color: menuTheme == "L" ? ThemeColors.disabled : ThemeColors.gray,
              width: 3,
            ),
          ),
        ),
        width: phoneWidth / 4,
        child: ListView.builder(
          padding: EdgeInsets.zero,
          itemCount: categoryList.length,
          itemBuilder: (BuildContext context, int index) {
            return GestureDetector(
                onTap: () {
                  setState(() {
                    selectedIndex = index;
                    pageController.jumpToPage(index);
                  });
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: (selectedIndex == index)
                        ? ThemeColors.primaryDark
                        : menuTheme == "L"
                            ? ThemeColors.light
                            : ThemeColors.dark,
                    border: Border(
                      bottom: BorderSide(
                        color: orderTheme == "L"
                            ? ThemeColors.disabled
                            : ThemeColors.gray,
                      ),
                    ),
                  ),
                  height: categoryList[index].icon == null
                      ? phoneHeight / 13
                      : phoneHeight / 8,
                  padding: EdgeInsets.symmetric(
                    horizontal: spacingHeightSmall,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (categoryList[index].icon != null)
                        Container(
                          alignment: Alignment.center,
                          padding: EdgeInsets.only(bottom: spacingHeightSmall),
                          child: CachedNetworkImage(
                            imageUrl: categoryList[index].icon!,
                            width: phoneWidth / 12,
                            height: phoneWidth / 12,
                            placeholder: (context, url) {
                              return Center(
                                child: CircularProgressIndicator(
                                  color: (selectedIndex == index)
                                      ? ThemeColors.light
                                      : menuTheme == "L"
                                          ? ThemeColors.dark
                                          : ThemeColors.light,
                                ),
                              );
                            },
                            errorWidget: (context, url, error) {
                              return Icon(
                                Icons.error_outline_outlined,
                                color: (selectedIndex == index)
                                    ? ThemeColors.light
                                    : menuTheme == "L"
                                        ? ThemeColors.dark
                                        : ThemeColors.light,
                              );
                            },
                          ),
                        ),
                      Text(
                        categoryList[index].name,
                        style: TextStyle(
                          color: (selectedIndex == index)
                              ? ThemeColors.light
                              : menuTheme == "L"
                                  ? ThemeColors.dark
                                  : ThemeColors.light,
                          height: 1.25,
                          fontSize: h4,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ));
          },
        ));
  }

  Widget item() {
    return Expanded(
      child: PageView(
        physics: const NeverScrollableScrollPhysics(),
        controller: pageController,
        children: List.generate(categoryList.length, (index) {
          return SingleChildScrollView(
            padding: EdgeInsets.only(
              left: defaultPadding,
              top: defaultPadding,
              bottom: defaultPadding,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  categoryList[index].name,
                  style: TextStyle(
                      color: menuTheme == "L"
                          ? ThemeColors.dark
                          : ThemeColors.light,
                      fontSize: h2,
                      fontWeight: FontWeight.bold),
                ),
                GridView.builder(
                    padding: EdgeInsets.only(top: defaultInnerPadding),
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      mainAxisExtent: phoneHeight / 3.5,
                      mainAxisSpacing: 10,
                    ),
                    shrinkWrap: true,
                    itemCount: categoryList[selectedIndex].products.length,
                    itemBuilder: (BuildContext ctx, index) {
                      return GestureDetector(
                        onTap: () {
                          MenuPage.getProductDetail(
                            context,
                            categoryList[selectedIndex].products[index].id,
                            "menu/",
                          );
                        },
                        child: Container(
                          margin: EdgeInsets.only(right: spacingWidth),
                          decoration: BoxDecoration(
                            color: ThemeColors.light,
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(color: ThemeColors.disabled),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Hero(
                                tag:
                                    "menu/${categoryList[selectedIndex].products[index].id}",
                                child: ClipRRect(
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(20),
                                    topRight: Radius.circular(20),
                                  ),
                                  child: CachedNetworkImage(
                                    imageUrl: categoryList[selectedIndex]
                                            .products[index]
                                            .image ??
                                        "",
                                    height: phoneWidth / 3,
                                    width: phoneWidth / 3,
                                    fit: BoxFit.cover,
                                    placeholder: (context, url) {
                                      return Center(
                                        child: CircularProgressIndicator(
                                          color: ThemeColors.primaryDark,
                                        ),
                                      );
                                    },
                                    errorWidget: (context, url, error) {
                                      return Icon(
                                        Icons.error_outline_outlined,
                                        color: ThemeColors.primaryDark,
                                      );
                                    },
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(8),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    AutoSizeText(
                                      categoryList[selectedIndex]
                                          .products[index]
                                          .name,
                                      style: TextStyle(
                                          color: ThemeColors.dark,
                                          fontSize: h4),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    SizedBox(height: spacingHeightSmall),
                                    Text(
                                      important_variables.currency +
                                          categoryList[selectedIndex]
                                              .products[index]
                                              .price!,
                                      style: TextStyle(
                                        color: ThemeColors.primaryDark,
                                        fontSize: h3,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                      );
                    }),
                SizedBox(height: spacingHeightMedium),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget searchItem() {
    return GridView.builder(
      padding: EdgeInsets.only(
        bottom: defaultInnerPadding,
        left: defaultPadding,
      ),
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        mainAxisExtent: phoneHeight / 4.25,
        mainAxisSpacing: 10,
      ),
      shrinkWrap: true,
      itemCount: searchList.length,
      itemBuilder: (BuildContext ctx, index) {
        return GestureDetector(
          onTap: () {
            MenuPage.getProductDetail(
                context, searchList[index].id, "menu/search/");
          },
          child: Container(
            margin: EdgeInsets.only(right: spacingWidth),
            decoration: BoxDecoration(
              color: ThemeColors.light,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: ThemeColors.disabled),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Hero(
                  tag: "menu/search/${searchList[index].id}",
                  child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                    child: CachedNetworkImage(
                      imageUrl: searchList[index].image ?? "",
                      height: phoneWidth / 3.5,
                      width: phoneWidth / 3.5,
                      fit: BoxFit.cover,
                      placeholder: (context, url) {
                        return Center(
                          child: CircularProgressIndicator(
                            color: ThemeColors.primaryDark,
                          ),
                        );
                      },
                      errorWidget: (context, url, error) {
                        return Icon(
                          Icons.error_outline_outlined,
                          color: ThemeColors.primaryDark,
                        );
                      },
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AutoSizeText(
                        searchList[index].name,
                        style: TextStyle(color: ThemeColors.dark, fontSize: h4),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: spacingHeightSmall),
                      Text(
                        important_variables.currency + searchList[index].price!,
                        style: TextStyle(
                          color: ThemeColors.primaryDark,
                          fontSize: h3,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }
}
