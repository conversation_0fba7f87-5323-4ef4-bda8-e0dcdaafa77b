import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/location_service.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Menu/menu_page.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hive/hive.dart';

class MenuOutlet extends StatefulWidget {
  Outlet? outlet;

  MenuOutlet({Key? key, this.outlet}) : super(key: key);

  @override
  _MenuOutletState createState() => _MenuOutletState();
}

List<Outlet> outletList = [];
late Future future;

class _MenuOutletState extends State<MenuOutlet> {
  late Future boxFuture;
  late Box boxGlobal;
  late Box boxMenu;

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();
    future = getOutlet();
  }

  openBox() async {
    boxMenu = await Hive.openBox('boxMenu');
    boxGlobal = await Hive.openBox('boxGlobal');

    Outlet? selectedOutlet = boxMenu.get("selected_outlet") ?? null;
    if (selectedOutlet != null) {
      setState(() {
        widget.outlet = selectedOutlet;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.outlet != null
        ? MenuPage(outlet: widget.outlet!)
        : FutureBuilder(
            future: boxFuture,
            builder: (BuildContext context, AsyncSnapshot snapshot) {
              if (snapshot.connectionState == ConnectionState.done) {
                return GestureDetector(
                  onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
                  behavior: HitTestBehavior.translucent,
                  child: Scaffold(
                    body: RefreshIndicator(
                      color: ThemeColors.primaryDark,
                      onRefresh: () {
                        return Future.delayed(const Duration(seconds: 1), () {
                          setState(() {
                            future = getOutlet();
                          });
                        });
                      },
                      child: Stack(
                        children: [
                          menuMainWidget ?? Container(),
                        ],
                      ),
                    ),
                  ),
                );
              }
              return Container();
            });
  }

  Future<Map?> getOutlet() async {
    ApiGet apiGet = ApiGet();

    // check location permission
    Position? currentPosition;
    if (Platform.isIOS) {
      currentPosition = await LocationService.getCurrentPosition();
    } else {
      Position? permissionCheck =
          await LocationService.checkAndroidLocationPermission();
      if (permissionCheck != null)
        currentPosition = await LocationService.getCurrentPosition();
    }

    Map? data = await apiGet.outletList(
        currentPosition?.latitude, currentPosition?.longitude);

    if (data != null && data['data'] != null) {
      outletList = (data['data']['outlets'] as List)
          .map((data) => Outlet.fromJson(data))
          .toList();
    }
    return data;
  }
}

Future<Map?> getOutletSearch(String? searchValue) async {
  ApiGet apiGet = ApiGet();
  Map? data = await apiGet.outletListSearch(searchValue);
  return data;
}
