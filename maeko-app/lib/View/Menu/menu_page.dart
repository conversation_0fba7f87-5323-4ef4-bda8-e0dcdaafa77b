import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Model/menu.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Model/product.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Menu/Section/menu_template1.dart';
import 'package:amverton/View/Menu/menu_detail_template1.dart';

class MenuPage extends StatefulWidget {
  final Outlet outlet;

  const MenuPage({
    Key? key,
    required this.outlet,
  }) : super(key: key);

  @override
  _MenuPageState createState() => _MenuPageState();

  static void getProductDetail(
      BuildContext context, int productId, String tag) {
    _MenuPageState? state = context.findAncestorStateOfType<_MenuPageState>();
    state?.getProductDetail(productId, tag);
  }
}

late Future future;
List<MenuCategory> categoryList = [];
Outlet? outlet;

class _MenuPageState extends State<MenuPage> {
  @override
  void initState() {
    super.initState();
    outlet = widget.outlet;
    future = getMenu();
  }

  @override
  void didUpdateWidget(covariant MenuPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    future = getMenu();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      behavior: HitTestBehavior.translucent,
      child: Scaffold(
        body: getMenuTemplate(),
      ),
    );
  }

  Widget getMenuTemplate() {
    if (menuMainTemplate == "1")
      return MenuTemplate1();
    else if (menuMainTemplate == "2")
      return MenuTemplate1();
    else
      return MenuTemplate1();
  }

  Future<Map?> getMenu() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.menu(widget.outlet.id, null);
    if (data != null && data['data'] != null) {
      categoryList = (data['data']['categories'] as List)
          .map((data) => MenuCategory.fromJson(data))
          .toList();
    }
    return data;
  }

  getProductDetail(int productId, String tag) async {
    EasyLoading.show();
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.productDetail(productId);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        Product product = Product.fromJson(data['data']['product']);
        List<Product> recommended =
            (data['data']['recommended_products'] as List)
                .map((data) => Product.fromJson(data))
                .toList();
        String? inviteMsg = data['data']['invite_message'] ?? null;
        String? inviteUrl = data['data']['invite_url'] ?? null;

        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (BuildContext context) => MenuDetailTemplate1(
                      product: product,
                      recommended: recommended,
                      inviteMsg: inviteMsg,
                      inviteUrl: inviteUrl,
                      tag: "$tag${product.id}",
                    )));
      }
    }
  }
}

Future<Map?> getMenuSearch(String? searchValue) async {
  ApiGet apiGet = ApiGet();
  Map? data = await apiGet.menuSearch(outlet!.id, searchValue);
  return data;
}
