import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:amverton/Constant/theme.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:amverton/View/Scanqr/Section/balance_template1.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/user_membership.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Points/points_history_page.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class MembershipTemplate1 extends StatefulWidget {
  static String routeName = "/membership/1";
  final dynamic args;

  const MembershipTemplate1({Key? key, this.args}) : super(key: key);

  @override
  _MembershipTemplate1State createState() => _MembershipTemplate1State();
}

class _MembershipTemplate1State extends State<MembershipTemplate1>
    with TickerProviderStateMixin {
  late Box box;
  late Box boxGlobal;
  late Future future;
  late Future boxFuture;
  String theme = "L";
  late TabController tabController;
  bool isLogin = false;

  // Updated variables to match new API response
  User? user;
  List<Membership> memberships = [];
  String memberGrade = "";
  String totalSpend = "0.00";
  String userPoint = "0.00";
  String pointExpirationMessage = "";

  // Default static content (no more assets)
  String pageTitle = "Membership";
  String benefitTitle = "My Benefits";
  String benefitHeadline = "Enjoy exclusive member benefits";

  @override
  void initState() {
    super.initState();
    theme = widget.args?["theme"] ?? "L";
    boxFuture = openBox();
    future = getMemberships();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    isLogin = box.get("is_login") ?? false;
    return box;
  }

  @override
  void dispose() {
    if (memberships.isNotEmpty) {
      tabController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                appBar: AppBar(
                  title: Text(
                    pageTitle,
                    style: TextStyle(
                      color: theme == "L" ? ThemeColors.primaryDark : ThemeColors.light,
                    ),
                  ),
                  iconTheme: IconThemeData(
                    color: theme == "L" ? ThemeColors.primaryDark : ThemeColors.light,
                  ),
                ),
                body: Container(
                  width: phoneWidth,
                  height: phoneHeight,
                  decoration: BoxDecoration(
                    color: ThemeColors.light,
                  ),
                  child: FutureBuilder(
                      future: future,
                      builder: (BuildContext context, AsyncSnapshot snapshot) {
                        if (snapshot.connectionState == ConnectionState.done) {
                          if (snapshot.hasData && snapshot.data?['status'] == 'success') {
                            // Parse the new API response
                            try {
                              user = User.fromJson(snapshot.data['data']['user']);
                              memberships = (snapshot.data['data']['memberships'] as List)
                                  .map((e) => Membership.fromJson(e))
                                  .toList();
                              
                              memberGrade = user?.membership.name ?? "-";
                              totalSpend = user?.totalSpend ?? "0.00";
                              userPoint = user?.point ?? "0.00";
                              pointExpirationMessage = "points that will expire soon";

                              int currentMembershipIndex = memberships.indexWhere(
                                  (membership) => membership.id == user?.membership.id);

                              if (memberships.isNotEmpty) {
                                tabController = TabController(
                                    length: memberships.length,
                                    vsync: this,
                                    initialIndex: currentMembershipIndex == -1 ? 0 : currentMembershipIndex);
                              }

                              return SingleChildScrollView(
                                padding: EdgeInsets.only(bottom: verticalPaddingMedium),
                                child: Column(
                                  children: [
                                    BalanceTemplate1(theme: 'L'),
                                    SizedBox(height: spacingHeightSmall),
                                    // Current membership benefits
                                    if (user != null) membershipBenefitsWidget(),
                                    // All membership tiers
                                    if (memberships.isNotEmpty) tierDetail(),
                                   
                                  ],
                                ),
                              );
                            } catch (e) {
                              print("Error parsing API response: $e");
                              return Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.error, size: 64, color: Colors.red),
                                    SizedBox(height: 16),
                                    Text("Error loading membership data"),
                                    ElevatedButton(
                                      onPressed: () {
                                        setState(() {
                                          future = getMemberships();
                                        });
                                      },
                                      child: Text("Retry"),
                                    ),
                                  ],
                                ),
                              );
                            }
                          } else {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.warning, size: 64, color: Colors.orange),
                                  SizedBox(height: 16),
                                  Text("No membership data available"),
                                  ElevatedButton(
                                    onPressed: () {
                                      setState(() {
                                        future = getMemberships();
                                      });
                                    },
                                    child: Text("Retry"),
                                  ),
                                ],
                              ),
                            );
                          }
                        }
                        return Container(
                          child: Center(
                            child: CircularProgressIndicator(),
                          ),
                        );
                      }),
                ),
              ),
            );
          }
          return Scaffold(
            appBar: AppBar(),
            body: Center(child: CircularProgressIndicator()),
          );
        });
  }

  Widget membershipBenefitsWidget() {
    if (user == null || user!.membership.benefits.isEmpty) {
      return Container();
    }
    
    return Container(
      margin: EdgeInsets.only(
          top: defaultInnerPadding,
          left: spacingHeightMedium,
          right: spacingHeightMedium),
      padding: EdgeInsets.all(defaultPadding),
      width: phoneWidth,
      decoration: BoxDecoration(
        border: Border.all(
          color: ThemeColors.disabled,
        ),
        boxShadow: kElevationToShadow[3],
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: [
            Color(0xFF818592),
            Color(0xFF595C68),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            benefitTitle,
            style: TextStyle(
              color: Colors.white,
              fontSize: h3,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            benefitHeadline,
            style: TextStyle(
              color: Colors.white,
              fontSize: h4,
            ),
          ),
          SizedBox(height: spacingHeightMedium),
          // Handle case where there are more than 5 benefits
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: List.generate(
                user!.membership.benefits.length > 5 ? 5 : user!.membership.benefits.length, 
                (index) {
                  Benefit benefit = user!.membership.benefits[index];
                  return Container(
                    width: phoneWidth / 5,
                    margin: EdgeInsets.only(right: spacingHeightSmall),
                    child: Column(
                      children: [
                        CachedNetworkImage(
                          imageUrl: benefit.icon,
                          width: phoneWidth / 9,
                          height: phoneWidth / 9,
                          fit: BoxFit.cover,
                          placeholder: (context, url) {
                            return Center(
                              child: CircularProgressIndicator(
                                color: Colors.white,
                              ),
                            );
                          },
                          errorWidget: (context, url, error) {
                            return Icon(
                              Icons.card_giftcard,
                              color: Colors.white,
                              size: phoneWidth / 9,
                            );
                          },
                        ),
                        SizedBox(height: spacingHeightSmall),
                        Container(
                          height: phoneWidth / 10,
                          child: AutoSizeText(
                            benefit.title,
                            style: TextStyle(
                              color: Colors.white,
                            ),
                            maxFontSize: h4,
                            minFontSize: h5,
                            maxLines: 2,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  );
                }
              ),
            ),
          ),
        ],
      ),
    );
  }
Widget tierDetail() {
  if (memberships.isEmpty) return Container();
  
  return DefaultTabController(
    length: memberships.length,
    child: Container(
      padding: EdgeInsets.symmetric(horizontal: defaultInnerPadding),
      height: phoneHeight / 3,
      child: Column(
        children: [
          TabBar(
            controller: tabController,
            isScrollable: false,
            indicatorColor: ThemeColors.primaryDark,
            indicator: UnderlineTabIndicator(
              borderSide: BorderSide(
                width: 5.0,
                color: ThemeColors.primaryDark,
              ),
              insets: EdgeInsets.symmetric(horizontal: 55.0),
            ),
            labelColor: ThemeColors.primaryDark,
            labelStyle: TextStyle(fontWeight: FontWeight.bold),
            unselectedLabelColor: ThemeColors.gray,
            unselectedLabelStyle: TextStyle(fontWeight: FontWeight.normal),
            tabs: List.generate(memberships.length, (index) {
              return Tab(
                text: memberships[index].name,
              );
            }),
          ),
          Expanded(
            child: TabBarView(
              controller: tabController,
              children: List.generate(
                memberships.length,
                (index) {
                  Membership membership = memberships[index];
                  return Container(
                    width: double.infinity, // Add this to ensure full width
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start, // Add this for left alignment
                      children: [
                        // Benefits section
                        Padding(
                          padding: EdgeInsets.all(16.0), // Add consistent padding inside the container
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start, // Ensure left alignment for benefits
                            children: [
                              // F&B Discount
                              _buildBenefitItem(
                                "F&B Discount",
                                "A ${membership.diningDiscount}% discount is available on dining services.",
                              ),
                              SizedBox(height: spacingHeightMedium),
                              // Room Discount
                              _buildBenefitItem(
                                "Room Discount",
                                "Enjoy a ${membership.roomDiscount}% discount on Room Booking",
                              ),
                              SizedBox(height: spacingHeightMedium),
                              // Point Gain
                              _buildBenefitItem(
                                "Point Gain",
                                "Every MYR 1.00 is equal 1 point\nEvery 100 points is equal to MYR 5 i.e 5%",
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _buildBenefitItem(String title, String description) {
  return Container(
    width: double.infinity, // Ensure benefit item takes full width
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start, // Left align content
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: h3,
            fontWeight: FontWeight.bold,
            color: ThemeColors.primaryDark,
            fontFamily: fontFamily2,
          ),
        ),
        SizedBox(height: spacingHeightSmall),
        Text(
          description,
          style: TextStyle(
            fontSize: h4,
            color: ThemeColors.primaryDark,
            fontFamily: fontFamily2,
          ),
        ),
      ],
    ),
  );
}
  Future<Map?> getMemberships() async {
    try {
      ApiGet apiGet = ApiGet();
      Map? data = await apiGet.memberships();
      return data;
    } catch (e) {
      print("Error fetching memberships: $e");
      return null;
    }
  }
}