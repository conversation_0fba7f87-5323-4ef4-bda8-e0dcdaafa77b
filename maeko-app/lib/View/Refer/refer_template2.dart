import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/referral_reward.dart';
import 'package:amverton/Model/user.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:hive/hive.dart';
import 'package:share_plus/share_plus.dart';
import 'package:collection/collection.dart';

class ReferTemplate2 extends StatefulWidget {
  static String routeName = "/referral/2";
  final dynamic args;

  const ReferTemplate2({Key? key, this.args}) : super(key: key);

  @override
  _ReferTemplate2State createState() => _ReferTemplate2State();
}

class _ReferTemplate2State extends State<ReferTemplate2> {
  late Future<void> boxFuture;
  late Future<Map?> future;
  late Box<dynamic> box;
  String theme = "L";

  List<ReferralReward> rewardsList = [];
  late User user;
  String inviteMessage = "";
  String inviteUrl = "";

  @override
  void initState() {
    super.initState();
    theme = widget.args["theme"];
    boxFuture = openBox();
    future = getReferral();
  }

  Future<void> openBox() async {
    box = await Hive.openBox<dynamic>('box');
    user = box.get("user");
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<void>(
      future: boxFuture,
      builder: (BuildContext context, AsyncSnapshot<void> snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          return Scaffold(
            extendBodyBehindAppBar: true,
            appBar: AppBar(
              title: Text(
                "Referral",
                style: TextStyle(
                  color: ThemeColors.primaryDark,
                ),
              ),
              iconTheme: IconThemeData(
                color: ThemeColors.primaryDark,
              ),
            ),
            body: Stack(
              children: [
                Container(
                  width: phoneWidth,
                  height: phoneHeight,
                ),
                Container(
                  width: phoneWidth,
                  height: phoneHeight / 2,
                  decoration: BoxDecoration(
                    color: Colors.white,
                  ),
                ),
                FutureBuilder<Map?>(
                  future: future,
                  builder: (BuildContext context, AsyncSnapshot<Map?> snapshot) {
                    if (snapshot.hasData && snapshot.data != null) {
                      inviteMessage = snapshot.data!["data"]["invite_message"];
                      inviteUrl = snapshot.data!["data"]["invite_url"];
                    }
                    return Container(
                      width: phoneWidth,
                      height: phoneHeight,
                      padding: EdgeInsets.fromLTRB(
                        defaultPadding,
                        verticalPaddingLarge + defaultPadding,
                        defaultPadding,
                        defaultPadding,
                      ),
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            Container(
                              margin: EdgeInsets.symmetric(
                                  vertical: spacingHeightMedium),
                              padding: EdgeInsets.all(defaultPadding),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                color: ThemeColors.light,
                              ),
                              width: phoneWidth,
                              child: Column(
                                children: [
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: verticalPaddingSmall),
                                    child: Text(
                                      "",
                                      style: TextStyle(
                                        color: ThemeColors.dark,
                                        fontSize: h3,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                  SizedBox(height: spacingHeightSmall),
                                  Text(
                                    "",
                                    style: TextStyle(
                                      color: ThemeColors.gray,
                                      fontSize: h4,
                                    ),
                                  ),
                                  SizedBox(height: spacingHeightMedium),
                                  Text(
                                    "",
                                    style: TextStyle(
                                      color: ThemeColors.dark,
                                      fontSize: h3,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  SizedBox(height: spacingHeightSmall),
                                  Container(
                                    width: phoneWidth,
                                    padding:
                                        EdgeInsets.all(defaultInnerPadding),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(20),
                                      color: ThemeColors.primaryLight,
                                    ),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        IconButton(
                                          color: ThemeColors.dark,
                                          icon: Icon(Icons.copy),
                                          onPressed: () {
                                            // Clipboard.setData(ClipboardData(
                                            //   text: user.referralCode ?? "",
                                            // )).then((_) {
                                            //   ScaffoldMessenger.of(context)
                                            //       .showSnackBar(const SnackBar(
                                            //           content: Text(
                                            //               "Referral code copied to clipboard.")));
                                            // });
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            DefaultButton(
                              borderRadius: 15,
                              text: "Share",
                              buttonColor: ThemeColors.primaryDark,
                              onPressed: () {
                                shareMore();
                              },
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          );
        }
        return Scaffold(
          appBar: AppBar(),
          body: Center(child: CircularProgressIndicator()),
        );
      },
    );
  }

  Future<Map?> getReferral() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.referral();

    if (data != null && data['data'] != null) {
      rewardsList = (data['data']['referrals'] as List)
          .map((data) => ReferralReward.fromJson(data))
          .toList();
    }
    return data;
  }

  void shareMore() async {
    final box = context.findRenderObject() as RenderBox?;
    if (box != null) {
      await Share.share("$inviteMessage\n$inviteUrl",
          sharePositionOrigin: box.localToGlobal(Offset.zero) & box.size);
    }
  }
}
