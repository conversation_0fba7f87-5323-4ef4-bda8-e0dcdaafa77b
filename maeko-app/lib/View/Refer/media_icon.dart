import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_size.dart';

class MediaIcon extends StatelessWidget {
  final String image;
  final Color bgColor;
  final Function()? onTap;

  const MediaIcon({
    Key? key,
    required this.image,
    required this.bgColor,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: onTap,
        child: Container(
          height: phoneWidth / 10,
          width: phoneWidth / 5,
          padding: const EdgeInsets.all(5),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: bgColor,
          ),
          child: Image.asset(image, color: Colors.white),
        ));
  }
}
