import 'dart:io';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/referral_reward.dart';
import 'package:amverton/Model/user.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Refer/media_icon.dart';
import 'package:hive/hive.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:collection/collection.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class ReferTemplate1 extends StatefulWidget {
  static String routeName = "/referral/1";
  final dynamic args;

  const ReferTemplate1({Key? key, this.args}) : super(key: key);

  @override
  _ReferTemplate1State createState() => _ReferTemplate1State();
}

class _ReferTemplate1State extends State<ReferTemplate1> {
  late Future boxFuture;
  late Future future;
  late Box box;
  late Box boxGlobal;
  String theme = "L";

  List<ReferralReward> rewardsList = [];
  late User user;
  String inviteMessage = "";
  String inviteUrl = "";

  @override
  void initState() {
    super.initState();
    theme = widget.args["theme"];
    boxFuture = openBox();
    future = getReferral();
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    user = box.get("user");
    return await Hive.openBox<Asset>('ACCOUNT+REFERRAL+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: boxFuture,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          final Box list = snapshot.data;
          list.watch();

          String? backgroundType = list.values
                  .firstWhereOrNull(
                      (element) => element.name == "BACKGROUND_TYPE")
                  ?.data ??
              "";
          Asset? background = list.values.firstWhereOrNull(
              (element) => element.name == "BACKGROUND_$backgroundType");

          return Scaffold(
            extendBodyBehindAppBar: true,
            appBar: AppBar(
              title: Text(
                list.values
                        .firstWhereOrNull((element) => element.name == "TITLE")
                        ?.data ??
                    "",
                style: TextStyle(
                  color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                ),
              ),
              iconTheme: IconThemeData(
                color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
              ),
            ),
            body: Container(
              height: phoneHeight,
              decoration: background == null
                  ? BoxDecoration(
                      color: ThemeColors.light,
                    )
                  : BoxDecoration(
                      image: backgroundType == "IMAGE"
                          ? DecorationImage(
                              image: CachedNetworkImageProvider(
                                  background.data ?? ""),
                              fit: BoxFit.cover,
                            )
                          : null,
                      color: backgroundType == "COLOR"
                          ? Color(
                              int.parse("0xff${background.data ?? "FFFFFF"}"))
                          : null,
                    ),
              child: FutureBuilder(
                  future: future,
                  builder: (BuildContext context, AsyncSnapshot snapshot) {
                    if (snapshot.connectionState == ConnectionState.done) {
                      if (snapshot.hasData) {
                        inviteMessage = snapshot.data["data"]["invite_message"];
                        inviteUrl = snapshot.data["data"]["invite_url"];
                      }

                      return SingleChildScrollView(
                        padding: EdgeInsets.only(
                          top: verticalPaddingLarge,
                          bottom: bottomPaddingWithoutBar,
                        ),
                        child: Column(
                          children: [
                            Column(
                              children: [
                                CachedNetworkImage(
                                  imageUrl: list.values
                                          .firstWhereOrNull((element) =>
                                              element.name == "BANNER")
                                          ?.data ??
                                      "",
                                  fit: BoxFit.fitWidth,
                                  placeholder: (context, url) {
                                    return Padding(
                                      padding: EdgeInsets.all(defaultPadding),
                                      child: CircularProgressIndicator(
                                        color: ThemeColors.primaryDark,
                                      ),
                                    );
                                  },
                                  errorWidget: (context, url, error) {
                                    return Container();
                                  },
                                ),
                                SizedBox(height: spacingHeightLarge),
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: defaultPadding),
                                  child: Text(
                                    list.values
                                            .firstWhereOrNull((element) =>
                                                element.name == "HEADLINE")
                                            ?.data ??
                                        "",
                                    style: TextStyle(
                                      color: theme == "L"
                                          ? ThemeColors.dark
                                          : ThemeColors.light,
                                      fontSize: h2,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                Container(
                                  margin: EdgeInsets.only(
                                      top: spacingHeightMedium,
                                      bottom: spacingHeightLarge),
                                  padding: EdgeInsets.all(defaultInnerPadding),
                                  width: phoneWidth / 1.35,
                                  decoration: BoxDecoration(
                                    color: ThemeColors.light,
                                    borderRadius: BorderRadius.circular(20),
                                    border: Border.all(
                                        color: theme == "L"
                                            ? ThemeColors.disabled
                                            : ThemeColors.gray),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Spacer(),
                                      // Text(user.referralCode ?? "-"),
                                      // const Spacer(),
                                      // GestureDetector(
                                      //     onTap: () {
                                      //       Clipboard.setData(ClipboardData(
                                      //         text: user.referralCode ?? "",
                                      //       )).then((_) {
                                      //         ScaffoldMessenger.of(context)
                                      //             .showSnackBar(const SnackBar(
                                      //                 content: Text(
                                      //                     "Referral code copied to clipboard.")));
                                      //       });
                                      //     },
                                      //     child: ImageIcon(AssetImage(
                                      //         "assets/icons/${boxGlobal.get("iconSet")}/copy-right.png"))),
                                    ],
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: defaultPadding),
                                  child: Text(
                                    list.values
                                            .firstWhereOrNull((element) =>
                                                element.name == "SUBHEADLINE")
                                            ?.data ??
                                        "",
                                    style: TextStyle(
                                        color: theme == "L"
                                            ? ThemeColors.dark
                                            : ThemeColors.light,
                                        fontSize: h3),
                                  ),
                                ),
                                SizedBox(height: spacingHeightMedium),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    MediaIcon(
                                      image:
                                          "assets/icons/Social media-facebook.png",
                                      bgColor: const Color(0xff1877f2),
                                      onTap: () {
                                        openUrl(
                                            "https://www.facebook.com/sharer/sharer.php?u=$inviteUrl&quote=$inviteMessage");
                                      },
                                    ),
                                    MediaIcon(
                                      image:
                                          "assets/icons/Social media-whatsapp.png",
                                      bgColor: const Color(0xff00D95F),
                                      onTap: () {
                                        openUrl(
                                            "https://api.whatsapp.com/send?text=$inviteMessage\n$inviteUrl");
                                      },
                                    ),
                                    MediaIcon(
                                      image:
                                          "assets/icons/${boxGlobal.get("iconSet")}/Standard_Dots menu.png",
                                      bgColor: ThemeColors.primaryDark,
                                      onTap: () {
                                        shareMore();
                                      },
                                    ),
                                  ],
                                ),
                                referralRewardsView(list),
                              ],
                            ),
                            //How it works
                            Padding(
                              padding: EdgeInsets.all(defaultPadding),
                              child: HtmlWidget(
                                '''
                      ${list.values.firstWhereOrNull((element) => element.name == "INFORMATION")?.data ?? ""}
                      ''',
                                textStyle: TextStyle(
                                  color: theme == "L"
                                      ? ThemeColors.dark
                                      : ThemeColors.light,
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }
                    return Container();
                  }),
            ),
          );
        }
        return Scaffold(appBar: AppBar());
      },
    );
  }

  Widget referralRewardsView(Box list) {
    if (rewardsList.isNotEmpty) {
      return Container(
        margin: EdgeInsets.fromLTRB(
            defaultPadding, spacingHeightLarge, defaultPadding, defaultPadding),
        padding: EdgeInsets.fromLTRB(
            defaultInnerPadding, defaultInnerPadding, defaultInnerPadding, 0),
        decoration: BoxDecoration(
          color: ThemeColors.light,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
              color: theme == "L" ? ThemeColors.disabled : ThemeColors.gray),
        ),
        child: Column(
          children: [
            Text(
              list.values
                      .firstWhereOrNull(
                          (element) => element.name == "REWARD_TITLE")
                      ?.data ??
                  "",
              style: TextStyle(
                  color: ThemeColors.dark,
                  fontSize: h2,
                  fontWeight: FontWeight.bold),
            ),
            SizedBox(height: spacingHeightMedium),
            Stack(
              children: [
                Positioned.fill(
                  child: Container(
                    margin: EdgeInsets.only(left: defaultPadding, top: 10),
                    decoration: BoxDecoration(
                      border: Border(
                          left: BorderSide(
                        width: 5,
                        color: ThemeColors.secondaryDark,
                      )),
                    ),
                  ),
                ),
                Column(
                  children: List.generate(rewardsList.length, (index) {
                    return Column(
                      children: [
                        Container(
                          margin: EdgeInsets.only(bottom: defaultInnerPadding),
                          child: Row(
                            children: [
                              Container(
                                margin: EdgeInsets.only(right: defaultPadding),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: rewardsList[index].isClaimed == true
                                      ? ThemeColors.primaryDark
                                      : ThemeColors.disabled,
                                ),
                                child: Image.asset(
                                  "assets/icons/${boxGlobal.get("iconSet")}/check-broken.png",
                                  width: phoneWidth / 10,
                                  height: phoneWidth / 10,
                                  color: theme == "L"
                                      ? ThemeColors.light
                                      : ThemeColors.dark,
                                ),
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Invite ${rewardsList[index].invite} Friend",
                                    style: TextStyle(
                                      color: ThemeColors.dark,
                                      fontSize: h3,
                                    ),
                                  ),
                                  SizedBox(
                                    width: phoneWidth / 3,
                                    child: AutoSizeText(
                                      () {
                                        if (rewardsList[index].type ==
                                            "POINT") {
                                          return "+${rewardsList[index].point} coins";
                                        } else if (rewardsList[index].type ==
                                            "VOUCHER") {
                                          return "${rewardsList[index].voucher?.title ?? ""}";
                                        } else {
                                          return "-";
                                        }
                                      }(),
                                      style: TextStyle(
                                        color: ThemeColors.dark,
                                        fontSize: h3,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                              const Spacer(),
                              SizedBox(
                                width: phoneWidth / 4,
                                child: DefaultButton(
                                  text: list.values
                                          .firstWhereOrNull((element) =>
                                              element.name ==
                                              "COLLECT_BUTTON_TEXT")
                                          ?.data ??
                                      "",
                                  textColor: () {
                                    if (rewardsList[index].isClaimed == true) {
                                      return ThemeColors.gray;
                                    } else if (rewardsList[index].isEligible ==
                                        true) {
                                      return null;
                                    } else {
                                      return ThemeColors.dark;
                                    }
                                  }(),
                                  buttonColor: () {
                                    if (rewardsList[index].isClaimed == true) {
                                      return const Color(0xfff2f2f2);
                                    } else if (rewardsList[index].isEligible ==
                                        true) {
                                      return important_variables.projectName ==
                                              "obriens"
                                          ? ThemeColors.secondaryDark
                                          : ThemeColors.primaryDark;
                                    } else {
                                      return ThemeColors.disabled;
                                    }
                                  }(),
                                  clickable: rewardsList[index].isEligible,
                                  onPressed: () {
                                    if (rewardsList[index].isEligible == true) {
                                      claimRewards();
                                    }
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    );
                  }),
                )
              ],
            ),
          ],
        ),
      );
    }
    return Container();
  }

  Future<Map?> getReferral() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.referral();

    if (data != null && data['data'] != null) {
      rewardsList = (data['data']['rewards'] as List)
          .map((data) => ReferralReward.fromJson(data))
          .toList();
    }
    return data;
  }

  void claimRewards() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    Map? data = await apiPost.claimReferralRewards();
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          setState(() {
            future = getReferral();
          });
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  void openUrl(String url) async {
    Uri value = Uri.parse(url);
    print(value.toString());

    if (await canLaunchUrl(value)) {
      if (Platform.isIOS)
        await launchUrl(value);
      else
        await launchUrl(value, mode: LaunchMode.externalApplication);
    } else {
      print("failed to open");
    }
  }

  void shareMore() async {
    final box = context.findRenderObject() as RenderBox?;
    await Share.share("$inviteMessage\n$inviteUrl",
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size);
  }
}
