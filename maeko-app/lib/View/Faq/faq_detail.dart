import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/faq.dart';
import 'package:hive/hive.dart';

class FaqDetail extends StatefulWidget {
  final Question question;

  const FaqDetail({Key? key, required this.question}) : super(key: key);

  @override
  _FaqDetailState createState() => _FaqDetailState();
}

class _FaqDetailState extends State<FaqDetail> {
  Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: ThemeColors.light,
        appBar: AppBar(title: const Text("FAQ")),
        body: SingleChildScrollView(
          padding: EdgeInsets.all(defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(
                    top: topPaddingWithoutBar, bottom: defaultPadding),
                child: Text(
                  widget.question.question,
                  style: TextStyle(
                      color: ThemeColors.dark,
                      fontSize: h1,
                      fontWeight: FontWeight.bold),
                ),
              ),
              Text(
                widget.question.answer,
                style: TextStyle(color: ThemeColors.dark, fontSize: h3),
              ),
              SizedBox(height: spacingHeightMedium),
            ],
          ),
        ));
  }
}
