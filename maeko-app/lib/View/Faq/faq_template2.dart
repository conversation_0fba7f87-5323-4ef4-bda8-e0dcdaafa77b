import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/faq.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Custom%20Widgets/empty_list.dart';
import 'package:amverton/View/Faq/faq_detail.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class FaqTemplate2 extends StatefulWidget {
  static String routeName = "/faq/2";
  final dynamic args;

  const FaqTemplate2({Key? key, this.args}) : super(key: key);

  @override
  _FaqTemplate2State createState() => _FaqTemplate2State();
}

class _FaqTemplate2State extends State<FaqTemplate2> {
  late Future boxFuture;
  late Box boxGlobal;
  String theme = "L";
  String? titleBackgroundType;
  Asset? titleBackground;
  String? contentBackgroundType;
  Asset? contentBackground;

  late Future future;
  bool onSearch = false;
  TextEditingController searchController = TextEditingController();
  final StreamController streamController = StreamController.broadcast();

  @override
  void initState() {
    super.initState();
    theme = widget.args["theme"];
    boxFuture = openBox();
    future = getFaq();
    searchController.addListener(() {
      streamController.add(searchController.text);
    });
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('ACCOUNT+QUESTIONS+2');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            titleBackgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "TITLE_BACKGROUND_TYPE")
                    ?.data ??
                "";
            titleBackground = list.values.firstWhereOrNull((element) =>
                element.name == "TITLE_BACKGROUND_$titleBackgroundType");

            contentBackgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "CONTENT_BACKGROUND_TYPE")
                    ?.data ??
                "";
            contentBackground = list.values.firstWhereOrNull((element) =>
                element.name == "CONTENT_BACKGROUND_$contentBackgroundType");

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                  extendBodyBehindAppBar: true,
                  appBar: AppBar(
                    title: Text(
                      list.values
                              .firstWhereOrNull(
                                  (element) => element.name == "TITLE")
                              ?.data ??
                          "",
                      style: TextStyle(
                        color:
                            theme == "L" ? ThemeColors.dark : ThemeColors.light,
                      ),
                    ),
                    iconTheme: IconThemeData(
                      color:
                          theme == "L" ? ThemeColors.dark : ThemeColors.light,
                    ),
                  ),
                  body: Stack(
                    children: [
                      Container(
                        width: phoneWidth,
                        height: phoneHeight,
                        decoration: contentBackground == null
                            ? BoxDecoration(
                                color: ThemeColors.light,
                              )
                            : BoxDecoration(
                                image: contentBackgroundType == "IMAGE"
                                    ? DecorationImage(
                                        image: CachedNetworkImageProvider(
                                            contentBackground!.data ?? ""),
                                        fit: BoxFit.cover,
                                      )
                                    : null,
                                color: contentBackgroundType == "COLOR"
                                    ? Color(int.parse(
                                        "0xff${contentBackground!.data ?? "FFFFFF"}"))
                                    : null,
                              ),
                      ),
                      Container(
                        width: phoneWidth,
                        height: phoneHeight / 3.5,
                        decoration: titleBackground == null
                            ? BoxDecoration(
                                color: ThemeColors.light,
                              )
                            : BoxDecoration(
                                image: titleBackgroundType == "IMAGE"
                                    ? DecorationImage(
                                        image: CachedNetworkImageProvider(
                                            titleBackground!.data ?? ""),
                                        fit: BoxFit.cover,
                                      )
                                    : null,
                                color: titleBackgroundType == "COLOR"
                                    ? Color(int.parse(
                                        "0xff${titleBackground!.data ?? "FFFFFF"}"))
                                    : null,
                                borderRadius: const BorderRadius.only(
                                  bottomLeft: Radius.circular(15),
                                  bottomRight: Radius.circular(15),
                                ),
                              ),
                      ),
                      Container(
                        width: phoneWidth,
                        height: phoneHeight,
                        padding: EdgeInsets.fromLTRB(
                            defaultPadding,
                            defaultPadding + kToolbarHeight + kToolbarHeight,
                            defaultPadding,
                            defaultPadding),
                        child: CustomScrollView(
                          physics: const ClampingScrollPhysics(),
                          slivers: [
                            SliverFillRemaining(
                              hasScrollBody: false,
                              child: Column(
                                children: [
                                  Text(
                                    list.values
                                            .firstWhereOrNull((element) =>
                                                element.name == "HEADLINE")
                                            ?.data ??
                                        "",
                                    style: TextStyle(
                                        color: theme == "L"
                                            ? ThemeColors.dark
                                            : ThemeColors.light,
                                        fontSize: h2,
                                        fontWeight: FontWeight.bold),
                                  ),
                                  detailView(list),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    ],
                  )),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }

  Widget detailView(Box list) {
    return Visibility(
      visible: !onSearch,
      child: Expanded(
        child: Container(
          width: phoneWidth,
          padding: EdgeInsets.only(
              top: bottomPaddingWithoutBar, bottom: defaultPadding),
          child: FutureBuilder(
            future: future,
            builder: (BuildContext context, AsyncSnapshot snapshot) {
              if (snapshot.connectionState == ConnectionState.done) {
                if (snapshot.hasData) {
                  Faq faq = Faq.fromJson(snapshot.data["data"]);

                  if (faq.questions!.isNotEmpty) {
                    return Column(
                      children: List.generate(faq.questions!.length, (index) {
                        return GestureDetector(
                          onTap: () {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (BuildContext context) => FaqDetail(
                                      question: faq.questions![index]),
                                ));
                          },
                          child: Container(
                            margin: EdgeInsets.symmetric(
                              horizontal: defaultPadding,
                              vertical: spacingHeightSmall,
                            ),
                            decoration: BoxDecoration(
                              color: ThemeColors.light,
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: theme == "L"
                                    ? ThemeColors.disabled
                                    : ThemeColors.gray,
                              ),
                            ),
                            child: ListTile(
                              title: Text(
                                faq.questions![index].question,
                                style: TextStyle(
                                  color: ThemeColors.dark,
                                  fontSize: h4,
                                ),
                              ),
                              trailing: ImageIcon(
                                AssetImage(
                                    "assets/icons/${boxGlobal.get("iconSet")}/Standard_Moreleft.png"),
                                color: theme == "L"
                                    ? ThemeColors.dark
                                    : ThemeColors.light,
                              ),
                            ),
                          ),
                        );
                      }),
                    );
                  } else {
                    return EmptyList(theme: theme);
                  }
                }
              }
              return Container();
            },
          ),
        ),
      ),
    );
  }

  Future<Map?> getFaq() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.faq();

    return data;
  }
}
