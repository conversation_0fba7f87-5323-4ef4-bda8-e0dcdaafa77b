import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/faq.dart';
import 'package:amverton/View/Faq/faq_detail.dart';
import 'package:hive/hive.dart';

class FaqList extends StatefulWidget {
  final String categoryTitle;
  final List<Question> questions;

  const FaqList({
    Key? key,
    required this.categoryTitle,
    required this.questions,
  }) : super(key: key);

  @override
  _FaqListState createState() => _FaqListState();
}

class _FaqListState extends State<FaqList> {
  Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeColors.light,
      appBar: AppBar(title: Text(widget.categoryTitle)),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(defaultPadding),
        child: Column(
          children: List.generate(widget.questions.length, (index) {
            return GestureDetector(
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (BuildContext context) => FaqDetail(
                              question: widget.questions[index],
                            )));
              },
              child: Container(
                margin: EdgeInsets.symmetric(vertical: spacingHeightSmall),
                decoration: BoxDecoration(
                  color: ThemeColors.light,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: ThemeColors.disabled,
                  ),
                ),
                child: ListTile(
                  title: Text(
                    widget.questions[index].question,
                    style: TextStyle(
                      color: ThemeColors.dark,
                      fontSize: h4,
                    ),
                  ),
                  trailing: ImageIcon(
                    AssetImage(
                        "assets/icons/${boxGlobal.get("iconSet")}/Standard_Moreleft.png"),
                    color: ThemeColors.dark,
                  ),
                ),
              ),
            );
          }),
        ),
      ),
    );
  }
}
