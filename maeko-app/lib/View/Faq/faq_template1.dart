import 'dart:async';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/faq.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Custom%20Widgets/default_search_bar.dart';
import 'package:amverton/View/Faq/faq_detail.dart';
import 'package:amverton/View/Faq/faq_list.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class FaqTemplate1 extends StatefulWidget {
  static String routeName = "/faq/1";
  final dynamic args;

  const FaqTemplate1({Key? key, this.args}) : super(key: key);

  @override
  _FaqTemplate1State createState() => _FaqTemplate1State();
}

class _FaqTemplate1State extends State<FaqTemplate1> {
  late Future boxFuture;
  late Box boxGlobal;
  String theme = "L";
  String? topBackgroundType;
  Asset? topBackground;
  String? bottomBackgroundType;
  Asset? bottomBackground;

  late Future future;
  bool onSearch = false;
  TextEditingController searchController = TextEditingController();
  final StreamController streamController = StreamController.broadcast();

  @override
  void initState() {
    super.initState();
    theme = widget.args["theme"];
    boxFuture = openBox();
    future = getFaq();
    searchController.addListener(() {
      streamController.add(searchController.text);
    });
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('ACCOUNT+QUESTIONS+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            topBackgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "TOP_BACKGROUND_TYPE")
                    ?.data ??
                "";
            topBackground = list.values.firstWhereOrNull((element) =>
                element.name == "TOP_BACKGROUND_$topBackgroundType");

            bottomBackgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BOTTOM_BACKGROUND_TYPE")
                    ?.data ??
                "";
            bottomBackground = list.values.firstWhereOrNull((element) =>
                element.name == "BOTTOM_BACKGROUND_$bottomBackgroundType");

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                  extendBodyBehindAppBar: true,
                  appBar: AppBar(
                    title: Text(
                      list.values
                              .firstWhereOrNull(
                                  (element) => element.name == "TITLE")
                              ?.data ??
                          "",
                      style: TextStyle(
                        color:
                            theme == "L" ? ThemeColors.dark : ThemeColors.light,
                      ),
                    ),
                    iconTheme: IconThemeData(
                      color:
                          theme == "L" ? ThemeColors.dark : ThemeColors.light,
                    ),
                  ),
                  body: CustomScrollView(
                    slivers: [
                      SliverFillRemaining(
                        hasScrollBody: false,
                        child: Column(
                          children: [
                            Container(
                              height: phoneHeight / 3.6,
                              padding: EdgeInsets.fromLTRB(
                                defaultPadding,
                                verticalPaddingLarge,
                                defaultPadding,
                                defaultPadding,
                              ),
                              decoration: topBackground == null
                                  ? BoxDecoration(
                                      color: ThemeColors.light,
                                    )
                                  : BoxDecoration(
                                      image: topBackgroundType == "IMAGE"
                                          ? DecorationImage(
                                              image: CachedNetworkImageProvider(
                                                  topBackground!.data ?? ""),
                                              fit: BoxFit.cover,
                                            )
                                          : null,
                                      color: topBackgroundType == "COLOR"
                                          ? Color(int.parse(
                                              "0xff${topBackground!.data ?? "FFFFFF"}"))
                                          : null,
                                    ),
                              child: Column(
                                children: [
                                  DefaultSearchBar(
                                    controller: searchController,
                                    hintText: "Search",
                                    onChanged: (value) {
                                      if (value.isEmpty && onSearch) {
                                        setState(() {
                                          onSearch = false;
                                        });
                                      } else if (value.isNotEmpty &&
                                          !onSearch) {
                                        setState(() {
                                          onSearch = true;
                                        });
                                      }
                                    },
                                  ),
                                  SizedBox(height: spacingHeightSmall),
                                  Text(
                                    list.values
                                            .firstWhereOrNull((element) =>
                                                element.name == "HEADLINE")
                                            ?.data ??
                                        "",
                                    style: TextStyle(
                                        color: theme == "L"
                                            ? ThemeColors.dark
                                            : ThemeColors.light,
                                        fontSize: h2,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                            ),
                            detailView(list),
                            searchView(),
                          ],
                        ),
                      )
                    ],
                  )),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }

  Widget detailView(Box list) {
    return Visibility(
      visible: !onSearch,
      child: Expanded(
        child: Container(
          width: phoneWidth,
          padding: EdgeInsets.only(bottom: defaultPadding),
          decoration: bottomBackground == null
              ? BoxDecoration(
                  color: ThemeColors.light,
                )
              : BoxDecoration(
                  image: bottomBackgroundType == "IMAGE"
                      ? DecorationImage(
                          image: CachedNetworkImageProvider(
                              bottomBackground!.data ?? ""),
                          fit: BoxFit.cover,
                        )
                      : null,
                  color: bottomBackgroundType == "COLOR"
                      ? Color(int.parse(
                          "0xff${bottomBackground!.data ?? "FFFFFF"}"))
                      : null,
                ),
          child: FutureBuilder(
            future: future,
            builder: (BuildContext context, AsyncSnapshot snapshot) {
              if (snapshot.connectionState == ConnectionState.done) {
                if (snapshot.hasData) {
                  Faq faq = Faq.fromJson(snapshot.data["data"]);

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (faq.categories!.isNotEmpty)
                        Padding(
                          padding: EdgeInsets.all(defaultPadding),
                          child: Text(
                            list.values
                                    .firstWhereOrNull((element) =>
                                        element.name == "CATEGORIES_TITLE")
                                    ?.data ??
                                "",
                            style: TextStyle(
                                color: theme == "L"
                                    ? ThemeColors.dark
                                    : ThemeColors.light,
                                fontSize: h3,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                      SingleChildScrollView(
                        padding: EdgeInsets.only(
                            left: defaultPadding, bottom: defaultPadding),
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children:
                              List.generate(faq.categories!.length, (index) {
                            return GestureDetector(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (BuildContext context) =>
                                          FaqList(
                                        categoryTitle:
                                            faq.categories![index].name,
                                        questions:
                                            faq.categories![index].questions!,
                                      ),
                                    ));
                              },
                              child: Container(
                                margin:
                                    EdgeInsets.only(right: defaultInnerPadding),
                                padding: EdgeInsets.all(defaultInnerPadding),
                                height: phoneWidth / 3.5,
                                width: phoneWidth / 3.5,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(20),
                                  color: ThemeColors.light,
                                  border: Border.all(
                                    color: theme == "L"
                                        ? ThemeColors.gray
                                        : ThemeColors.disabled,
                                  ),
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    if (faq.categories![index].image != null)
                                      CachedNetworkImage(
                                        imageUrl: faq.categories![index].image!,
                                        width: phoneWidth / 10,
                                        fit: BoxFit.fitWidth,
                                        placeholder: (context, url) {
                                          return Center(
                                            child: CircularProgressIndicator(
                                              color: ThemeColors.primaryDark,
                                            ),
                                          );
                                        },
                                        errorWidget: (context, url, error) {
                                          return Icon(
                                            Icons.error_outline_outlined,
                                            color: ThemeColors.primaryDark,
                                          );
                                        },
                                      ),
                                    SizedBox(height: spacingHeightSmall),
                                    AutoSizeText(
                                      faq.categories![index].name,
                                      style: TextStyle(
                                        color: ThemeColors.dark,
                                        fontSize: h4,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 2,
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }),
                        ),
                      ),
                      if (faq.questions!.isNotEmpty)
                        Padding(
                          padding: EdgeInsets.all(defaultPadding),
                          child: Text(
                            list.values
                                    .firstWhereOrNull((element) =>
                                        element.name == "QUESTIONS_TITLE")
                                    ?.data ??
                                "",
                            style: TextStyle(
                                color: theme == "L"
                                    ? ThemeColors.dark
                                    : ThemeColors.light,
                                fontSize: h3,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                      Column(
                        children: List.generate(faq.questions!.length, (index) {
                          return GestureDetector(
                            onTap: () {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (BuildContext context) =>
                                        FaqDetail(
                                            question: faq.questions![index]),
                                  ));
                            },
                            child: Container(
                              margin: EdgeInsets.symmetric(
                                horizontal: defaultPadding,
                                vertical: spacingHeightSmall,
                              ),
                              decoration: BoxDecoration(
                                color: ThemeColors.light,
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: theme == "L"
                                      ? ThemeColors.disabled
                                      : ThemeColors.gray,
                                ),
                              ),
                              child: ListTile(
                                title: Text(
                                  faq.questions![index].question,
                                  style: TextStyle(
                                    color: ThemeColors.dark,
                                    fontSize: h4,
                                  ),
                                ),
                                trailing: ImageIcon(
                                  AssetImage(
                                      "assets/icons/${boxGlobal.get("iconSet")}/Standard_Moreleft.png"),
                                  color: theme == "L"
                                      ? ThemeColors.dark
                                      : ThemeColors.light,
                                ),
                              ),
                            ),
                          );
                        }),
                      ),
                    ],
                  );
                }
              }
              return Container();
            },
          ),
        ),
      ),
    );
  }

  Widget searchView() {
    return Visibility(
      visible: onSearch,
      child: Expanded(
        child: Container(
          width: phoneWidth,
          padding: EdgeInsets.only(bottom: defaultPadding),
          decoration: bottomBackground == null
              ? BoxDecoration(
                  color: ThemeColors.light,
                )
              : BoxDecoration(
                  image: bottomBackgroundType == "IMAGE"
                      ? DecorationImage(
                          image: CachedNetworkImageProvider(
                              bottomBackground!.data ?? ""),
                          fit: BoxFit.cover,
                        )
                      : null,
                  color: bottomBackgroundType == "COLOR"
                      ? Color(int.parse(
                          "0xff${bottomBackground!.data ?? "FFFFFF"}"))
                      : null,
                ),
          child: StreamBuilder(
            stream: streamController.stream.asBroadcastStream(),
            builder: (BuildContext context, AsyncSnapshot snapshot) {
              if (snapshot.hasData) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(top: defaultPadding),
                      child: Text(
                        "Search Result",
                        style: TextStyle(
                            color: theme == "L"
                                ? ThemeColors.dark
                                : ThemeColors.light,
                            fontSize: h1,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
                    SizedBox(height: spacingHeightMedium),
                    FutureBuilder(
                        future: getFaqSearch(snapshot.data),
                        builder:
                            (BuildContext context, AsyncSnapshot snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.done) {
                            List<Question> searchList =
                                (snapshot.data['data']['questions'] as List)
                                    .map((data) => Question.fromJson(data))
                                    .toList();

                            return Column(
                              children:
                                  List.generate(searchList.length, (index) {
                                return GestureDetector(
                                  onTap: () {
                                    Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (BuildContext context) =>
                                              FaqDetail(
                                                  question: searchList[index]),
                                        ));
                                  },
                                  child: Container(
                                    margin: EdgeInsets.symmetric(
                                      horizontal: defaultPadding,
                                      vertical: spacingHeightSmall,
                                    ),
                                    decoration: BoxDecoration(
                                      color: ThemeColors.light,
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(
                                        color: theme == "L"
                                            ? ThemeColors.disabled
                                            : ThemeColors.gray,
                                      ),
                                    ),
                                    child: ListTile(
                                      title: Text(
                                        searchList[index].question,
                                        style: TextStyle(
                                          color: ThemeColors.dark,
                                          fontSize: h4,
                                        ),
                                      ),
                                      trailing: ImageIcon(
                                        AssetImage(
                                            "assets/icons/${boxGlobal.get("iconSet")}/Standard_Moreleft.png"),
                                        color: theme == "L"
                                            ? ThemeColors.dark
                                            : ThemeColors.light,
                                      ),
                                    ),
                                  ),
                                );
                              }),
                            );
                          }

                          return Container();
                        }),
                  ],
                );
              }
              return Container();
            },
          ),
        ),
      ),
    );
  }

  Future<Map?> getFaq() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.faq();

    return data;
  }

  Future<Map?> getFaqSearch(String? searchValue) async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.faqSearch(searchValue);

    return data;
  }
}
