// import 'dart:convert';
// import 'dart:developer';

// import 'package:amverton/Constant/theme.dart';
// import 'package:amverton/Constant/theme_size.dart';
// import 'package:amverton/Repository/api_get.dart';
// import 'package:amverton/View/Home/web_view.dart';
// import 'package:flutter/material.dart';

// class BookingPage extends StatefulWidget {
//   @override
//   State<BookingPage> createState() => _BookingPageState();
// }

// class _BookingPageState extends State<BookingPage> {
//   late Future future;
//   late String? golfUrl;
//   late String? hotelUrl;
//   late String? themeParkUrl;

//   @override
//   void initState() {
//     super.initState();
//     future = getBooking();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text('Bookings'),
//         centerTitle: true,
//         backgroundColor: Colors.white,
//         foregroundColor: Colors.black,
//         elevation: 0,
//       ),
//       body: FutureBuilder(
//           future: future,
//           builder: (context, snapshot) {
//             if (snapshot.connectionState == ConnectionState.done) {
//               return Padding(
//                 padding: EdgeInsets.all(defaultPadding),
//                 child: Column(
//                   children: [
//                     Expanded(
//                       child: BookingOption(
//                         imagePath: 'assets/images/golf.png',
//                         title: 'Golf',
//                         url: golfUrl,
//                       ),
//                     ),
//                     SizedBox(height: defaultPadding),
//                     Expanded(
//                       child: BookingOption(
//                         imagePath: 'assets/images/hotel.png',
//                         title: 'Hotel',
//                         url: hotelUrl,
//                       ),
//                     ),
//                     SizedBox(height: defaultPadding),
//                     Expanded(
//                       child: BookingOption(
//                         imagePath: 'assets/images/theme_park.png',
//                         title: 'Theme Park',
//                         url: themeParkUrl,
//                       ),
//                     ),
//                     SizedBox(height: defaultPadding),
//                     Expanded(
//                       child: Opacity(
//                         opacity: 0.0,
//                         child: BookingOption(
//                           imagePath: 'assets/images/coming_soon_food.png',
//                           title: 'Coming Soon',
//                           url: null,
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               );
//             }

//             return Container();
//           }),
//     );
//   }

//   Future<Map?> getBooking() async {
//     ApiGet apiGet = ApiGet();

//     Map? data = await apiGet.bookingList();

//     golfUrl = null;
//     hotelUrl = null;
//     themeParkUrl = null;

//     if (data != null && data['data'] != null) {
//       golfUrl = data['data']['golf_booking_url'];
//       hotelUrl = data['data']['hotel_booking_url'];
//       themeParkUrl = data['data']['theme_park_booking_url'];
//     }

//     return data;
//   }
// }

// class BookingOption extends StatelessWidget {
//   final String imagePath;
//   final String title;
//   final String? url;

//   const BookingOption({
//     required this.imagePath,
//     required this.title,
//     required this.url,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: () {
//         if (url != null)
//           Navigator.push(
//               context,
//               MaterialPageRoute(
//                 builder: (BuildContext context) => WebView(
//                   url: url!,
//                 ),
//               ));
//       },
//       child: Container(
//         height: 150,
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(12),
//           image: DecorationImage(
//             image: AssetImage(imagePath),
//             fit: BoxFit.cover,
//             colorFilter: ColorFilter.mode(
//               Colors.black.withOpacity(0.4),
//               BlendMode.darken,
//             ),
//           ),
//         ),
//         child: Center(
//           child: Text(
//             title,
//             style: TextStyle(
//               color: Colors.white,
//               fontSize: 32,
//               fontWeight: FontWeight.bold,
//               fontFamily: fontFamily1,
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }
import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/order.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Custom%20Widgets/default_divider2.dart';
import 'package:amverton/View/OrderMenu/order_history_detail_page.dart';
import 'package:amverton/View/Custom%20Widgets/empty_list.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:intl/intl.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;
import 'package:rxdart/subjects.dart';

class BookingPage extends StatefulWidget {
  @override
  State<BookingPage> createState() => _BookingPageState();
}

class _BookingPageState extends State<BookingPage> {
  late Box boxGlobal;
  late Future boxFuture;
  String theme = "L";

  final ScrollController activeScrollController = ScrollController();
  bool activeHasMorePages = false;
  int activeNextPage = 0;
  StreamController activeStreamController = BehaviorSubject();
  List<Order> activeOrderList = [];

  final ScrollController inactiveScrollController = ScrollController();
  bool inactiveHasMorePages = false;
  int inactiveNextPage = 0;
  StreamController inactiveStreamController = BehaviorSubject();
  List<Order> inactiveOrderList = [];

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();
   // getOrder("active", activeNextPage);
   // getOrder("inactive", inactiveNextPage);

    activeScrollController.addListener(() {
      if (activeScrollController.position.pixels ==
          activeScrollController.position.maxScrollExtent) {
        if (activeHasMorePages) getOrder("active", activeNextPage);
      }
    });

    inactiveScrollController.addListener(() {
      if (inactiveScrollController.position.pixels ==
          inactiveScrollController.position.maxScrollExtent) {
        if (inactiveHasMorePages) getOrder("inactive", inactiveNextPage);
      }
    });
  }

  openBox() async {
    boxGlobal = await Hive.openBox("boxGlobal");
    return await Hive.openBox<Asset>('ACCOUNT+ORDER_HISTORY+1');
  }

  @override
  void dispose() {
    activeStreamController.close();
    inactiveStreamController.close();
    activeScrollController.dispose();
    inactiveScrollController.dispose();
    super.dispose();
  }


  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return DefaultTabController(
              length: 2,
              child: Scaffold(
                appBar: AppBar(
                  backgroundColor: Colors.white, 
                  title: Text(
                    list.values
                            .firstWhereOrNull(
                                (element) => element.name == "TITLE")
                            ?.data ??
                        "",
                    style: TextStyle(
                      color: ThemeColors.primaryDark,
                      fontFamily: "Poppins",
                      fontSize: h2,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  iconTheme: IconThemeData(
                    color: ThemeColors.primaryDark,
                  ),
                  bottom: PreferredSize(
                    preferredSize: Size.fromHeight(60),
                    child: Padding( // <-- This adds margin around the entire tab bar
                      padding: EdgeInsets.symmetric(horizontal: 16), // Left & right margin
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(30),
                          ),
                          child: TabBar(
                          indicator: BoxDecoration(
                            gradient: LinearGradient(
                            colors: [
                              Color(0xFF818592),
                              Color(0xFF595C68),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                            borderRadius: BorderRadius.circular(30),
                          ),
                          indicatorSize: TabBarIndicatorSize.tab,
                          labelColor: Colors.white,
                          unselectedLabelColor: ThemeColors.gray,
                          indicatorWeight: 0, // Completely removes underline
                          indicatorPadding: EdgeInsets.zero, // Removes any indicator padding
                          dividerColor: Colors.transparent,
                         tabs: [
                            Tab(child: Container(width: double.infinity, child: Center( child: Text("Activities Booking", style: TextStyle(fontSize: h2,fontWeight: FontWeight.w600,fontFamily: "Poppins"),textAlign: TextAlign.center)))),
                            Tab(child: Container(width: double.infinity, child: Center( child: Text("Past Booking", style: TextStyle(fontSize: h2,fontWeight: FontWeight.w600,fontFamily: "Poppins"),textAlign: TextAlign.center)))),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                body: Container(
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                  child: Column(
                    children: [
                      Expanded(
                        child: TabBarView(
                          children: [
                            active(background, backgroundType),
                            inactive(background, backgroundType),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }
          return Container();
        });
  }

  // @override
  // Widget build(BuildContext context) {
  //   return FutureBuilder(
  //       future: boxFuture,
  //       builder: (BuildContext context, AsyncSnapshot snapshot) {
  //         if (snapshot.connectionState == ConnectionState.done) {
  //           final Box list = snapshot.data;
  //           list.watch();

  //           String? backgroundType = list.values
  //                   .firstWhereOrNull(
  //                       (element) => element.name == "BACKGROUND_TYPE")
  //                   ?.data ??
  //               "";
  //           Asset? background = list.values.firstWhereOrNull(
  //               (element) => element.name == "BACKGROUND_$backgroundType");

  //           return DefaultTabController(
  //             length: 2,
  //             child: Scaffold(
  //               appBar: AppBar(
  //                 title: Text(
  //                   'My Booking',
  //                   style: TextStyle(
  //                     color:
  //                         theme == "L" ? ThemeColors.dark : ThemeColors.light,
  //                   ),
  //                 ),
  //                 centerTitle: true,
  //                 backgroundColor: Colors.white,
  //                 foregroundColor: Colors.black,
  //                 elevation: 0,
  //                 iconTheme: IconThemeData(
  //                   color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
  //                 ),
  //               ),
  //               body: Container(
  //                 decoration: background == null
  //                     ? null
  //                     : BoxDecoration(
  //                         image: backgroundType == "IMAGE"
  //                             ? DecorationImage(
  //                                 image: CachedNetworkImageProvider(
  //                                     background.data ?? ""),
  //                                 fit: BoxFit.cover,
  //                               )
  //                             : null,
  //                         color: backgroundType == "COLOR"
  //                             ? Color(int.parse(
  //                                 "0xff${background.data ?? "FFFFFF"}"))
  //                             : null,
  //                       ),
  //                 child: Column(
  //                   children: [
  //                     TabBar(
  //                       padding: EdgeInsets.symmetric(
  //                           horizontal: defaultPadding + 10),
  //                       labelColor: ThemeColors.primaryDark,
  //                       indicator: BoxDecoration(
  //                         color: ThemeColors.primaryDark,
  //                       ),
  //                       indicatorWeight: 1,
  //                       indicatorPadding: EdgeInsets.only(top: 40),
  //                       unselectedLabelColor: ThemeColors.gray,
  //                       tabs: const [
  //                         Tab(text: "Active"),
  //                         Tab(text: "Past"),
  //                       ],
  //                     ),
  //                     Expanded(
  //                       child: TabBarView(
  //                         children: [
  //                           active(background, backgroundType),
  //                           inactive(background, backgroundType),
  //                         ],
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //               ),
  //             ),
  //           );
  //         }
  //         return Container();
  //       });
  // }

  Widget active(background, backgroundType) {
    return Container(
      decoration: background == null
          ? null
          : BoxDecoration(
              image: backgroundType == "IMAGE"
                  ? DecorationImage(
                      image: CachedNetworkImageProvider(background.data ?? ""),
                      fit: BoxFit.cover,
                    )
                  : null,
              color: backgroundType == "COLOR"
                  ? Color(int.parse("0xff${background.data ?? "FFFFFF"}"))
                  : null,
            ),
      height: phoneHeight,
      child: RefreshIndicator(
        edgeOffset: verticalPaddingMedium,
        color: ThemeColors.primaryDark,
        onRefresh: () {
          return Future.delayed(const Duration(seconds: 1), () {
            setState(() {
              activeHasMorePages = false;
              activeNextPage = 0;
              activeOrderList.clear();
              getOrder("active", activeNextPage);
            });
          });
        },
        child: StreamBuilder(
            stream: activeStreamController.stream,
            builder: (BuildContext context, AsyncSnapshot snapshot) {
              if (snapshot.hasError) {
                return Container();
              } else {
                if (activeOrderList.isEmpty) {
                  return EmptyList(theme: theme);
                } else {
                  return ListView.builder(
                    controller: activeScrollController,
                    physics: AlwaysScrollableScrollPhysics(),
                    padding: EdgeInsets.all(defaultPadding),
                    itemCount: activeOrderList.length,
                    itemBuilder: (BuildContext context, int index) {
                      Order order = activeOrderList[index];
                      return GestureDetector(
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (BuildContext context) =>
                                      OrderHistoryDetailPage(
                                        id: activeOrderList[index].id,
                                      )));
                        },
                        child: orderCard(order, true),
                      );
                    },
                  );
                }
              }
            }),
      ),
    );
  }

  Widget inactive(background, backgroundType) {
    return RefreshIndicator(
      edgeOffset: verticalPaddingMedium,
      color: ThemeColors.primaryDark,
      onRefresh: () {
        return Future.delayed(const Duration(seconds: 1), () {
          setState(() {
            inactiveHasMorePages = false;
            inactiveNextPage = 0;
            inactiveOrderList.clear();
            getOrder("inactive", inactiveNextPage);
          });
        });
      },
      child: StreamBuilder(
          stream: inactiveStreamController.stream,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            if (snapshot.hasError) {
              return Container();
            } else {
              if (inactiveOrderList.isEmpty) {
                return EmptyList(theme: theme);
              } else {
                return ListView.builder(
                  controller: inactiveScrollController,
                  physics: AlwaysScrollableScrollPhysics(),
                  padding: EdgeInsets.all(defaultPadding),
                  itemCount: inactiveOrderList.length,
                  itemBuilder: (BuildContext context, int index) {
                    Order order = inactiveOrderList[index];
                    return GestureDetector(
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (BuildContext context) =>
                                    OrderHistoryDetailPage(
                                      id: inactiveOrderList[index].id,
                                    )));
                      },
                      child: orderCard(order, false),
                    );
                  },
                );
              }
            }
          }),
    );
  }

  Container orderCard(Order order, bool active) {
    return Container(
      margin: EdgeInsets.only(bottom: defaultInnerPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: kElevationToShadow[3],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            decoration: BoxDecoration(
              color: active ? ThemeColors.tertiaryDark : ThemeColors.disabled,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            padding: EdgeInsets.symmetric(
              horizontal: defaultPadding,
              vertical: defaultInnerPadding,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'Reservation',
                  style: TextStyle(
                    fontSize: h2,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.only(
              bottom: defaultPadding,
              left: defaultInnerPadding,
              right: defaultInnerPadding,
            ),
            child: Column(
              children: [
                SizedBox(height: spacingHeightMedium),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Row(
                              children: [
                                Text(
                                  'Kazuma Japanese Restaurant',
                                  style: TextStyle(
                                    fontSize: h3,
                                    fontWeight: FontWeight.bold,
                                    fontFamily: fontFamily2
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              children: [
                                Text(
                                  'ID: ',
                                  style: TextStyle(
                                    fontSize: h3,
                                    fontWeight: FontWeight.bold,
                                    fontFamily: fontFamily2,
                                  ),
                                ),
                                Text(
                                  order.number.toString(),
                                  style: TextStyle(
                                    fontSize: h3,
                                    fontWeight: FontWeight.bold,
                                    decoration: TextDecoration.underline,
                                    fontFamily: fontFamily2,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        SizedBox(height: spacingHeightSmall),
                        Text(
                          DateFormat('yyyy MMM dd  HH:mm')
                              .format(order.createdAt),
                          style: TextStyle(
                            fontSize: h3,
                          ),
                        ),
                      ],
                    ),
                    IgnorePointer(
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                             colors: [
                              Color(0xFF818592),
                              Color(0xFF595C68),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: ElevatedButton(
                          onPressed: () {},
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            shadowColor: Colors.transparent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                          child: Text(
                            'View',
                            style: TextStyle(
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                Divider(height: 20, thickness: 1),
                Container(
                  margin: EdgeInsets.symmetric(horizontal: defaultPadding),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.store, color: Colors.black54),
                          Text(
                            '5',
                            style: TextStyle(
                              fontSize: h3,
                              fontFamily: fontFamily2,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(
                            width: defaultInnerPadding,
                          ),
                          Text(
                            DateFormat('yyyy MMM dd  HH:mm')
                                .format(order.createdAt),
                            style: TextStyle(
                              fontSize: h3,
                              fontFamily: fontFamily2,
                            ),
                          ),
                        ],
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                           Text(
                            DateFormat('yyyy MMM dd  HH:mm')
                                .format(order.createdAt),
                            style: TextStyle(
                              fontSize: h3,
                              fontFamily: fontFamily2,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget detailList(String title, String data) {
    return Row(
      children: [
        Container(
          width: phoneWidth / 3,
          child: Text(
            "$title:",
            style: TextStyle(
              color: ThemeColors.gray,
              fontSize: h3,
            ),
          ),
        ),
        SizedBox(width: spacingWidth),
        Expanded(
          child: Text(
            data,
            style: TextStyle(
              color: ThemeColors.dark,
              fontSize: h4,
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  Future<Map?> getOrder(String filter, int nextPage) async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.orderList(nextPage, filter);

    if (data != null && data['data'] != null) {
      List<Order> newList = (data['data']['orders'] as List)
          .map((data) => Order.fromJson(data))
          .toList();

      if (filter == "active") {
        activeHasMorePages = data["data"]["has_more_pages"];
        if (activeHasMorePages) {
          activeNextPage = data["data"]["next_page"] ?? 0;
        }
        activeOrderList.addAll(newList);
        activeStreamController.add(data);
      }

      if (filter == "inactive") {
        inactiveHasMorePages = data["data"]["has_more_pages"];
        if (inactiveHasMorePages) {
          inactiveNextPage = data["data"]["next_page"] ?? 0;
        }
        inactiveOrderList.addAll(newList);
        inactiveStreamController.add(data);
      }
    }
    return data;
  }
}