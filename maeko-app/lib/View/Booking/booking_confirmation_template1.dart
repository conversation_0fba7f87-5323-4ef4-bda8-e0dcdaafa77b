import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/bottom_bar.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/main.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';
import 'package:collection/collection.dart';

class BookingConfirmationTemplate1 extends StatefulWidget {
  static String routeName = "/booking_confirmation/1";
  final dynamic args;

  const BookingConfirmationTemplate1({Key? key, required this.args})
      : super(key: key);

  @override
  _BookingConfirmationTemplate1State createState() =>
      _BookingConfirmationTemplate1State();
}

class _BookingConfirmationTemplate1State
    extends State<BookingConfirmationTemplate1> {
  late Future boxFuture;
  late Box boxGlobal;

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>("BOOKINGS+MAIN+1");
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return Scaffold(
              appBar: AppBar(title: const Text("Booking Confirmation")),
              body: SingleChildScrollView(
                padding: EdgeInsets.all(defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: Text(
                        "Confirm Your Reservation!",
                        style: TextStyle(
                            color: ThemeColors.dark,
                            fontSize: h1,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
                    SizedBox(height: spacingHeightMedium),
                    label("Name", widget.args["name"]),
                    label(
                        "Phone Number",
                        widget.args["countryCode"] +
                            widget.args["phoneNumber"]),
                    label("Number of guests",
                        widget.args["numberGuests"].toString()),
                    label(
                        "Date and Time",
                        DateFormat('hh:mm a, d MMM yyyy, EEEE')
                            .format(DateTime.parse(widget.args["dateTime"]))),
                    // GestureDetector(
                    //   onTap: () {
                    //     // TODO: move to choose payment method
                    //   },
                    //   child: Container(
                    //       margin: EdgeInsets.only(top: spacingHeightLarge),
                    //       child: Row(
                    //         children: [
                    //           Text(
                    //             "Payment Method",
                    //             style: TextStyle(
                    //                 color: Color(
                    //                     int.parse(boxGlobal.get("colorD1"))),
                    //                 fontSize: h3),
                    //           ),
                    //           const Spacer(),
                    //           Text(
                    //             "Debit Card",
                    //             style: TextStyle(
                    //                 color: Color(
                    //                     int.parse(boxGlobal.get("colorD1"))),
                    //                 fontSize: h2,
                    //                 fontWeight: FontWeight.bold),
                    //           ),
                    //           ImageIcon(AssetImage(
                    //               "assets/icons/${boxGlobal.get("iconSet")}/Standard_Moreleft.png")),
                    //         ],
                    //       )),
                    // )
                  ],
                ),
              ),
              bottomNavigationBar: Padding(
                  padding: EdgeInsets.fromLTRB(
                    defaultInnerPadding,
                    defaultInnerPadding,
                    defaultInnerPadding,
                    bottomPaddingWithoutBar,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      DefaultButton(
                        text: "Confirm",
                        buttonColor: ThemeColors.primaryDark,
                        onPressed: () {
                          makeBooking();
                        },
                      ),
                      SizedBox(height: spacingHeightSmall),
                      DefaultButton(
                        text: "Edit",
                        textColor: ThemeColors.primaryDark,
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    ],
                  )),
            );
          }
          return Container();
        });
  }

  void makeBooking() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    Map? data = await apiPost.makeBooking(
      widget.args["outletId"],
      widget.args["name"],
      widget.args["countryCode"],
      widget.args["phoneNumber"],
      widget.args["numberGuests"],
      widget.args["dateTime"],
    );
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          List<BottomBar> bottomBarList = boxGlobal.get("bottomBar") ?? [];
          if (bottomBarList.isNotEmpty) {
            int bookingIndex = bottomBarList
                .indexWhere((element) => element.name == "BOOKINGS");
            if (bookingIndex != -1) {
              final dynamic navigationBar = barGlobalKey.currentWidget;
              navigationBar.onTap(bookingIndex);

              Navigator.pushNamed(
                  context, "/booking_history/${bookingMainTemplate}");
            }
          }
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  Widget label(String title, String data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: spacingHeightMedium),
        Text(
          title,
          style: TextStyle(color: ThemeColors.dark, fontSize: h3),
        ),
        SizedBox(height: spacingHeightSmall),
        Text(
          data,
          style: TextStyle(
              color: ThemeColors.dark,
              fontSize: h2,
              fontWeight: FontWeight.bold),
        ),
        SizedBox(height: spacingHeightSmall),
      ],
    );
  }
}
