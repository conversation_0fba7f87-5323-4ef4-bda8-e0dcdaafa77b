import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/user.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_datetimefield1.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_phonefield1.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';
import 'package:collection/collection.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class MakeBookingTemplate1 extends StatefulWidget {
  static String routeName = "/make_booking/1";
  final dynamic args;

  const MakeBookingTemplate1({Key? key, required this.args}) : super(key: key);

  @override
  _MakeBookingTemplate1State createState() => _MakeBookingTemplate1State();
}

class _MakeBookingTemplate1State extends State<MakeBookingTemplate1> {
  late Future boxFuture;
  late Box box;
  late Box boxGlobal;
  late User user;

  bool tnc = false;
  List<bool> selectedPax = List.generate(7, (index) => false);
  String selectedCountryCode = "";
  List<String> countryCodeList = [];
  TextEditingController nameController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController timeController = TextEditingController();
  TextEditingController dateController = TextEditingController();
  TextEditingController remarkController = TextEditingController();

  TextStyle? titleTextStyle;

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();
  }

  openBox() async {
    box = await Hive.openBox("box");
    boxGlobal = await Hive.openBox('boxGlobal');

    user = box.get("user");
    nameController.text = "";// user.name;

    selectedCountryCode = "";// user.countryCode;
    phoneController.text = user.phone;

    timeController.text = TimeOfDay.now().format(context);
    dateController.text = DateFormat('yyyy-MM-dd').format(DateTime.now());

    countryCodeList = boxGlobal.get("countryCode") ?? [];
    selectedCountryCode = countryCodeList.first;

    return await Hive.openBox<Asset>("BOOKINGS+MAIN+1");
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            titleTextStyle = TextStyle(color: ThemeColors.dark, fontSize: h3);

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                appBar: AppBar(
                  title: Text(
                    list.values
                            .firstWhereOrNull(
                                (element) => element.name == "TITLE")
                            ?.data ??
                        "",
                  ),
                  actions: [
                    IconButton(
                        onPressed: () {
                          Navigator.pushNamed(context,
                              "/booking_history/${bookingMainTemplate}");
                        },
                        icon: ImageIcon(AssetImage(
                            "assets/icons/${boxGlobal.get("iconSet")}/History Icon.png")))
                  ],
                ),
                body: SingleChildScrollView(
                  padding: EdgeInsets.only(
                    top: defaultPadding,
                    bottom: bottomPaddingWithoutBar,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding:
                            EdgeInsets.symmetric(horizontal: defaultPadding),
                        child: Text("How many pax?", style: titleTextStyle),
                      ),
                      SingleChildScrollView(
                        padding: EdgeInsets.only(
                            top: defaultInnerPadding, bottom: defaultPadding),
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: List.generate(7, (index) {
                            return GestureDetector(
                              onTap: () {
                                setState(() {
                                  selectedPax =
                                      List.generate(7, (index) => false);
                                  selectedPax[index] = true;
                                });
                              },
                              child: Container(
                                alignment: Alignment.center,
                                width: phoneWidth / 6.5,
                                height: phoneWidth / 6.5,
                                margin: EdgeInsets.only(left: defaultPadding),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: selectedPax[index]
                                      ? ThemeColors.primaryDark
                                      : ThemeColors.disabled,
                                ),
                                child: Text(
                                  (index + 1).toString(),
                                  style: TextStyle(color: ThemeColors.light),
                                ),
                              ),
                            );
                          }),
                        ),
                      ),
                      Padding(
                        padding:
                            EdgeInsets.symmetric(horizontal: defaultPadding),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("Name", style: titleTextStyle),
                            DefaultTextField1(
                              controller: nameController,
                              hintText: "Abc",
                            ),
                            Text("Phone Number", style: titleTextStyle),
                            DefaultPhoneField1(
                              controller: phoneController,
                              countryCodeList: countryCodeList,
                              selectedCountryCode: selectedCountryCode,
                              onDropdownChanged: (value) {
                                setState(() {
                                  setState(() {
                                    selectedCountryCode = value!;
                                  });
                                });
                              },
                            ),
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("Time Slot", style: titleTextStyle),
                                      DefaultDateTimeField1(
                                        controller: timeController,
                                        onTap: () {
                                          selectTime();
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(width: spacingWidth),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("Pick a Date",
                                          style: titleTextStyle),
                                      DefaultDateTimeField1(
                                        controller: dateController,
                                        onTap: () {
                                          selectDate();
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Text("Booking Fees", style: titleTextStyle),
                            Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: spacingHeightSmall),
                              child: Text(
                                "${important_variables.currency}15",
                                style: TextStyle(
                                  color: ThemeColors.primaryDark,
                                  fontSize: h2,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            // Text("Remarks", style: titleTextStyle),
                            // DefaultTextField(
                            //   controller: remarkController,
                            //   maxLines: 3,
                            //   hintText:
                            //       "Eg: baby chair, near entrance, wheel chair friendly.",
                            // ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                RichText(
                                  text: TextSpan(
                                    style: titleTextStyle,
                                    children: [
                                      const TextSpan(text: 'I agree to the '),
                                      TextSpan(
                                        text: 'Terms and Conditions',
                                        style: TextStyle(
                                            color: ThemeColors.primaryDark),
                                        recognizer: TapGestureRecognizer()
                                          ..onTap = () {
                                            // Navigator.push(
                                            //     context,
                                            //     MaterialPageRoute(
                                            //         builder: (BuildContext context) =>
                                            //         const Register1Template1()));
                                          },
                                      ),
                                    ],
                                  ),
                                ),
                                Checkbox(
                                  value: tnc,
                                  onChanged: (bool? value) {
                                    if (value != null) {
                                      setState(() {
                                        tnc = value;
                                      });
                                    }
                                  },
                                ),
                              ],
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
                bottomNavigationBar: Padding(
                  padding: EdgeInsets.fromLTRB(
                    defaultInnerPadding,
                    defaultInnerPadding,
                    defaultInnerPadding,
                    bottomPaddingWithoutBar,
                  ),
                  child: DefaultButton(
                    text: list.values
                            .firstWhereOrNull(
                                (element) => element.name == "BOOK_BUTTON_TEXT")
                            ?.data ??
                        "",
                    buttonColor:
                        tnc ? ThemeColors.primaryDark : ThemeColors.disabled,
                    textColor: tnc ? null : ThemeColors.dark,
                    onPressed: () {
                      if (tnc) proceed();
                    },
                  ),
                ),
              ),
            );
          }
          return Container();
        });
  }

  void proceed() async {
    int numberGuests = selectedPax.indexWhere((element) => element) + 1;
    String dateTime = () {
      String time = "";
      if (timeController.text.toUpperCase().contains("M"))
        time = DateFormat("HH:mm")
            .format(DateFormat("hh:mm a").parse(timeController.text));
      else
        time = timeController.text;

      return "${dateController.text} $time";
    }();

    if (numberGuests == 0) {
      defaultDialog(context, 400, "Please select the number of pax.", () {})
          .show();
    } else if (nameController.text.isEmpty || phoneController.text.isEmpty) {
      defaultDialog(
              context, 400, "Please fill in all the blanks to continue.", () {})
          .show();
    } else {
      Navigator.pushNamed(
          context, "/booking_confirmation/${bookingMainTemplate}",
          arguments: {
            "outletId": widget.args["outletId"],
            "name": nameController.text,
            "countryCode": selectedCountryCode,
            "phoneNumber": phoneController.text,
            "numberGuests": numberGuests,
            "dateTime": dateTime,
          });
    }
  }

  void selectTime() async {
    String time = "";
    if (timeController.text.toUpperCase().contains("M"))
      time = DateFormat("HH:mm")
          .format(DateFormat("hh:mm a").parse(timeController.text));
    else
      time = timeController.text;

    final TimeOfDay? picked = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(DateFormat.Hm().parse(time)));
    if (picked != null) {
      setState(() {
        timeController.text = picked.format(context);
      });
    }
  }

  void selectDate() async {
    final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: DateTime.parse(dateController.text),
        firstDate: DateTime.now(),
        lastDate: DateTime(DateTime.now().year + 1));
    if (picked != null) {
      setState(() {
        dateController.text = DateFormat('yyyy-MM-dd').format(picked);
      });
    }
  }
}
