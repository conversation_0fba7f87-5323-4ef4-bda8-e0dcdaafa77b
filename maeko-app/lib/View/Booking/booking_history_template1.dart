import 'dart:async';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/booking.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/empty_list.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';
import 'package:collection/collection.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class BookingHistoryTemplate1 extends StatefulWidget {
  static String routeName = "/booking_history/1";

  const BookingHistoryTemplate1({Key? key}) : super(key: key);

  @override
  _BookingHistoryTemplate1State createState() =>
      _BookingHistoryTemplate1State();
}

class _BookingHistoryTemplate1State extends State<BookingHistoryTemplate1> {
  late Future boxFuture;
  late Box boxGlobal;

  final ScrollController scrollController = ScrollController();
  bool hasMorePages = false;
  int nextPage = 0;
  StreamController streamController = StreamController();
  List<Booking> bookingList = [];

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();
    getBooking();

    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (hasMorePages) getBooking();
      }
    });
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>("BOOKINGS+MAIN+1");
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return Scaffold(
              appBar: AppBar(
                title: const Text("My Booking"),
              ),
              body: StreamBuilder(
                  stream: streamController.stream,
                  builder: (BuildContext context, AsyncSnapshot snapshot) {
                    if (snapshot.hasError) {
                      return Container();
                    } else {
                      if (bookingList.isEmpty) {
                        return EmptyList(theme: bookingTheme);
                      } else {
                        return RefreshIndicator(
                          color: ThemeColors.primaryDark,
                          onRefresh: () {
                            return Future.delayed(const Duration(seconds: 1),
                                () {
                              setState(() {
                                hasMorePages = false;
                                nextPage = 0;
                                bookingList.clear();
                                getBooking();
                              });
                            });
                          },
                          child: SizedBox(
                            height: phoneHeight,
                            child: SingleChildScrollView(
                              physics: const AlwaysScrollableScrollPhysics(),
                              padding: EdgeInsets.all(defaultPadding),
                              child: Column(
                                  children: List.generate(bookingList.length,
                                      (index) {
                                return bookingView(bookingList[index]);
                              })),
                            ),
                          ),
                        );
                      }
                    }
                  }),
            );
          }
          return Container();
        });
  }

  Widget bookingView(Booking booking) {
    return GestureDetector(
      onTap: () {},
      child: Container(
        padding: EdgeInsets.all(defaultInnerPadding),
        margin: EdgeInsets.only(bottom: defaultInnerPadding),
        width: phoneWidth,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: ThemeColors.disabled,
            )),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CircleAvatar(
              // radius: phoneWidth * 0.016,
              backgroundColor: ThemeColors.disabled,
              child: Padding(
                padding: const EdgeInsets.all(5),
                child: Image.asset(
                  "assets/icons/A/Booking 01.png",
                  color: ThemeColors.light,
                ),
              ),
            ),
            SizedBox(width: spacingWidth),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Table Booking",
                    style: TextStyle(
                        color: ThemeColors.dark,
                        fontSize: h2,
                        fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: spacingHeightSmall),
                  Text(
                    DateFormat('hh:mm a, d MMM yyyy, EEEE')
                        .format(booking.dateTime),
                    style: TextStyle(color: ThemeColors.dark, fontSize: h4),
                  ),
                  Text(
                    "Deposit: ${important_variables.currency}${booking.depositAmount}",
                    style: TextStyle(color: ThemeColors.dark, fontSize: h4),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  "${booking.numberOfGuests} Seats",
                  style: TextStyle(
                      color: ThemeColors.dark,
                      fontSize: h2,
                      fontWeight: FontWeight.bold),
                ),
                SizedBox(height: spacingHeightSmall),
                Text(
                  booking.status,
                  style:
                      TextStyle(color: ThemeColors.primaryDark, fontSize: h4),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Future<Map?> getBooking() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.bookingList();

    if (data != null && data['data'] != null) {
      List<Booking> newList = (data['data']['bookings'] as List)
          .map((data) => Booking.fromJson(data))
          .toList();

      hasMorePages = data["data"]["has_more_pages"];
      if (hasMorePages) {
        nextPage = data["data"]["next_page"] ?? 0;
      }
      bookingList.addAll(newList);
      streamController.add(data);
    }
    return data;
  }
}
