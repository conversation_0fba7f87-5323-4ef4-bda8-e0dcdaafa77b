import 'dart:convert';
import 'dart:developer';

import 'package:amverton/Constant/theme.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Custom%20Widgets/default_address_phone_field1.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_dropdown1.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
import 'package:flutter/material.dart';

class DiningDetailTemplate1 extends StatefulWidget {
  @override
  State<DiningDetailTemplate1> createState() => _DiningDetailTemplate1State();
}

class _DiningDetailTemplate1State extends State<DiningDetailTemplate1> {
  late Future future;
  late String? meltingPotUrl;
  late String? xinCuisineUrl;
  late String? restaurant3Url;

  @override
  void initState() {
    super.initState();
    future = getDining();
  }

   Future<Map?> getDining() async {
    ApiGet apiGet = ApiGet();

    Map? data = await apiGet.bookingList();

    meltingPotUrl = null;
    xinCuisineUrl = null;
    restaurant3Url = null;

    if (data != null && data['data'] != null) {
      meltingPotUrl = data['data']['melting_pot_url'];
      xinCuisineUrl = data['data']['xin_cuisine_url'];
      restaurant3Url = data['data']['restaurant_3_url'];
    }
    return data;
  }
  
  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: future,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
         // final Box list = snapshot.data;
         // list.watch();

          // String? backgroundType = list.values
          //         .firstWhereOrNull((element) =>
          //             element.name == "PRODUCT_DETAIL_BACKGROUND_TYPE")
          //         ?.data ??
          //     "";
          // Asset? background = list.values.firstWhereOrNull((element) =>
          //     element.name == "PRODUCT_DETAIL_BACKGROUND_$backgroundType");

          return GestureDetector(
            onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
            behavior: HitTestBehavior.translucent,
            child: Scaffold(
              extendBodyBehindAppBar: true,
              appBar: AppBar(
                backgroundColor: Colors.transparent,
                leading: GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    margin: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                        shape: BoxShape.circle, color: ThemeColors.gray),
                    child: Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              body: Column(
                children: [
                  Expanded(
                    flex: 7,
                    child: Container(
                      height: phoneHeight,
                      // decoration: background == null
                      //     ? null
                      //     : BoxDecoration(
                      //         image: backgroundType == "IMAGE"
                      //             ? DecorationImage(
                      //                 image: CachedNetworkImageProvider(
                      //                     background.data ?? ""),
                      //                 fit: BoxFit.cover,
                      //               )
                      //             : null,
                      //         color: backgroundType == "COLOR"
                      //             ? Color(int.parse("0xff${background.data ?? "FFFFFF"}"))
                      //             : null,
                      //       ),
                      child: Stack(
                        children: [
                          // Top banner image
                          Container(
                            height: 250,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: AssetImage('assets/images/Kazuma-Dining.jpg'),
                                fit: BoxFit.cover,
                              ),
                            ),
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.black.withOpacity(0.4),
                                    Colors.black.withOpacity(0.2),
                                  ],
                                  begin: Alignment.bottomCenter,
                                  end: Alignment.topCenter,
                                ),
                              ),
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'TASTE THE FRESHNESS OF JAPAN',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                        fontWeight: FontWeight.normal,
                                        fontFamily: fontFamily2,
                                        letterSpacing: 1.5,
                                      ),
                                    ),
                                    SizedBox(height: 8),
                                    Text(
                                      'Kazuma Japanese\nRestaurant',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 32,
                                        fontWeight: FontWeight.bold,
                                        fontFamily: fontFamily2,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          // Scrollable Content
                          Positioned(
                            top: 250, // Start right after banner height
                            left: 0,
                            right: 0,
                            bottom: 0,
                            child: SingleChildScrollView(
                              child: Column(
                                children: [
                                  // Restaurant Description
                                  Padding(
                                    padding: EdgeInsets.all(20),
                                    child: Text(
                                      'Synonymous with harmony, peace and serenity, Kazuma offers authentic Japanese cuisine with fresh ingredients air-flown from Japan. Treat yourself to a selection of sushi and sashimi during the day and perfectly grilled kushiyaki at night. For something quick yet delightful, try the value-for-money teishoku set.',
                                      style: TextStyle(
                                        fontSize: 16,
                                        height: 1.5,
                                        color: Colors.black87,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),

                                  // Restaurant Info
                                  Container(
                                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                                    child: Column(
                                      children: [
                                        Text(
                                          'Dress Code: Smart Casual',
                                          style: TextStyle(
                                            fontSize: h2,
                                            fontWeight: FontWeight.w600,
                                            color: ThemeColors.primaryDark,
                                            fontFamily: fontFamily2
                                          ),
                                        ),
                                        SizedBox(height: 4),
                                        Text(
                                          'Lunch',
                                          style: TextStyle(
                                            fontSize: h2,
                                            fontWeight: FontWeight.w600,
                                            color: ThemeColors.primaryDark,
                                            fontFamily: fontFamily2
                                          ),
                                        ),
                                        Text(
                                          'Mon – Sun: 11.30 am – 3 pm',
                                          style: TextStyle(
                                            fontSize: h3,
                                            color: ThemeColors.primaryDark,
                                            fontFamily: fontFamily2
                                          ),
                                        ),
                                        SizedBox(height: 4),
                                        Text(
                                          'Dinner',
                                          style: TextStyle(
                                            fontSize: h2,
                                            fontWeight: FontWeight.w600,
                                            color: ThemeColors.primaryDark,
                                            fontFamily: fontFamily2
                                          ),
                                        ),
                                        Text(
                                          'Mon – Sun: 6 pm – 10 pm',
                                          style: TextStyle(
                                            fontSize: h3,
                                            color: ThemeColors.primaryDark,
                                            fontFamily: fontFamily2
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  // Access the Menu Section
                                  Padding(
                                    padding: EdgeInsets.all(10),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Access the Menu',
                                          style: TextStyle(
                                            fontSize: h1,
                                            fontWeight: FontWeight.bold,
                                            color: ThemeColors.primaryDark,
                                            fontFamily: fontFamily2
                                          ),
                                        ),
                                        SizedBox(height: 10),
                                        
                                        // Menu Items Row
                                        Row(
                                          children: [
                                            // Kazuma Bento
                                            Expanded(
                                              child: Container(
                                                height: 200,
                                                decoration: BoxDecoration(
                                                  borderRadius: BorderRadius.circular(12),
                                                  image: DecorationImage(
                                                    image: AssetImage('assets/images/Kazuma_Bento.jpg'),
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                                child: Container(
                                                  decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.circular(12),
                                                    gradient: LinearGradient(
                                                      colors: [
                                                        Colors.black.withOpacity(0.6),
                                                        Colors.transparent,
                                                      ],
                                                      begin: Alignment.bottomCenter,
                                                      end: Alignment.topCenter,
                                                    ),
                                                  ),
                                                  child: Align(
                                                    alignment: Alignment.bottomLeft,
                                                    child: Padding(
                                                      padding: EdgeInsets.all(16),
                                                      child: Text(
                                                        'Kazuma Bento',
                                                        style: TextStyle(
                                                          color: Colors.white,
                                                          fontSize: h1,
                                                          fontWeight: FontWeight.bold,
                                                          fontFamily: fontFamily2
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: 12),
                                            
                                            // Kazuma Seafood Salad
                                            Expanded(
                                              child: Container(
                                                height: 200,
                                                decoration: BoxDecoration(
                                                  borderRadius: BorderRadius.circular(12),
                                                  image: DecorationImage(
                                                    image: AssetImage('assets/images/Kazuma_Salad.jpg'),
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                                child: Container(
                                                  decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.circular(12),
                                                    gradient: LinearGradient(
                                                      colors: [
                                                        Colors.black.withOpacity(0.6),
                                                        Colors.transparent,
                                                      ],
                                                      begin: Alignment.bottomCenter,
                                                      end: Alignment.topCenter,
                                                    ),
                                                  ),
                                                  child: Align(
                                                    alignment: Alignment.bottomLeft,
                                                    child: Padding(
                                                      padding: EdgeInsets.all(16),
                                                      child: Text(
                                                        'Kazuma Seafood\nSalad',
                                                        style: TextStyle(
                                                          color: Colors.white,
                                                          fontSize: h1,
                                                          fontWeight: FontWeight.bold,
                                                          fontFamily: fontFamily2
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Animation
                          // SlideTransition(
                          //   position: moveAnimation,
                          //   child: Transform.scale(
                          //     scale: scale,
                          //     child: CachedNetworkImage(
                          //       imageUrl: widget.product.image ?? "",
                          //       height: phoneHeight / 3,
                          //       width: phoneWidth,
                          //       fit: BoxFit.cover,
                          //       errorWidget: (context, url, error) => Icon(
                          //         Icons.image,
                          //         size: 200,
                          //         color: ThemeColors.primaryDark,
                          //       ),
                          //     ),
                          //   ),
                          // ),
                        ],
                      ),
                    ),
                  ),
                    Expanded(
                      flex: 1,
                      child: BookTableContainer(),
                    ),
                ],
              ),

            ),
          );
        }
        return Container();
      },
    );
  }

  Widget BookTableContainer(){
    return Container(
      padding: EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
          color: ThemeColors.disabled,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Left side - Discount info
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '5% off',
                style: TextStyle(
                  fontSize: h1,
                  fontWeight: FontWeight.bold,
                  color: ThemeColors.primaryDark,
                  fontFamily: fontFamily2
                ),
              ),
              Text(
                'Advantage Tier',
                style: TextStyle(
                  fontSize: h2,
                  color: ThemeColors.gray,
                  fontFamily: fontFamily2
                ),
              ),
            ],
          ),
          
          // Right side - Book Table button
          Container(
            width: phoneWidth * 0.35, // Adjust width as needed
            child: DefaultButton(
              text: "Book Table",
              height: phoneHeight * 0.06,
              buttonColor: ThemeColors.primaryDark,               
              onPressed: () async {
                // Your booking logic here
              },
            ),
          ),
        ],
      ),
    );
  }
}