import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_search_bar.dart';
import 'package:hive/hive.dart';
import 'package:amverton/View/Booking/booking_outlet.dart';
import 'package:collection/collection.dart';

class OutletTemplate1 extends StatefulWidget {
  final String theme;

  const OutletTemplate1({Key? key, required this.theme}) : super(key: key);

  @override
  _OutletTemplate1State createState() => _OutletTemplate1State();
}

class _OutletTemplate1State extends State<OutletTemplate1> {
  late Future boxFuture;
  late Box box;
  late Box boxGlobal;

  bool onSearch = false;
  List<Outlet> searchList = [];
  TextEditingController searchController = TextEditingController();
  final StreamController streamController = StreamController.broadcast();

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();

    searchController.addListener(() {
      streamController.add(searchController.text);
    });
  }

  openBox() async {
    box = await Hive.openBox('box');
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>("BOOKINGS+MAIN+1");
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return Stack(
              children: [
                Container(
                  width: phoneWidth,
                  height: phoneHeight,
                  decoration: background == null
                      ? null
                      : BoxDecoration(
                          image: backgroundType == "IMAGE"
                              ? DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      background.data ?? ""),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: backgroundType == "COLOR"
                              ? Color(int.parse(
                                  "0xff${background.data ?? "FFFFFF"}"))
                              : null,
                        ),
                ),
                Container(
                  width: phoneWidth,
                  height: phoneHeight / 4.5,
                  decoration: BoxDecoration(
                    color: ThemeColors.primaryLight,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(15),
                      bottomRight: Radius.circular(15),
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.fromLTRB(defaultPadding,
                      topPaddingWithoutBar, defaultPadding, defaultPadding),
                  child: Column(
                    children: [
                      DefaultSearchBar(
                          controller: searchController,
                          hintText: "Search",
                          borderRadius: 15,
                          onChanged: (value) {
                            if (value.isEmpty && onSearch) {
                              setState(() {
                                onSearch = false;
                              });
                            } else if (value.isNotEmpty && !onSearch) {
                              setState(() {
                                onSearch = true;
                              });
                            }
                          }),
                      Visibility(
                        visible: !onSearch,
                        child: FutureBuilder(
                            future: future,
                            builder:
                                (BuildContext context, AsyncSnapshot snapshot) {
                              if (snapshot.connectionState ==
                                  ConnectionState.done) {
                                return SingleChildScrollView(
                                  padding: EdgeInsets.only(top: defaultPadding),
                                  child: Column(
                                    children: List.generate(outletList.length,
                                        (index) {
                                      return outletView(outletList[index]);
                                    }),
                                  ),
                                );
                              }
                              return Container();
                            }),
                      ),
                      Visibility(
                        visible: onSearch,
                        child: StreamBuilder(
                          stream: streamController.stream.asBroadcastStream(),
                          builder:
                              (BuildContext context, AsyncSnapshot snapshot) {
                            if (snapshot.hasData) {
                              return SingleChildScrollView(
                                padding: EdgeInsets.only(top: defaultPadding),
                                child: Column(
                                  children: [
                                    Text(
                                      "Search Result",
                                      style: TextStyle(
                                          color: ThemeColors.dark,
                                          fontSize: h2,
                                          fontWeight: FontWeight.bold),
                                    ),
                                    SizedBox(height: spacingHeightMedium),
                                    FutureBuilder(
                                      future: getOutletSearch(snapshot.data),
                                      builder: (BuildContext context,
                                          AsyncSnapshot snapshot) {
                                        if (snapshot.connectionState ==
                                            ConnectionState.done) {
                                          searchList = (snapshot.data['data']
                                                  ['outlets'] as List)
                                              .map((data) =>
                                                  Outlet.fromJson(data))
                                              .toList();

                                          return Column(
                                              children: List.generate(
                                                  searchList.length, (index) {
                                            return outletView(
                                                searchList[index]);
                                          }));
                                        }
                                        return SizedBox(
                                          height: phoneHeight / 3,
                                          child: Center(
                                            child: CircularProgressIndicator(
                                                color: ThemeColors.primaryDark),
                                          ),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              );
                            }
                            return Container();
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          }
          return Container();
        });
  }

  Widget outletView(Outlet outlet) {
    bool isLogin = box.get("is_login") ?? false;

    return GestureDetector(
      onTap: () {
        if (isLogin) {
          Navigator.pushNamed(context, "/make_booking/${bookingMainTemplate}",
              arguments: {
                "outletId": outlet.id,
              });
        } else {
          Navigator.of(context, rootNavigator: true)
              .pushNamed("/login/${loginAsset.template}");
        }
      },
      child: Container(
        margin: EdgeInsets.only(bottom: defaultInnerPadding),
        padding: EdgeInsets.all(defaultInnerPadding),
        width: phoneWidth,
        decoration: BoxDecoration(
          color: ThemeColors.light,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(20)),
              child: CachedNetworkImage(
                imageUrl: outlet.image ?? "",
                height: phoneHeight / 7.5,
                width: phoneWidth,
                fit: BoxFit.cover,
                placeholder: (context, url) {
                  return Center(
                    child: CircularProgressIndicator(
                      color: ThemeColors.primaryDark,
                    ),
                  );
                },
                errorWidget: (context, url, error) {
                  return Icon(
                    Icons.error_outline_outlined,
                    color: ThemeColors.primaryDark,
                  );
                },
              ),
            ),
            SizedBox(height: spacingHeightSmall),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        outlet.name,
                        style: TextStyle(
                          color: widget.theme == "L"
                              ? ThemeColors.dark
                              : ThemeColors.light,
                          fontSize: h3,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: spacingHeightSmall),
                      Text(
                        "08:00 - 18:00",
                        style: TextStyle(
                            color: widget.theme == "L"
                                ? ThemeColors.dark
                                : ThemeColors.light,
                            fontSize: h4),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () {},
                  child: ImageIcon(
                    AssetImage(
                        "assets/icons/${boxGlobal.get("iconSet")}/Store 01.png"),
                    color: ThemeColors.primaryDark,
                  ),
                ),
              ],
            ),
            SizedBox(height: spacingHeightSmall),
          ],
        ),
      ),
    );
  }
}
