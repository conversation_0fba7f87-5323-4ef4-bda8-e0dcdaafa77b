import 'dart:async';
import 'dart:io';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/location_service.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hive/hive.dart';

class BookingOutlet extends StatefulWidget {
  const BookingOutlet({Key? key}) : super(key: key);

  @override
  _BookingOutletState createState() => _BookingOutletState();
}

List<Outlet> outletList = [];
late Future future;

class _BookingOutletState extends State<BookingOutlet> {
  late Future boxFuture;
  late Box boxGlobal;

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();
    future = getOutlet();
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                body: RefreshIndicator(
                  color: ThemeColors.primaryDark,
                  onRefresh: () {
                    return Future.delayed(const Duration(seconds: 1), () {
                      setState(() {
                        future = getOutlet();
                      });
                    });
                  },
                  child: Stack(
                    children: [
                      bookingMainWidget ?? Container(),
                    ],
                  ),
                ),
              ),
            );
          }
          return Container();
        });
  }

  Future<Map?> getOutlet() async {
    ApiGet apiGet = ApiGet();

    // check location permission
    Position? currentPosition;
    if (Platform.isIOS) {
      currentPosition = await LocationService.getCurrentPosition();
    } else {
      Position? permissionCheck =
          await LocationService.checkAndroidLocationPermission();
      if (permissionCheck != null)
        currentPosition = await LocationService.getCurrentPosition();
    }

    Map? data = await apiGet.outletList(
        currentPosition?.latitude, currentPosition?.longitude);

    if (data != null && data['data'] != null) {
    outletList = (data['data']['outlets'] as List)
          .map((data) => Outlet.fromJson(data))
          .toList();
    }
    return data;
  }
}

Future<Map?> getOutletSearch(String? searchValue) async {
  ApiGet apiGet = ApiGet();
  Map? data = await apiGet.outletListSearch(searchValue);
  return data;
}
