import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:hive/hive.dart';

class DefaultDateTimeField2 extends StatelessWidget {
  final TextEditingController controller;
  final String? labelText;
  final Color? fieldColor;
  final Function()? onTap;

  DefaultDateTimeField2({
    Key? key,
    required this.controller,
    this.labelText,
    this.fieldColor,
    required this.onTap,
  }) : super(key: key);

  final Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(vertical: defaultInnerPadding),
        decoration: BoxDecoration(
          color: fieldColor ?? ThemeColors.light,
          border: Border(bottom: BorderSide(color: ThemeColors.disabled)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (labelText != null)
              Text(
                labelText!,
                style: TextStyle(
                  color: ThemeColors.primaryDark,
                  fontSize: h3,
                  fontWeight: FontWeight.w600,
                ),
              ),
            TextField(
              controller: controller,
              style: TextStyle(color: ThemeColors.dark, fontSize: h3),
              readOnly: true,
              cursorColor: ThemeColors.primaryDark,
              decoration: InputDecoration(
                contentPadding: EdgeInsets.all(defaultPadding),
                border: InputBorder.none,
              ),
              onTap: onTap,
            ),
          ],
        ));
  }
}
