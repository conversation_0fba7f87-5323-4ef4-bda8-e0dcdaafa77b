import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:hive_flutter/hive_flutter.dart';

class DefaultTextField3 extends StatefulWidget {
  final String label;
  final String hintText;
  final TextEditingController controller;

  const DefaultTextField3(
      {super.key,
      required this.label,
      required this.controller,
      required this.hintText});

  @override
  _DefaultTextField3State createState() => _DefaultTextField3State();
}

class _DefaultTextField3State extends State<DefaultTextField3> {
  int _charCount = 0;
  final Box boxGlobal = Hive.box("boxGlobal");

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(() {
      setState(() {
        _charCount = widget.controller.text.length;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: TextStyle(
            fontSize: h4,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.0),
        Stack(
          children: [
            Container(
              height: buttonHeight * 2,
              decoration: BoxDecoration(
                color: ThemeColors.light,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: ThemeColors.disabled),
              ),
              child: TextField(
                controller: widget.controller,
                maxLength: 100,
                maxLines: null,
                style: TextStyle(fontSize: h3),
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  contentPadding: EdgeInsets.fromLTRB(16, 10, 16, 28),
                  border: InputBorder.none,
                  counterText: '',
                ),
              ),
            ),
            Positioned(
              right: defaultPadding,
              bottom: 8.0,
              child: Text(
                '${_charCount} / 100',
                style: TextStyle(
                  fontSize: h5,
                  color: Colors.grey,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
