import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:hive/hive.dart';

class DefaultSearchBar extends StatelessWidget {
  final TextEditingController controller;
  final String? hintText;
  final TextInputType keyBoardType;
  final bool readOnly;
  final bool obscureText;
  final Function(String)? onChanged;
  final double? borderRadius; // default 30

  DefaultSearchBar({
    Key? key,
    required this.controller,
    this.hintText,
    this.keyBoardType = TextInputType.text,
    this.readOnly = false,
    this.obscureText = false,
    required this.onChanged,
    this.borderRadius = 30,
  }) : super(key: key);

  final Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: defaultInnerPadding),
      height: buttonHeight,
      decoration: BoxDecoration(
        color: ThemeColors.light,
        borderRadius: BorderRadius.circular(borderRadius!),
        border: Border.all(color: ThemeColors.disabled),
      ),
      child: TextField(
        controller: controller,
        keyboardType: keyBoardType,
        readOnly: readOnly,
        obscureText: obscureText,
        cursorColor: ThemeColors.primaryDark,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(color: ThemeColors.gray, fontSize: h2),
          contentPadding: EdgeInsets.all(defaultPadding),
          border: InputBorder.none,
          suffixIcon: Icon(
            Icons.search,
            color: ThemeColors.primaryDark,
          ),
        ),
        style: TextStyle(color: ThemeColors.dark, fontSize: h2),
        onChanged: onChanged,
      ),
    );
  }
}
