import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:intl/intl.dart';

class VoucherCard extends StatelessWidget {
  final String imageUrl;
  final String title;
  final String endon;
  String? tag;
  bool used;
  bool active;

  VoucherCard({
    required this.imageUrl,
    required this.title,
    required this.endon,
    this.used = false,
    this.tag,
    this.active = true,
  });

  Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: spacingHeightLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 6.0,
            spreadRadius: 2.0,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            child: Stack(
              children: [
                ColorFiltered(
                  colorFilter: !active
                      ? ColorFilter.mode(
                          Colors.grey,
                          BlendMode.saturation,
                        )
                      : ColorFilter.mode(
                          Colors.transparent,
                          BlendMode.multiply,
                        ),
                  child: Hero(
                    tag: tag == null ? UniqueKey() : tag!,
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: double.infinity,
                      height: phoneHeight / 5,
                      fit: BoxFit.cover,
                      placeholder: (context, url) {
                        return Center(
                          child: CircularProgressIndicator(
                            color: ThemeColors.primaryDark,
                          ),
                        );
                      },
                      errorWidget: (context, url, error) {
                        return Icon(
                          Icons.error_outline_outlined,
                          color: ThemeColors.primaryDark,
                        );
                      },
                    ),
                  ),
                ),
                if (imageUrl == "")
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20),
                        ),
                        color: ThemeColors.disabled.withOpacity(0.5),
                      ),
                    ),
                  )
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(defaultPadding),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: h4,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (active) ...[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Expired At',
                        style: TextStyle(
                          fontSize: h4,
                          color: ThemeColors.gray,
                        ),
                      ),
                      Text(
                        DateFormat('yyyy-MM-dd').format(
                          DateTime.parse(endon),
                        ),
                        style: TextStyle(
                          fontSize: h4,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
                if (!active) ...[
                  Text(
                    used ? "Used" : 'Expired',
                    style: TextStyle(
                      fontSize: h4,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ]
              ],
            ),
          ),
        ],
      ),
    );
  }

  bool dateExpired(date) {
    final now = DateTime.now();
    final expirationDate = DateTime.parse(date);
    final bool isExpired = expirationDate.isBefore(now);
    return isExpired;
  }
}
