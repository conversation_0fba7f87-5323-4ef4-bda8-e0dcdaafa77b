import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:hive/hive.dart';

class DefaultPhoneField1 extends StatefulWidget {
  final TextEditingController controller;
  final String? hintText;
  final bool readOnly;
  final List<String> countryCodeList;
  final String selectedCountryCode;
  final Function(String?) onDropdownChanged;
  final bool contactFunction;
  final Function()? onContacts;

  const DefaultPhoneField1({
    Key? key,
    required this.controller,
    this.hintText,
    this.readOnly = false,
    required this.countryCodeList,
    required this.selectedCountryCode,
    required this.onDropdownChanged,
    this.contactFunction = false,
    this.onContacts,
  }) : super(key: key);

  @override
  State<DefaultPhoneField1> createState() => _DefaultPhoneField1State();
}

class _DefaultPhoneField1State extends State<DefaultPhoneField1> {
  Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: defaultInnerPadding),
      decoration: BoxDecoration(
        color: ThemeColors.light,
        borderRadius: BorderRadius.circular(30),
        border: Border.all(color: ThemeColors.disabled),
      ),
      child: Row(
        children: [
          Container(
            width: phoneWidth / 5,
            decoration: BoxDecoration(
              border: Border(
                  right: BorderSide(
                color: ThemeColors.disabled,
              )),
            ),
            child: DropdownButton2(
              value: widget.selectedCountryCode,
              buttonStyleData: ButtonStyleData(
                padding: EdgeInsets.only(left: defaultPadding, right: 5),
                height: buttonHeight,
                decoration: BoxDecoration(
                  color: ThemeColors.light,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(30),
                    bottomLeft: Radius.circular(30),
                  ),
                ),
              ),
              underline: Container(),
              style: TextStyle(color: ThemeColors.dark, fontSize: h3),
              iconStyleData: IconStyleData(
                iconEnabledColor: ThemeColors.dark,
              ),
              dropdownStyleData: DropdownStyleData(
                width: phoneWidth / 5,
                decoration: BoxDecoration(
                  color: ThemeColors.light,
                  borderRadius: BorderRadius.circular(5),
                ),
              ),
              items: widget.countryCodeList.map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(
                    value,
                    style: TextStyle(color: ThemeColors.dark),
                  ),
                );
              }).toList(),
              onChanged: widget.onDropdownChanged,
              isExpanded: true,
            ),
          ),
          Expanded(
            child: TextField(
              controller: widget.controller,
              keyboardType: TextInputType.phone,
              readOnly: widget.readOnly,
              cursorColor: ThemeColors.primaryDark,
              decoration: InputDecoration(
                hintText: "1********",
                hintStyle: TextStyle(color: ThemeColors.gray, fontSize: h3),
                contentPadding: EdgeInsets.all(defaultInnerPadding),
                border: InputBorder.none,
                suffixIcon: widget.contactFunction
                    ? IconButton(
                        icon: Icon(
                          Icons.contacts,
                          color: ThemeColors.primaryDark,
                        ),
                        onPressed: widget.onContacts,
                      )
                    : null,
              ),
              style: TextStyle(color: ThemeColors.dark, fontSize: h3),
            ),
          ),
        ],
      ),
    );
  }
}
