import 'package:amverton/Constant/theme.dart';
import 'package:amverton/View/Restaurant/restaurant_outlet.dart';
import 'package:flutter/material.dart';
import 'package:amverton/main.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
import 'package:hive/hive.dart';

class EmptyList extends StatelessWidget {
  final String theme;

  EmptyList({
    Key? key,
    required this.theme,
  }) : super(key: key);

  final Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: phoneWidth,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.folder_open_outlined,
            size: 80,
            color: ThemeColors.primaryDark,
          ),
          SizedBox(height: spacingHeightSmall),
          Text(
            "You have no upcoming booking",
            style: TextStyle(
              color: ThemeColors.primaryDark,
              fontSize: h1,
              fontFamily: fontFamily2,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: spacingHeightMedium),
          Text(
            "Wanna make a booking? book now!",
            style: TextStyle(
              color: ThemeColors.gray,
              fontSize: h3,
              fontFamily: fontFamily2,
            ),
          ),
          SizedBox(height: spacingHeightLarge),
          Container(
            width: double.infinity,
            margin: EdgeInsets.symmetric(horizontal: 70),
            child: DefaultButton(
              text: "Book Now",
              buttonColor: Colors.white,
              onPressed: () {
                 MyHomePage.navigateToTab(context, "RESTAURANT");
              },
            ),
          ),
        ],
      ),
    );
  }
}
