import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:hive/hive.dart';
import 'package:shimmer/shimmer.dart';

class Loading1 extends StatelessWidget {
  Loading1({Key? key}) : super(key: key);

  final Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.fromLTRB(
        defaultPadding,
        topPaddingWithoutBar,
        defaultPadding,
        defaultPadding,
      ),
      child: Shimmer.fromColors(
        baseColor: ThemeColors.disabled,
        highlightColor: ThemeColors.light,
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.symmetric(vertical: defaultInnerPadding),
              height: buttonHeight,
              decoration: BoxDecoration(
                color: ThemeColors.light,
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            Column(
              children: List.generate(3, (index) {
                return Container(
                  margin: EdgeInsets.symmetric(vertical: defaultInnerPadding),
                  height: phoneHeight / 3.5,
                  decoration: BoxDecoration(
                    color: ThemeColors.light,
                    borderRadius: BorderRadius.circular(20),
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}
