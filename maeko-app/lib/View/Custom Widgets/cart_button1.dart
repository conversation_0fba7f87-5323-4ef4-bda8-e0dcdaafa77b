import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:hive/hive.dart';

class CartButton1 extends StatefulWidget {
  final VoidCallback onPressed;

  const CartButton1({
    Key? key,
    required this.onPressed,
  }) : super(key: key);

  @override
  _CartButton1State createState() => _CartButton1State();
}

class _CartButton1State extends State<CartButton1> {
  Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: buttonHeight,
      child: SizedBox.expand(
        child: GestureDetector(
          onTap: widget.onPressed,
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: defaultPadding),
            padding: EdgeInsets.symmetric(horizontal: defaultPadding),
            decoration: BoxDecoration(
              color: ThemeColors.secondaryDark,
              borderRadius: BorderRadius.circular(15),
            ),
            alignment: Alignment.centerLeft,
            child: Text(
              "View Cart",
              style: TextStyle(
                fontSize: h2,
                color: ThemeColors.light,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
