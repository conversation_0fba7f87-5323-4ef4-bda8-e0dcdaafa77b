import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:hive/hive.dart';

class DefaultTextField1 extends StatelessWidget {
  final TextEditingController controller;
  final String? hintText;
  final TextInputType keyBoardType;
  final int maxLines;
  final bool readOnly;
  final bool obscureText;

  DefaultTextField1({
    Key? key,
    required this.controller,
    this.hintText,
    this.keyBoardType = TextInputType.text,
    this.maxLines = 1,
    this.readOnly = false,
    this.obscureText = false,
  }) : super(key: key);

  final Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Container(
     // margin: EdgeInsets.symmetric(vertical: defaultInnerPadding),
      height: buttonHeight * maxLines,
      decoration: BoxDecoration(
        color: ThemeColors.light,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: ThemeColors.disabled),
      ),
      child: TextField(
        controller: controller,
        keyboardType: keyBoardType,
        style: TextStyle(color: ThemeColors.dark, fontSize: h3),
        maxLines: maxLines,
        readOnly: readOnly,
        obscureText: obscureText,
        cursorColor: ThemeColors.primaryDark,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(color: ThemeColors.gray, fontSize: h3),
          contentPadding: EdgeInsets.all(defaultPadding),
          border: InputBorder.none,
        ),
      ),
    );
  }
}
