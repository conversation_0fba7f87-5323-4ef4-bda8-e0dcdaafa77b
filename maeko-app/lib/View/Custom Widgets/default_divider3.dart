import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/View/Custom%20Widgets/default_divider.dart';
import 'package:hive_flutter/hive_flutter.dart';

class DefaultDivider3 extends StatelessWidget {
  DefaultDivider3({super.key});

  final Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return DefaultDivider(color: ThemeColors.disabled);
  }
}
