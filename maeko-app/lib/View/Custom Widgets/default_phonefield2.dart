import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:hive/hive.dart';

class DefaultPhoneField2 extends StatefulWidget {
  final TextEditingController controller;
  final String labelText;
  final bool readOnly;
  final List<String> countryCodeList;
  final String selectedCountryCode;
  final Function(String?) onDropdownChanged;
  final bool contactFunction;
  final Function()? onContacts;

  const DefaultPhoneField2({
    Key? key,
    required this.controller,
    this.labelText = "Phone Number",
    this.readOnly = false,
    required this.countryCodeList,
    required this.selectedCountryCode,
    required this.onDropdownChanged,
    this.contactFunction = false,
    this.onContacts,
  }) : super(key: key);

  @override
  State<DefaultPhoneField2> createState() => _DefaultPhoneField2State();
}

class _DefaultPhoneField2State extends State<DefaultPhoneField2> {
  Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: defaultInnerPadding),
      decoration: BoxDecoration(
        color: ThemeColors.light,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.labelText,
            style: TextStyle(
              color: ThemeColors.primaryDark,
              fontSize: h3,
              fontWeight: FontWeight.w600,
            ),
          ),
          Row(
            children: [
              Container(
                width: phoneWidth / 5,
                child: DropdownButton2(
                  value: widget.selectedCountryCode,
                  buttonStyleData: ButtonStyleData(
                    padding: EdgeInsets.only(left: defaultPadding, right: 5),
                    height: buttonHeight,
                    decoration: BoxDecoration(
                      color: ThemeColors.light,
                    ),
                  ),
                  underline: Container(),
                  style: TextStyle(color: ThemeColors.dark, fontSize: h3),
                  iconStyleData: IconStyleData(
                    iconEnabledColor: ThemeColors.dark,
                  ),
                  dropdownStyleData: DropdownStyleData(
                    width: phoneWidth / 5,
                    decoration: BoxDecoration(
                      color: ThemeColors.light,
                    ),
                  ),
                  items: widget.countryCodeList.map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(
                        value,
                        style: TextStyle(color: ThemeColors.dark),
                      ),
                    );
                  }).toList(),
                  onChanged: widget.onDropdownChanged,
                  isExpanded: true,
                ),
              ),
              Expanded(
                child: Theme(
                  data: Theme.of(context).copyWith(
                    colorScheme: ThemeData()
                        .colorScheme
                        .copyWith(primary: ThemeColors.primaryDark),
                  ),
                  child: TextField(
                    controller: widget.controller,
                    keyboardType: TextInputType.phone,
                    readOnly: widget.readOnly,
                    cursorColor: ThemeColors.primaryDark,
                    decoration: InputDecoration(
                      hintText: "1********",
                      hintStyle:
                          TextStyle(color: ThemeColors.gray, fontSize: h3),
                      contentPadding: EdgeInsets.all(defaultInnerPadding),
                      enabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: ThemeColors.disabled),
                      ),
                      suffixIcon: widget.contactFunction
                          ? IconButton(
                              icon: Icon(
                                Icons.contacts,
                                color: ThemeColors.primaryDark,
                              ),
                              onPressed: widget.onContacts,
                            )
                          : null,
                    ),
                    style: TextStyle(color: ThemeColors.dark, fontSize: h3),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
