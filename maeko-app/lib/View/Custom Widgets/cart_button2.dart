import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/View/OrderMenu/order_menu_page.dart';
import 'package:hive/hive.dart';

class CartButton2 extends StatefulWidget {
  final VoidCallback onPressed;

  const CartButton2({
    Key? key,
    required this.onPressed,
  }) : super(key: key);

  @override
  _CartButton2State createState() => _CartButton2State();
}

class _CartButton2State extends State<CartButton2> {
  Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        FloatingActionButton(
          heroTag: "cart_button2",
          backgroundColor: ThemeColors.primaryDark,
          onPressed: widget.onPressed,
          child: ImageIcon(
            AssetImage("assets/icons/${boxGlobal.get("iconSet")}/Cart 02.png"),
            size: 50,
            color: ThemeColors.light,
          ),
        ),
        Positioned(
          right: 1,
          child: ValueListenableBuilder<int>(
            builder: (BuildContext context, int value, Widget? child) {
              return value != 0
                  ? Container(
                      width: phoneWidth / 18,
                      height: phoneWidth / 18,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: ThemeColors.secondaryDark,
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        "$value",
                        style: TextStyle(
                          fontSize: h4,
                          color: ThemeColors.light,
                        ),
                      ),
                    )
                  : SizedBox();
            },
            valueListenable: cartQuantity,
          ),
        ),
      ],
    );
  }
}
