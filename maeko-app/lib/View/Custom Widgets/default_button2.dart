import 'package:amverton/Constant/theme.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_size.dart';

class DefaultButton2 extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed; // default pop()
  final Color? buttonColor; // default white
  final Color? textColor; // default white
  final double? borderRadius; // default 30
  final bool clickable;
  final bool textBold;
  final double? height;
  final double? fontSize;

  DefaultButton2({
    Key? key,
    required this.text,
    this.onPressed,
    this.buttonColor,
    this.textColor,
    this.borderRadius = 30,
    this.clickable = true,
    this.textBold = false,
    this.height,
    this.fontSize,
  }) : super(key: key);

  @override
  _DefaultButton2State createState() => _DefaultButton2State();
}

class _DefaultButton2State extends State<DefaultButton2> {
  @override
  Widget build(BuildContext context) {
    return AbsorbPointer(
      absorbing: !widget.clickable,
      child: SizedBox(
        width: double.infinity,
        height: widget.height ?? buttonHeight,
        child: SizedBox.expand(
          child: GestureDetector(
            onTap: widget.onPressed ?? () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: widget.buttonColor ?? Colors.white,
                borderRadius: BorderRadius.circular(widget.borderRadius!),
              ),
              child: Center(
                child: Text(
                  widget.text,
                  style: TextStyle(
                    fontSize: widget.fontSize ?? h2,
                    color: widget.textColor ?? Colors.white,
                    fontWeight: widget.textBold ? FontWeight.bold : null,
                    fontFamily: fontFamily2
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
