import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_size.dart';

class FirstTimeOpenError extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
        color: Colors.white,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.warning_amber_outlined,
              color: Colors.red,
              size: 60,
            ),
            SizedBox(height: spacingHeightMedium),
            Text(
              "Something went error.\nPlease try to reopen later.",
              textAlign: TextAlign.center,
            ),
          ],
        ));
  }
}
