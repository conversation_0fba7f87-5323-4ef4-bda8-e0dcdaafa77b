import 'dart:convert';
import 'dart:developer';

import 'package:amverton/Constant/theme.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Restaurant/Section/slideshow_template1.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/Model/restaurantProduct.dart';
import 'package:flutter/material.dart';

class RestaurantDetailTemplate1 extends StatefulWidget {
  final String restaurantId;

  const RestaurantDetailTemplate1({super.key, required this.restaurantId});

  @override
  State<RestaurantDetailTemplate1> createState() => _RestaurantDetailTemplate1State();
}

class _RestaurantDetailTemplate1State extends State<RestaurantDetailTemplate1> {
  late Future future;
  Map<String, dynamic>? restaurant;
  List<Map<String, dynamic>> products = [];
  @override
  void initState() {
    super.initState();
    future = getDining();
  }

  Future<Map?> getDining() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.restaurantDetail(int.parse(widget.restaurantId));
 print("Testsets1");
    if (data != null && data['data'] != null && data['data']['restaurant'] != null) {
       print("Testsets2");
      restaurant = data['data']['restaurant'];

        // Check what products contains before assignment
      final rawProducts = restaurant!['products'];
      print("Raw products: $rawProducts");

      if (rawProducts != null && rawProducts is List) {
        products = List<Map<String, dynamic>>.from(rawProducts);
      }
    }
    return data;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: future,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          return GestureDetector(
            onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
            behavior: HitTestBehavior.translucent,
            child: Scaffold(
              extendBodyBehindAppBar: true,
              appBar: AppBar(
                backgroundColor: Colors.transparent,
                leading: GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    margin: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                        shape: BoxShape.circle, color: ThemeColors.gray),
                    child: Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              body: Column(
                children: [
                  Expanded(
                    flex: 7,
                    child: Container(
                      height: phoneHeight,
                      child: Stack(
                        children: [
                          // Banner
                          Container(
                            height: 250,
                            padding: EdgeInsets.only(top: 50), // Adjust for AppBar height
                            width: double.infinity,
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: NetworkImage(restaurant?['image_1'] ?? ''),
                                fit: BoxFit.cover,
                              ),
                            ),
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.black.withOpacity(0.4),
                                    Colors.black.withOpacity(0.2),
                                  ],
                                  begin: Alignment.bottomCenter,
                                  end: Alignment.topCenter,
                                ),
                              ),
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      restaurant!['tagline'],
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: h2,
                                        fontFamily: fontFamily2,
                                        letterSpacing: 1.5,
                                      ),
                                    ),
                                    SizedBox(height: 8),
                                    Text(
                                      restaurant!['name'],
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold,
                                        fontFamily: fontFamily2,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),

                          // Content below banner
                          Positioned(
                            top: 250,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            child: SingleChildScrollView(
                              child: Column(
                                children: [
                                  Padding(
                                    padding: EdgeInsets.all(20),
                                    child: Text(
                                      restaurant?['description'] ?? '',
                                      style: TextStyle(
                                        fontSize: h2,
                                        height: 1.5,
                                        color: ThemeColors.primaryDark,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),

                                  // // Dress Code
                                  // if (restaurant?['dress_code'] != null)
                                  //   Padding(
                                  //     padding: const EdgeInsets.only(bottom: 10),
                                  //     child: Text(
                                  //       'Dress Code: ${restaurant!['dress_code']}',
                                  //       style: TextStyle(
                                  //         fontSize: h2,
                                  //         fontWeight: FontWeight.w800,
                                  //         color: ThemeColors.primaryDark,
                                  //         fontFamily: fontFamily2,
                                  //       ),
                                  //     ),
                                  //   ),

                                  // Menu Section
                                  Padding(
                                    padding: EdgeInsets.all(10),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Access the Menu',
                                          style: TextStyle(
                                            fontSize: h1,
                                            fontWeight: FontWeight.bold,
                                            color: ThemeColors.primaryDark,
                                            fontFamily: fontFamily2,
                                          ),
                                        ),
                                        SizedBox(height: 5),
                                        Padding(
                                          padding: const EdgeInsets.symmetric(vertical: 16.0),
                                          child: SlideshowTemplate1(
                                            theme: "L",
                                            products: products,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: BookTableContainer(),
                  ),
                ],
              ),
            ),
          );
        }
        return Center(child: CircularProgressIndicator());
      },
    );
  }

  Widget BookTableContainer() {
    return Container(
      padding: EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: ThemeColors.disabled),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Discount Info
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '5% off',
                style: TextStyle(
                  fontSize: h1,
                  fontWeight: FontWeight.bold,
                  color: ThemeColors.primaryDark,
                  fontFamily: fontFamily2,
                ),
              ),
              Text(
                'Advantage Tier',
                style: TextStyle(
                  fontSize: h2,
                  color: ThemeColors.gray,
                  fontFamily: fontFamily2,
                ),
              ),
            ],
          ),

          // Book Table Button
          Container(
            width: phoneWidth * 0.35,
            child: DefaultButton(
              text: "Book Table",
              height: phoneHeight * 0.06,
              buttonColor: ThemeColors.primaryDark,
              onPressed: () {
                // Navigate to booking page or open booking dialog
              },
            ),
          ),
        ],
      ),
    );
  }
}
