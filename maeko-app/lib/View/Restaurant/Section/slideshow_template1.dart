import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:carousel_indicator/carousel_indicator.dart';
import '../../../Model/restaurantProduct.dart';

class SlideshowTemplate1 extends StatefulWidget {
  final String theme;
  final List<Map<String, dynamic>> products;

  const SlideshowTemplate1({
    Key? key,
    required this.theme,
    required this.products,
  }) : super(key: key);

  @override
  State<SlideshowTemplate1> createState() => _SlideshowTemplate1State();
}

class _SlideshowTemplate1State extends State<SlideshowTemplate1> {
  int currentIndex = 0;
  late PageController pageController;
  late List<Product> productList;

  @override
  void initState() {
    super.initState();
    productList = widget.products.map((p) => Product.fromJson(p)).toList();
    pageController = PageController(initialPage: 0);
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (productList.isEmpty) {
      return SizedBox(
        height: phoneHeight / 3.6,
        child: Center(
          child: CircularProgressIndicator(
            color: ThemeColors.primaryDark,
          ),
        ),
      );
    }

    final int pageCount = (productList.length / 2).ceil();

    return Column(
      children: [
        SizedBox(
          height: phoneHeight / 3.6,
          child: PageView.builder(
            controller: pageController,
            onPageChanged: (index) {
              setState(() {
                currentIndex = index;
              });
            },
            itemCount: pageCount,
            itemBuilder: (context, index) {
              int firstIndex = index * 2;
              int secondIndex = firstIndex + 1;

              Product product1 = productList[firstIndex];
              Product? product2 = secondIndex < productList.length
                  ? productList[secondIndex]
                  : null;

              return Row(
                children: [
                  Expanded(child: _buildSlideItem(product1)),
                  SizedBox(width: 8),
                  Expanded(child: product2 != null ? _buildSlideItem(product2) : SizedBox()),
                ],
              );
            },
          ),
        ),
        // SizedBox(height: spacingHeightSmall),
        // CarouselIndicator(
        //   count: pageCount,
        //   index: currentIndex,
        //   activeColor: ThemeColors.primaryDark,
        //   color: widget.theme == "L"
        //       ? ThemeColors.disabled
        //       : ThemeColors.gray,
        // ),
      ],
    );
  }

  Widget _buildSlideItem(Product product) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Stack(
        fit: StackFit.expand,
        children: [
          Image.network(
            product.image,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => Container(
              color: Colors.grey[300],
              alignment: Alignment.center,
              child: Icon(Icons.broken_image, size: 60),
            ),
          ),
          Container(
            alignment: Alignment.bottomLeft,
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.black54, Colors.transparent],
                begin: Alignment.bottomCenter,
                end: Alignment.topCenter,
              ),
            ),
            child: Text(
              product.name,
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: Colors.black,
                    blurRadius: 4,
                    offset: Offset(1, 1),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
