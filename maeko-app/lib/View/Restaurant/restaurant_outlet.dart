import 'dart:async';
import 'dart:io';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Model/restaurant.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/location_service.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hive/hive.dart';

class RestaurantOutlet extends StatefulWidget {
  const RestaurantOutlet({Key? key}) : super(key: key);

  @override
  _RestaurantOutletState createState() => _RestaurantOutletState();
}

List<Restaurant> restaurantList = [];
late Future future;

class _RestaurantOutletState extends State<RestaurantOutlet> {
  late Future boxFuture;
  late Box boxGlobal;

  @override
  void initState() {
    super.initState();
    boxFuture = openBox();
    future = getRestaurant();
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                body: RefreshIndicator(
                  color: ThemeColors.primaryDark,
                  onRefresh: () {
                    return Future.delayed(const Duration(seconds: 1), () {
                      setState(() {
                        future = getRestaurant();
                      });
                    });
                  },
                  child: Stack(
                    children: [
                      restaurantMainWidget ?? Container(),
                    ],
                  ),
                ),
              ),
            );
          }
          return Container();
        });
  }

  Future<Map?> getRestaurant() async {
    ApiGet apiGet = ApiGet();

    Map? data = await apiGet.restaurantList();

    if (data != null && data['data'] != null) {
    restaurantList = (data['data']['restaurants'] as List)
          .map((data) => Restaurant.fromJson(data))
          .toList();
    }
    return data;
  }
}
