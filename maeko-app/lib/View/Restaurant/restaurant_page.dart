import 'dart:convert';
import 'dart:developer';

import 'package:amverton/Constant/theme.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Restaurant/Section/restaurant_detail_template1.dart';
import 'package:flutter/material.dart';

class RestaurantPage extends StatefulWidget {
  @override
  State<RestaurantPage> createState() => _RestaurantPageState();
}

class _RestaurantPageState extends State<RestaurantPage> {
  late Future future;
  List<Map<String, String>> diningOptions = [];

  @override
  void initState() {
    super.initState();
    future = getDining();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FutureBuilder(
        future: future,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            if (snapshot.hasError) {
              return Center(
                child: Text('Something went wrong: ${snapshot.error}'),
              );
            }

            return Column(
              children: [
                // Header Image
                Container(
                  height: 200,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage('assets/images/restaurant_header.png'),
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.black.withOpacity(0.4),
                          Colors.black.withOpacity(0.2),
                        ],
                        begin: Alignment.bottomCenter,
                        end: Alignment.topCenter,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        'Dine with Us',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          fontFamily: fontFamily2,
                        ),
                      ),
                    ),
                  ),
                ),

                // Main List
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(top: defaultPadding, left: 6, right: 6, bottom: defaultPadding),
                    child: ListView.builder(
                      itemCount: diningOptions.length,
                      itemBuilder: (context, index) {
                        final option = diningOptions[index];
                        return DiningOption(
                          context,
                          option['id'] ?? '',
                          option['imagePath'] ?? '',
                          option['title'] ?? '',
                          option['subtitle'] ?? '',
                          option['discount'] ?? '',
                          option['url'] ?? '',
                        );
                      },
                    ),
                  ),
                ),
              ],
            );
          }

          return Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Future<Map?> getDining() async {
    ApiGet apiGet = ApiGet();

    Map? data = await apiGet.restaurantList();

    if (data != null && data['data'] != null && data['data']['restaurants'] != null) {
      List restaurants = data['data']['restaurants'];

      diningOptions = restaurants.map<Map<String, String>>((restaurant) {
        return {
          'id': restaurant['id'].toString(),
          'imagePath': restaurant['image_1'] ?? '',
          'title': restaurant['name'] ?? '',
          'subtitle': restaurant['tagline'] ?? '',
          'discount': '-5%', // default or dynamic
          'url': restaurant['slug'] ?? '', // use slug or full URL
        };
      }).toList();
    }

    return data;
  }
}

Widget DiningOption(BuildContext context, String id, String imagePath, String title,
    String subtitle, String? discount, String? url) {
  return GestureDetector(
    onTap: () {
      // You can replace this with WebViewPage or your own detail page
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => RestaurantDetailTemplate1(restaurantId: id), // Replace if needed
        ),
      );
    },
    child: Container(
      margin: EdgeInsets.only(bottom: defaultInnerPadding),
      padding: EdgeInsets.all(defaultInnerPadding),
      width: phoneWidth,
      height: 300,
      child: Stack(
        children: [
          // Background Image from network
          ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Image.network(
              imagePath,
              fit: BoxFit.cover,
              width: double.infinity,
              height:240,
              errorBuilder: (context, error, stackTrace) =>
                  Icon(Icons.broken_image, size: 100, color: Colors.grey),
            ),
          ),
          // White overlay positioned at bottom
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 85,
              margin: EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 5,
                    offset: Offset(0, -3),
                  ),
                ],
              ),
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            color: ThemeColors.primaryDark,
                            fontSize: h2,
                            fontWeight: FontWeight.bold,
                            fontFamily: fontFamily2,
                          ),
                        ),
                        SizedBox(height: 2),
                        Text(
                          subtitle,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: h3,
                            fontWeight: FontWeight.w400,
                            fontFamily: fontFamily2,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  if (discount != null && discount.isNotEmpty)
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color(0xFF818592),
                            Color(0xFF595C68),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        discount,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: h3,
                          fontWeight: FontWeight.bold,
                          fontFamily: fontFamily2,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    ),
  );
}
