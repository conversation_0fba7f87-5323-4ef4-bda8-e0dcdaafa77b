import 'dart:io';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/support.dart';
import 'package:hive/hive.dart';
import 'package:url_launcher/url_launcher.dart';

class SupportPlatform extends StatefulWidget {
  final String platform;
  final String platformImage;
  final List<SocialMedia> supportList;

  const SupportPlatform({
    Key? key,
    required this.platform,
    required this.platformImage,
    required this.supportList,
  }) : super(key: key);

  @override
  _SupportPlatformState createState() => _SupportPlatformState();
}

class _SupportPlatformState extends State<SupportPlatform> {
  Box boxGlobal = Hive.box('boxGlobal');

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeColors.light,
      appBar: AppBar(title: Text(widget.platform)),
      body: ListView.builder(
        padding: EdgeInsets.all(defaultPadding),
        itemCount: widget.supportList.length,
        itemBuilder: (BuildContext context, int index) {
          return Container(
            margin: EdgeInsets.only(bottom: defaultInnerPadding),
            padding: EdgeInsets.symmetric(vertical: spacingHeightSmall),
            width: phoneWidth,
            decoration: BoxDecoration(
              color: ThemeColors.light,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: ThemeColors.disabled),
            ),
            child: ListTile(
              onTap: () {
                openUrl(widget.supportList[index].username);
              },
              title: Text(
                widget.supportList[index].username,
                style: TextStyle(fontSize: h3),
              ),
              subtitle: Text(
                "Operating hours: ${widget.supportList[index].startAt}-${widget.supportList[index].endAt}",
                style: TextStyle(fontSize: h4),
              ),
              trailing: Container(
                width: phoneWidth / 10,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  image:
                      DecorationImage(image: AssetImage(widget.platformImage)),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void openUrl(String username) async {
    String check = username.replaceAll(RegExp('@'), '');
    String url = "";

    if (widget.platform == "Messenger") {
      url = "https://m.me/$check";
    } else if (widget.platform == "WhatsApp") {
      url = "https://wa.me/$check";
    } else if (widget.platform == "Telegram") {
      url = "https://t.me/$check";
    } else if (widget.platform == "Email") {
      url = "mailto:$username";
    }

    Uri value = Uri.parse(url);
    if (await canLaunchUrl(value)) {
      if (Platform.isIOS)
        await launchUrl(value);
      else
        await launchUrl(value, mode: LaunchMode.externalApplication);
    } else {
      print("failed to open");
    }
  }
}
