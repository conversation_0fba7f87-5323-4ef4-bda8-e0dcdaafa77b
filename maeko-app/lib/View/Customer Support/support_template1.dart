import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/support.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/View/Custom%20Widgets/empty_list.dart';
import 'package:amverton/View/Customer Support/support_platform.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class SupportTemplate1 extends StatefulWidget {
  static String routeName = "/support/1";
  final dynamic args;

  const SupportTemplate1({Key? key, this.args}) : super(key: key);

  @override
  _SupportTemplate1State createState() => _SupportTemplate1State();
}

class _SupportTemplate1State extends State<SupportTemplate1> {
  late Future boxFuture;
  late Future future;
  late Box boxGlobal;
  String theme = "L";
  Support? support;

  @override
  void initState() {
    super.initState();
    theme = widget.args["theme"];
    boxFuture = openBox();
    future = getCustomerSupport();
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    return await Hive.openBox<Asset>('ACCOUNT+CUSTOMER_SUPPORT+1');
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: boxFuture,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            String? backgroundType = list.values
                    .firstWhereOrNull(
                        (element) => element.name == "BACKGROUND_TYPE")
                    ?.data ??
                "";
            Asset? background = list.values.firstWhereOrNull(
                (element) => element.name == "BACKGROUND_$backgroundType");

            return Scaffold(
              appBar: AppBar(
                title: Text(
                  list.values
                          .firstWhereOrNull(
                              (element) => element.name == "TITLE")
                          ?.data ??
                      "",
                  style: TextStyle(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                ),
                iconTheme: IconThemeData(
                  color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                ),
              ),
              body: Container(
                height: phoneHeight,
                width: phoneWidth,
                decoration: background == null
                    ? BoxDecoration(
                        color:
                            theme == "L" ? ThemeColors.light : ThemeColors.dark,
                      )
                    : BoxDecoration(
                        image: backgroundType == "IMAGE"
                            ? DecorationImage(
                                image: CachedNetworkImageProvider(
                                    background.data ?? ""),
                                fit: BoxFit.cover,
                              )
                            : null,
                        color: backgroundType == "COLOR"
                            ? Color(
                                int.parse("0xff${background.data ?? "FFFFFF"}"))
                            : null,
                      ),
                child: FutureBuilder(
                  future: future,
                  builder: (BuildContext context, AsyncSnapshot snapshot) {
                    if (snapshot.connectionState == ConnectionState.done) {
                      if (snapshot.hasData) {
                        return SingleChildScrollView(
                          padding: EdgeInsets.all(defaultPadding),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: EdgeInsets.only(
                                    top: topPaddingWithoutBar,
                                    bottom: defaultPadding),
                                child: Text(
                                  list.values
                                          .firstWhereOrNull((element) =>
                                              element.name == "HEADLINE")
                                          ?.data ??
                                      "",
                                  style: TextStyle(
                                      color: theme == "L"
                                          ? ThemeColors.dark
                                          : ThemeColors.light,
                                      fontSize: h1,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                              Text(
                                list.values
                                        .firstWhereOrNull((element) =>
                                            element.name == "SUBHEADLINE")
                                        ?.data ??
                                    "",
                                style: TextStyle(
                                    color: theme == "L"
                                        ? ThemeColors.dark
                                        : ThemeColors.light,
                                    fontSize: h3),
                              ),
                              SizedBox(height: verticalPaddingSmall),
                              platformView(
                                "Email",
                                "assets/icons/${boxGlobal.get("iconSet")}/tabler_mail.png",
                                support?.email,
                              ),
                              platformView(
                                "Messenger",
                                "assets/icons/Social media-messenger.png",
                                support?.messenger,
                              ),
                              platformView(
                                "WhatsApp",
                                "assets/icons/Social media-whatsapp-1.png",
                                support?.whatsapp,
                              ),
                              platformView(
                                "Telegram",
                                "assets/icons/Social media-telegram.png",
                                support?.telegram,
                              ),
                            ],
                          ),
                        );
                      }
                      return EmptyList(theme: "L");
                    }
                    return Container();
                  },
                ),
              ),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }

  Widget platformView(
      String platform, String image, List<SocialMedia>? socialMedia) {
    return socialMedia!.isEmpty
        ? SizedBox()
        : Container(
            margin: EdgeInsets.only(bottom: defaultInnerPadding),
            padding: EdgeInsets.symmetric(vertical: spacingHeightSmall),
            decoration: BoxDecoration(
              color: ThemeColors.light,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: ThemeColors.disabled),
            ),
            child: ListTile(
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (BuildContext context) => SupportPlatform(
                              platform: platform,
                              platformImage: image,
                              supportList: socialMedia,
                            )));
              },
              leading: Container(
                width: phoneWidth / 10,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  image: DecorationImage(image: AssetImage(image)),
                ),
              ),
              title: Text(
                platform,
                style: TextStyle(
                  color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                ),
              ),
              trailing: ImageIcon(AssetImage(
                  "assets/icons/${boxGlobal.get("iconSet")}/Standard_Moreleft.png")),
            ),
          );
  }

  Future<Map?> getCustomerSupport() async {
    EasyLoading.show();
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.customerSupport();
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        support = Support.fromJson(data['data']['platforms']);
      }
    }
    return data;
  }
}
