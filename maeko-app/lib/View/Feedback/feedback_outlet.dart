import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/location_service.dart';
import 'package:amverton/View/Custom%20Widgets/default_search_bar.dart';
import 'package:amverton/View/Feedback/Section/outlet_template1.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hive/hive.dart';

class FeedbackOutlet extends StatefulWidget {
  const FeedbackOutlet({Key? key}) : super(key: key);

  @override
  _FeedbackOutletState createState() => _FeedbackOutletState();
}

class _FeedbackOutletState extends State<FeedbackOutlet> {
  late Future future;
  Box boxGlobal = Hive.box("boxGlobal");

  List<Outlet> outletList = [];
  bool onSearch = false;
  TextEditingController searchController = TextEditingController();
  final StreamController streamController = StreamController.broadcast();

  @override
  void initState() {
    super.initState();
    future = getOutlet();
    searchController.addListener(() {
      streamController.add(searchController.text);
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      behavior: HitTestBehavior.translucent,
      child: Scaffold(
        backgroundColor: ThemeColors.light,
        appBar: AppBar(title: const Text("Feedback")),
        body: RefreshIndicator(
          color: ThemeColors.primaryDark,
          onRefresh: () {
            return Future.delayed(const Duration(seconds: 1), () {
              if (onSearch) {
                setState(() {});
              } else {
                setState(() {
                  future = getOutlet();
                });
              }
            });
          },
          child: SizedBox(
            height: phoneHeight,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: EdgeInsets.fromLTRB(
                  defaultPadding, 0, defaultPadding, bottomPaddingWithoutBar),
              child: Column(
                children: [
                  DefaultSearchBar(
                    controller: searchController,
                    hintText: "Search",
                    onChanged: (value) {
                      if (value.isEmpty && onSearch) {
                        setState(() {
                          onSearch = false;
                        });
                      } else if (value.isNotEmpty && !onSearch) {
                        setState(() {
                          onSearch = true;
                        });
                      }
                    },
                  ),
                  SizedBox(height: spacingHeightMedium),
                  Visibility(
                    visible: !onSearch,
                    child: Column(
                      children: [
                        Text(
                          "Choose an Outlet",
                          style: TextStyle(
                              color: ThemeColors.dark,
                              fontSize: h2,
                              fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: spacingHeightMedium),
                        FutureBuilder(
                            future: future,
                            builder:
                                (BuildContext context, AsyncSnapshot snapshot) {
                              if (snapshot.connectionState ==
                                  ConnectionState.done) {
                                return ListView.builder(
                                  shrinkWrap: true,
                                  physics: NeverScrollableScrollPhysics(),
                                  itemCount: outletList.length,
                                  itemBuilder:
                                      (BuildContext context, int index) {
                                    return OutletTemplate1(
                                        outlet: outletList[index]);
                                  },
                                );
                              }
                              return Container();
                            }),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: onSearch,
                    child: StreamBuilder(
                      stream: streamController.stream.asBroadcastStream(),
                      builder: (BuildContext context, AsyncSnapshot snapshot) {
                        if (snapshot.hasData) {
                          return Column(
                            children: [
                              Text(
                                "Search Result",
                                style: TextStyle(
                                    color: ThemeColors.dark,
                                    fontSize: h2,
                                    fontWeight: FontWeight.bold),
                              ),
                              SizedBox(height: spacingHeightMedium),
                              FutureBuilder(
                                future: getOutletSearch(snapshot.data),
                                builder: (BuildContext context,
                                    AsyncSnapshot snapshot) {
                                  if (snapshot.connectionState ==
                                      ConnectionState.done) {
                                    List<Outlet> searchList = (snapshot
                                            .data['data']['outlets'] as List)
                                        .map((data) => Outlet.fromJson(data))
                                        .toList();

                                    return ListView.builder(
                                      shrinkWrap: true,
                                      physics: NeverScrollableScrollPhysics(),
                                      itemCount: searchList.length,
                                      itemBuilder:
                                          (BuildContext context, int index) {
                                        return OutletTemplate1(
                                            outlet: searchList[index]);
                                      },
                                    );
                                  }
                                  return SizedBox(
                                    height: phoneHeight / 3,
                                    child: Center(
                                      child: CircularProgressIndicator(
                                          color: ThemeColors.primaryDark),
                                    ),
                                  );
                                },
                              ),
                            ],
                          );
                        }
                        return Container();
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<Map?> getOutlet() async {
    ApiGet apiGet = ApiGet();

    // check location permission
    Position? currentPosition;
    if (Platform.isIOS) {
      currentPosition = await LocationService.getCurrentPosition();
    } else {
      Position? permissionCheck =
          await LocationService.checkAndroidLocationPermission();
      if (permissionCheck != null)
        currentPosition = await LocationService.getCurrentPosition();
    }

    Map? data = await apiGet.outletList(
        currentPosition?.latitude, currentPosition?.longitude);

    if (data != null && data['data'] != null) {
      outletList = (data['data']['outlets'] as List)
          .map((data) => Outlet.fromJson(data))
          .toList();
    }
    return data;
  }

  Future<Map?> getOutletSearch(String? searchValue) async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.outletListSearch(searchValue);

    return data;
  }
}
