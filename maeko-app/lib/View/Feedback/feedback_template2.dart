import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/feedback_criteria.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
import 'package:amverton/View/Feedback/Section/rating_template1.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class FeedbackTemplate2 extends StatefulWidget {
  static String routeName = "/feedback/2";
  final dynamic args;

  const FeedbackTemplate2({Key? key, this.args}) : super(key: key);

  @override
  _FeedbackTemplate2State createState() => _FeedbackTemplate2State();

  static void setRate(BuildContext context, int index, int rating) {
    _FeedbackTemplate2State? state =
        context.findAncestorStateOfType<_FeedbackTemplate2State>();
    state?.setRate(index, rating);
  }
}

class _FeedbackTemplate2State extends State<FeedbackTemplate2> {
  late Future future;
  late Box boxGlobal;
  late Box boxAsset;
  String theme = "L";

  TextEditingController commentController = TextEditingController();
  List<int> rate = [];
  List<FeedbackCriteria> criteriaList = [];
  List<String> rateIcon = [];

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    String iconSet = boxGlobal.get("iconSet");
    rateIcon = [
      "assets/icons/$iconSet/Feedback Rating_Angry.png",
      "assets/icons/$iconSet/Feedback Rating_OKOK.png",
      "assets/icons/$iconSet/Feedback Rating_Meh.png",
      "assets/icons/$iconSet/Feedback Rating_Happy.png",
      "assets/icons/$iconSet/Feedback Rating_Satisfied.png",
    ];
    theme = accountAsset
            .firstWhereOrNull((element) => element.sectionName == "FEEDBACK")
            ?.theme ??
        "L";
    boxAsset = await Hive.openBox<Asset>('ACCOUNT+FEEDBACK+2');

    return await getFeedbackCriteria();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: future,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          String? titleBackgroundType = boxAsset.values
                  .firstWhereOrNull(
                      (element) => element.name == "TITLE_BACKGROUND_TYPE")
                  ?.data ??
              "";
          Asset? titleBackground = boxAsset.values.firstWhereOrNull((element) =>
              element.name == "TITLE_BACKGROUND_$titleBackgroundType");

          String? contentBackgroundType = boxAsset.values
                  .firstWhereOrNull(
                      (element) => element.name == "CONTENT_BACKGROUND_TYPE")
                  ?.data ??
              "";
          Asset? contentBackground = boxAsset.values.firstWhereOrNull(
              (element) =>
                  element.name == "CONTENT_BACKGROUND_$contentBackgroundType");

          return GestureDetector(
            onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
            behavior: HitTestBehavior.translucent,
            child: Scaffold(
              extendBodyBehindAppBar: true,
              extendBody: true,
              appBar: AppBar(
                title: Text(
                  boxAsset.values
                          .firstWhereOrNull(
                              (element) => element.name == "TITLE")
                          ?.data ??
                      "",
                  style: TextStyle(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                ),
                iconTheme: IconThemeData(
                  color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                ),
              ),
              body: Stack(
                children: [
                  Container(
                    width: phoneWidth,
                    height: phoneHeight,
                    decoration: contentBackground == null
                        ? BoxDecoration(
                            color: ThemeColors.light,
                          )
                        : BoxDecoration(
                            image: contentBackgroundType == "IMAGE"
                                ? DecorationImage(
                                    image: CachedNetworkImageProvider(
                                        contentBackground.data ?? ""),
                                    fit: BoxFit.cover,
                                  )
                                : null,
                            color: contentBackgroundType == "COLOR"
                                ? Color(int.parse(
                                    "0xff${contentBackground.data ?? "FFFFFF"}"))
                                : null,
                          ),
                  ),
                  Container(
                    width: phoneWidth,
                    height: phoneHeight / 3.5,
                    decoration: titleBackground == null
                        ? BoxDecoration(
                            color: ThemeColors.light,
                          )
                        : BoxDecoration(
                            image: titleBackgroundType == "IMAGE"
                                ? DecorationImage(
                                    image: CachedNetworkImageProvider(
                                        titleBackground.data ?? ""),
                                    fit: BoxFit.cover,
                                  )
                                : null,
                            color: titleBackgroundType == "COLOR"
                                ? Color(int.parse(
                                    "0xff${titleBackground.data ?? "FFFFFF"}"))
                                : null,
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(15),
                              bottomRight: Radius.circular(15),
                            ),
                          ),
                  ),
                  Container(
                    width: phoneWidth,
                    height: phoneHeight,
                    padding: EdgeInsets.fromLTRB(
                      defaultPadding,
                      verticalPaddingLarge + defaultPadding,
                      defaultPadding,
                      defaultPadding,
                    ),
                    child: SingleChildScrollView(
                      padding: EdgeInsets.only(bottom: verticalPaddingLarge),
                      child: Column(
                        children: [
                          Text(
                            boxAsset.values
                                    .firstWhereOrNull(
                                        (element) => element.name == "HEADLINE")
                                    ?.data ??
                                "",
                            style: TextStyle(
                              color: theme == "L"
                                  ? ThemeColors.dark
                                  : ThemeColors.light,
                              fontSize: h1,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: spacingHeightSmall),
                          Text(
                            boxAsset.values
                                    .firstWhereOrNull((element) =>
                                        element.name == "SUBHEADLINE")
                                    ?.data ??
                                "",
                            style: TextStyle(
                              color: theme == "L"
                                  ? ThemeColors.dark
                                  : ThemeColors.light,
                              fontSize: h3,
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.only(top: verticalPaddingSmall),
                            padding: EdgeInsets.all(defaultPadding),
                            width: phoneWidth,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              color: ThemeColors.light,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Choose a Branch",
                                  style: TextStyle(
                                    color: ThemeColors.dark,
                                    fontSize: h3,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                GestureDetector(
                                  onTap: () {
                                    Navigator.pop(context);
                                  },
                                  child: Container(
                                    margin: EdgeInsets.only(
                                        top: defaultInnerPadding),
                                    padding:
                                        EdgeInsets.all(defaultInnerPadding),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(
                                        color: ThemeColors.disabled,
                                      ),
                                    ),
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: Text(
                                            widget.args["outletName"],
                                            style: TextStyle(
                                              color: ThemeColors.gray,
                                              fontSize: h3,
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: spacingWidth),
                                        ImageIcon(AssetImage(
                                            "assets/icons/${boxGlobal.get("iconSet")}/Standard_Moreleft.png")),
                                      ],
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
                          SizedBox(height: spacingHeightMedium),
                          Column(
                            children:
                                List.generate(criteriaList.length, (index) {
                              return RatingTemplate1(
                                index: index,
                                title: criteriaList[index].name,
                                rate: rate[index],
                                rateIcon: rateIcon,
                              );
                            }),
                          ),
                          DefaultTextField1(
                            controller: commentController,
                            maxLines: 3,
                            hintText: "Your additional feedbacks & comments.",
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              ),
              bottomNavigationBar: Padding(
                padding: EdgeInsets.fromLTRB(
                  defaultInnerPadding,
                  defaultInnerPadding,
                  defaultInnerPadding,
                  bottomPaddingWithoutBar,
                ),
                child: DefaultButton(
                  text: "Submit",
                  buttonColor: ThemeColors.primaryDark,
                  onPressed: () {
                    submitFeedback();
                  },
                ),
              ),
            ),
          );
        }
        return Scaffold(appBar: AppBar());
      },
    );
  }

  setRate(int index, int rating) {
    setState(() {
      rate[index] = rating;
    });
  }

  void submitFeedback() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    Map? data = await apiPost.submitFeedback(
        widget.args["outletId"], criteriaList, rate, commentController.text);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          Navigator.pop(context);
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  Future<Map?> getFeedbackCriteria() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.feedbackCriteria();

    if (data != null && data['data'] != null) {
      criteriaList = (data['data']['criteria'] as List)
          .map((data) => FeedbackCriteria.fromJson(data))
          .toList();
      rate = List.generate(criteriaList.length, (index) => 5);
    }
    return data;
  }
}
