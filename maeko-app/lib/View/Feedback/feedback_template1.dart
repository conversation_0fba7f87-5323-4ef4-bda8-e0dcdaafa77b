import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/feedback_criteria.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/api_post.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_button.dart';
import 'package:amverton/View/Custom%20Widgets/default_dialog.dart';
import 'package:amverton/View/Custom%20Widgets/default_textfield1.dart';
import 'package:amverton/View/Feedback/Section/rating_template1.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';
import 'package:amverton/Constant/important_variables.dart'
    as important_variables;

class FeedbackTemplate1 extends StatefulWidget {
  static String routeName = "/feedback/1";
  final dynamic args;

  const FeedbackTemplate1({Key? key, this.args}) : super(key: key);

  @override
  _FeedbackTemplate1State createState() => _FeedbackTemplate1State();

  static void setRate(BuildContext context, int index, int rating) {
    _FeedbackTemplate1State? state =
        context.findAncestorStateOfType<_FeedbackTemplate1State>();
    state?.setRate(index, rating);
  }
}

class _FeedbackTemplate1State extends State<FeedbackTemplate1> {
  late Future future;
  late Box boxGlobal;
  late Box boxAsset;
  String theme = "L";

  TextEditingController commentController = TextEditingController();
  List<int> rate = [];
  List<FeedbackCriteria> criteriaList = [];
  List<String> rateIcon = [];

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    boxGlobal = await Hive.openBox('boxGlobal');
    String iconSet = boxGlobal.get("iconSet");
    rateIcon = [
      "assets/icons/$iconSet/Feedback Rating_Angry.png",
      "assets/icons/$iconSet/Feedback Rating_OKOK.png",
      "assets/icons/$iconSet/Feedback Rating_Meh.png",
      "assets/icons/$iconSet/Feedback Rating_Happy.png",
      "assets/icons/$iconSet/Feedback Rating_Satisfied.png",
    ];
    theme = accountAsset
            .firstWhereOrNull((element) => element.sectionName == "FEEDBACK")
            ?.theme ??
        "L";
    boxAsset = await Hive.openBox<Asset>('ACCOUNT+FEEDBACK+1');

    return await getFeedbackCriteria();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: future,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          String? backgroundType = boxAsset.values
                  .firstWhereOrNull(
                      (element) => element.name == "BACKGROUND_TYPE")
                  ?.data ??
              "";
          Asset? background = boxAsset.values.firstWhereOrNull(
              (element) => element.name == "BACKGROUND_$backgroundType");

          return GestureDetector(
            onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
            behavior: HitTestBehavior.translucent,
            child: Scaffold(
              extendBody: true,
              appBar: AppBar(
                title: Text(
                  boxAsset.values
                          .firstWhereOrNull(
                              (element) => element.name == "TITLE")
                          ?.data ??
                      "",
                  style: TextStyle(
                    color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                  ),
                ),
                iconTheme: IconThemeData(
                  color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
                ),
              ),
              body: Container(
                height: phoneHeight,
                decoration: background == null
                    ? BoxDecoration(
                        color: ThemeColors.light,
                      )
                    : BoxDecoration(
                        image: backgroundType == "IMAGE"
                            ? DecorationImage(
                                image: CachedNetworkImageProvider(
                                    background.data ?? ""),
                                fit: BoxFit.cover,
                              )
                            : null,
                        color: backgroundType == "COLOR"
                            ? Color(
                                int.parse("0xff${background.data ?? "FFFFFF"}"))
                            : null,
                      ),
                child: SingleChildScrollView(
                  padding: EdgeInsets.fromLTRB(
                    defaultPadding,
                    defaultPadding,
                    defaultPadding,
                    topPaddingWithoutBar,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: topPaddingWithoutBar),
                      Text(
                        boxAsset.values
                                .firstWhereOrNull(
                                    (element) => element.name == "HEADLINE")
                                ?.data ??
                            "",
                        style: TextStyle(
                          color: theme == "L"
                              ? ThemeColors.dark
                              : ThemeColors.light,
                          fontSize: h1,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: spacingHeightSmall),
                      Text(
                        boxAsset.values
                                .firstWhereOrNull(
                                    (element) => element.name == "SUBHEADLINE")
                                ?.data ??
                            "",
                        style: TextStyle(
                          color: theme == "L"
                              ? ThemeColors.dark
                              : ThemeColors.light,
                          fontSize: h3,
                        ),
                      ),
                      SizedBox(height: spacingHeightLarge),
                      Column(
                        children: List.generate(criteriaList.length, (index) {
                          return RatingTemplate1(
                            index: index,
                            title: criteriaList[index].name,
                            rate: rate[index],
                            rateIcon: rateIcon,
                          );
                        }),
                      ),
                      DefaultTextField1(
                        controller: commentController,
                        maxLines: 3,
                        hintText: "Your additional feedbacks & comments.",
                      )
                    ],
                  ),
                ),
              ),
              bottomNavigationBar: Padding(
                padding: EdgeInsets.fromLTRB(
                  defaultInnerPadding,
                  defaultInnerPadding,
                  defaultInnerPadding,
                  bottomPaddingWithoutBar,
                ),
                child: DefaultButton(
                  text: "Submit",
                  buttonColor: important_variables.projectName == "obriens"
                      ? ThemeColors.secondaryDark
                      : ThemeColors.primaryDark,
                  onPressed: () {
                    submitFeedback();
                  },
                ),
              ),
            ),
          );
        }
        return Scaffold(appBar: AppBar());
      },
    );
  }

  setRate(int index, int rating) {
    setState(() {
      rate[index] = rating;
    });
  }

  void submitFeedback() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    Map? data = await apiPost.submitFeedback(
        widget.args["outletId"], criteriaList, rate, commentController.text);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          Navigator.pop(context);
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }

  Future<Map?> getFeedbackCriteria() async {
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.feedbackCriteria();

    if (data != null && data['data'] != null) {
      criteriaList = (data['data']['criteria'] as List)
          .map((data) => FeedbackCriteria.fromJson(data))
          .toList();
      rate = List.generate(criteriaList.length, (index) => 5);
    }
    return data;
  }
}
