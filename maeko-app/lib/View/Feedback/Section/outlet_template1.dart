import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class OutletTemplate1 extends StatefulWidget {
  final Outlet outlet;

  const OutletTemplate1({Key? key, required this.outlet}) : super(key: key);

  @override
  _OutletTemplate1State createState() => _OutletTemplate1State();
}

class _OutletTemplate1State extends State<OutletTemplate1> {
  Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          Navigator.pushNamed(
            context,
            "/feedback/${accountAsset.firstWhereOrNull((element) => element.sectionName == "FEEDBACK")?.template ?? ""}",
            arguments: {
              "outletId": widget.outlet.id,
              "outletName": widget.outlet.name,
            },
          );
        },
        child: Container(
          margin: EdgeInsets.only(bottom: defaultInnerPadding),
          padding: EdgeInsets.all(defaultInnerPadding),
          width: phoneWidth,
          decoration: BoxDecoration(
            color: ThemeColors.light,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: ThemeColors.disabled),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: phoneWidth / 1.8,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AutoSizeText(
                      widget.outlet.name,
                      style: TextStyle(
                          color: ThemeColors.dark,
                          fontSize: h3,
                          fontWeight: FontWeight.bold),
                      maxLines: 2,
                    ),
                    SizedBox(height: spacingHeightMedium),
                    if (widget.outlet.phone != null)
                      AutoSizeText(
                        widget.outlet.phone!,
                        style: TextStyle(color: ThemeColors.dark, fontSize: h4),
                        maxLines: 1,
                      ),
                    SizedBox(height: spacingHeightSmall),
                    AutoSizeText(
                      widget.outlet.address,
                      style: TextStyle(color: ThemeColors.gray, fontSize: h4),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
              Container(
                width: phoneWidth / 4,
                height: phoneWidth / 4,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: CachedNetworkImage(
                    imageUrl: widget.outlet.image ?? "",
                    fit: BoxFit.cover,
                    placeholder: (context, url) {
                      return Center(
                        child: CircularProgressIndicator(
                          color: ThemeColors.primaryDark,
                        ),
                      );
                    },
                    errorWidget: (context, url, error) {
                      return Icon(
                        Icons.error_outline_outlined,
                        color: ThemeColors.primaryDark,
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ));
  }
}
