import 'package:flutter/material.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Feedback/feedback_template1.dart';
import 'package:amverton/View/Feedback/feedback_template2.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class RatingTemplate1 extends StatefulWidget {
  final int index;
  final String title;
  final int rate;
  final List<String> rateIcon;

  const RatingTemplate1({
    Key? key,
    required this.index,
    required this.title,
    required this.rate,
    required this.rateIcon,
  }) : super(key: key);

  @override
  State<RatingTemplate1> createState() => _RatingTemplate1State();
}

class _RatingTemplate1State extends State<RatingTemplate1> {
  Box boxGlobal = Hive.box("boxGlobal");
  String theme = accountAsset
          .firstWhereOrNull((element) => element.sectionName == "FEEDBACK")
          ?.theme ??
      "L";

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: defaultInnerPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.title,
            style: TextStyle(
              color: theme == "L" ? ThemeColors.dark : ThemeColors.light,
              fontSize: h3,
            ),
          ),
          SizedBox(height: spacingHeightSmall),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(widget.rateIcon.length, (value) {
              return GestureDetector(
                onTap: () {
                  String feedbackTemplate = accountAsset
                          .firstWhereOrNull(
                              (element) => element.sectionName == "FEEDBACK")
                          ?.template ??
                      "";

                  if (feedbackTemplate == "1") {
                    FeedbackTemplate1.setRate(context, widget.index, value + 1);
                  } else if (feedbackTemplate == "2") {
                    FeedbackTemplate2.setRate(context, widget.index, value + 1);
                  }
                },
                child: Container(
                  padding: const EdgeInsets.all(5),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: widget.rate == value + 1
                        ? ThemeColors.secondaryDark
                        : theme == "L"
                            ? ThemeColors.disabled
                            : ThemeColors.gray,
                  ),
                  child: ImageIcon(
                    AssetImage(
                      widget.rateIcon[value],
                    ),
                    color: widget.rate == value + 1
                        ? ThemeColors.light
                        : ThemeColors.dark,
                    size: 30,
                  ),
                ),
              );
            }),
          ),
          SizedBox(height: spacingHeightSmall),
        ],
      ),
    );
  }
}
