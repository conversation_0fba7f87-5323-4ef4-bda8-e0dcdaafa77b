import 'dart:convert';
import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:amverton/Model/address.dart';
import 'package:amverton/Repository/api_get.dart';
import 'package:amverton/Repository/api_post.dart';

class AddressState {
  final bool isLoading;
  final String? error;
  final List<Address> addressList;

  AddressState({
    this.isLoading = false,
    this.error,
    this.addressList = const [],
  });

  AddressState copyWith({
    bool? isLoading,
    String? error,
    List<Address>? addressList,
  }) {
    return AddressState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      addressList: addressList ?? this.addressList,
    );
  }
}

class AddressNotifier extends StateNotifier<AddressState> {
  final ApiPost apiPost;
  final ApiGet apiGet;
  final String erorrMessage = "Something Went Wrong! Please Try Again Later!";

  AddressNotifier(this.apiPost, this.apiGet) : super(AddressState());

  void reset() {
    state = AddressState();
  }

  Future<void> getAddressList() async {
    state = state.copyWith(isLoading: true);

    try {
      ApiGet apiGet = ApiGet();
      Map? data = await apiGet.addressList();

      if (data != null && data['data'] != null) {
        List<Address> _addressList = (data['data']['addresses'] as List)
            .map((data) => Address.fromJson(data))
            .toList();

        state = state.copyWith(
          isLoading: false,
          addressList: _addressList,
        );

        print("run finish");

        return;
      }

      state = state.copyWith(isLoading: false, error: data?['message']);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: erorrMessage);
    }
  }
}

final addressNotifierProvider =
    StateNotifierProvider<AddressNotifier, AddressState>((ref) {
  final apiPost = ApiPost();
  final apiGet = ApiGet();
  return AddressNotifier(apiPost, apiGet);
});
