import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:amverton/Model/cart/cart.dart';
import 'package:amverton/Model/cart/cart_page_variable.dart';
import 'package:amverton/Model/home.dart';
import 'package:amverton/Model/scanqr.dart';
import 'package:amverton/Model/user.dart';
import 'package:amverton/Model/voucher.dart';
import 'package:amverton/Notifier/notifiers.dart';

final homeProvider = StateNotifierProvider<HomeNotifier, Home>((ref) {
  return HomeNotifier();
});

final scanQrProvider = StateNotifierProvider<ScanQRNotifier, Scanqr>((ref) {
  return ScanQRNotifier();
});



final userProvider = StateNotifierProvider<UserNotifier, User>((ref) {
  return UserNotifier();
});

final cartProvider = StateNotifierProvider<CartNotifier, Cart>((ref) {
  return CartNotifier();
});

final cartPageVariableProvider =
    StateNotifierProvider<CartPageVariableNotifier, CartPageVariable>((ref) {
  return CartPageVariableNotifier();
});

final voucherRedeemProvider =
    StateNotifierProvider<VoucherRedeemNotifier, Voucher>((ref) {
  return VoucherRedeemNotifier();
});
