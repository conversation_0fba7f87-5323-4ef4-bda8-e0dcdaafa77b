import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';

class ThemeColors {
  static final Box boxColor = Hive.box("boxColor");

  static Color get primaryLight => Color(0xffffffff);

  static Color get primaryDark => Color(0xff595959);

  static Color get secondaryLight => Color(0xfff1b3c5);

  static Color get secondaryDark => Color(0xfff1b3c5);

  static Color get tertiaryLight => Color(0xFF7f7883);

  static Color get tertiaryDark => Color(0xFF7f7883);

  static Color get dark => Color(0xff000000);

  static Color get gray => Color(0xFF475467);

  static Color get light => Color(0xffFFFFFF);

  static Color get disabled => Color(0xffd3d3d3);

  // static Color get primaryLight => Color(0xff0B8074);

  // static Color get primaryDark => Color(0xff0B8074);

  // static Color get secondaryLight => Color(0xfff1b3c5);

  // static Color get secondaryDark => Color(0xfff1b3c5);

  // static Color get dark => Color(0xff000000);

  // static Color get gray => Color(0xff808080);

  // static Color get light => Color(0xffFFFFFF);

  // static Color get disabled => Color(0xffd3d3d3);
}
