import 'package:flutter/material.dart';
import 'package:amverton/View/Account/about_us_template1.dart';
import 'package:amverton/View/Account/delivery_refund_policy_template1.dart';
import 'package:amverton/View/Address/add_address_template1.dart';
import 'package:amverton/View/Address/add_address_template2.dart';
import 'package:amverton/View/Address/edit_address_template1.dart';
import 'package:amverton/View/Address/edit_address_template2.dart';
import 'package:amverton/View/Auth/Login/staff_login_template1.dart';
import 'package:amverton/View/OrderMenu/order_history_template1.dart';
import 'package:amverton/View/Auth/Login/sms_verification_template1.dart';
import 'package:amverton/View/Booking/booking_confirmation_template1.dart';
import 'package:amverton/View/Booking/booking_history_template1.dart';
import 'package:amverton/View/Booking/make_booking_template1.dart';
import 'package:amverton/View/Faq/faq_template2.dart';
import 'package:amverton/View/Feedback/feedback_template1.dart';
import 'package:amverton/View/Feedback/feedback_template2.dart';
import 'package:amverton/View/Membership/membership_template1.dart';
import 'package:amverton/View/OrderMenu/order_history_template2.dart';
import 'package:amverton/View/Refer/refer_template2.dart';
import 'package:amverton/View/Rewards/rewards_list_template1.dart';
import 'package:amverton/View/Rewards/rewards_list_template2.dart';
import 'package:amverton/View/Rewards/rewards_list_template3.dart';
import 'package:amverton/View/Settings/change_password_template1.dart';
import 'package:amverton/View/Settings/change_password_template2.dart';
import 'package:amverton/View/Settings/edit_profile_template1.dart';
import 'package:amverton/View/Account/privacy_policy_template1.dart';
import 'package:amverton/View/Customer%20Support/support_template1.dart';
import 'package:amverton/View/Account/tnc_template1.dart';
import 'package:amverton/View/Auth/Forgot%20Password/forgot_password_template1.dart';
import 'package:amverton/View/Auth/Forgot%20Password/forgot_password_template2.dart';
import 'package:amverton/View/Auth/Forgot%20Password/reset_password_template1.dart';
import 'package:amverton/View/Auth/Forgot%20Password/reset_password_template2.dart';
import 'package:amverton/View/Auth/Login/login_template1.dart';
import 'package:amverton/View/Auth/Register/register1_template1.dart';
import 'package:amverton/View/Auth/Register/register2_template1.dart';
import 'package:amverton/View/Auth/Register/register3_template1.dart';
import 'package:amverton/View/Faq/faq_template1.dart';
import 'package:amverton/View/Refer/refer_template1.dart';
import 'package:amverton/main.dart';

final Map<String, WidgetBuilder> routes = {
  // Login
  LoginTemplate1.routeName: (context) => const LoginTemplate1(),
  StaffLoginTemplate1.routeName: (context) => const StaffLoginTemplate1(),
  SMSVerificationTemplate1.routeName: (context) => SMSVerificationTemplate1(
      args: ModalRoute.of(context)!.settings.arguments),
  // Register
  Register1Template1.routeName: (context) =>
      Register1Template1(args: ModalRoute.of(context)!.settings.arguments),

  Register2Template1.routeName: (context) =>
      Register2Template1(args: ModalRoute.of(context)!.settings.arguments),

  Register3Template1.routeName: (context) =>
      Register3Template1(args: ModalRoute.of(context)!.settings.arguments),

  // Forgot Password
  ForgotPasswordTemplate1.routeName: (context) =>
      const ForgotPasswordTemplate1(),
  ForgotPasswordTemplate2.routeName: (context) =>
      const ForgotPasswordTemplate2(),
  ResetPasswordTemplate1.routeName: (context) =>
      ResetPasswordTemplate1(args: ModalRoute.of(context)!.settings.arguments),
  ResetPasswordTemplate2.routeName: (context) =>
      ResetPasswordTemplate2(args: ModalRoute.of(context)!.settings.arguments),
  // Booking
  MakeBookingTemplate1.routeName: (context) =>
      MakeBookingTemplate1(args: ModalRoute.of(context)!.settings.arguments),
  BookingConfirmationTemplate1.routeName: (context) =>
      BookingConfirmationTemplate1(
          args: ModalRoute.of(context)!.settings.arguments),
  BookingHistoryTemplate1.routeName: (context) =>
      const BookingHistoryTemplate1(),
  // Account
  EditProfileTemplate1.routeName: (context) =>
      EditProfileTemplate1(args: ModalRoute.of(context)!.settings.arguments),
  ChangePasswordTemplate1.routeName: (context) =>
      const ChangePasswordTemplate1(),
  ChangePasswordTemplate2.routeName: (context) =>
      const ChangePasswordTemplate2(),
  ReferTemplate1.routeName: (context) =>
      ReferTemplate1(args: ModalRoute.of(context)!.settings.arguments),
  ReferTemplate2.routeName: (context) =>
      ReferTemplate2(args: ModalRoute.of(context)!.settings.arguments),
  FeedbackTemplate1.routeName: (context) =>
      FeedbackTemplate1(args: ModalRoute.of(context)!.settings.arguments),
  FeedbackTemplate2.routeName: (context) =>
      FeedbackTemplate2(args: ModalRoute.of(context)!.settings.arguments),
  FaqTemplate1.routeName: (context) =>
      FaqTemplate1(args: ModalRoute.of(context)!.settings.arguments),
  FaqTemplate2.routeName: (context) =>
      FaqTemplate2(args: ModalRoute.of(context)!.settings.arguments),
  SupportTemplate1.routeName: (context) =>
      SupportTemplate1(args: ModalRoute.of(context)!.settings.arguments),
  MembershipTemplate1.routeName: (context) =>
      MembershipTemplate1(args: ModalRoute.of(context)!.settings.arguments),
  OrderHistoryTemplate1.routeName: (context) =>
      OrderHistoryTemplate1(args: ModalRoute.of(context)!.settings.arguments),
  OrderHistoryTemplate2.routeName: (context) =>
      OrderHistoryTemplate2(args: ModalRoute.of(context)!.settings.arguments),
  RewardsListTemplate1.routeName: (context) =>
      RewardsListTemplate1(args: ModalRoute.of(context)!.settings.arguments),
  RewardsListTemplate2.routeName: (context) =>
      RewardsListTemplate2(args: ModalRoute.of(context)!.settings.arguments),
  RewardsListTemplate3.routeName: (context) =>
      RewardsListTemplate3(args: ModalRoute.of(context)!.settings.arguments),
  AboutUsTemplate1.routeName: (context) =>
      AboutUsTemplate1(args: ModalRoute.of(context)!.settings.arguments),
  TncTemplate1.routeName: (context) =>
      TncTemplate1(args: ModalRoute.of(context)!.settings.arguments),
  PrivacyPolicyTemplate1.routeName: (context) =>
      PrivacyPolicyTemplate1(args: ModalRoute.of(context)!.settings.arguments),
  DeliveryRefundPolicyTemplate1.routeName: (context) =>
      DeliveryRefundPolicyTemplate1(
          args: ModalRoute.of(context)!.settings.arguments),
  AddAddressTemplate1.routeName: (context) => const AddAddressTemplate1(),
  AddAddressTemplate2.routeName: (context) => const AddAddressTemplate2(),
  EditAddressTemplate1.routeName: (context) =>
      EditAddressTemplate1(args: ModalRoute.of(context)!.settings.arguments),
  EditAddressTemplate2.routeName: (context) =>
      EditAddressTemplate2(args: ModalRoute.of(context)!.settings.arguments),
  MyHomePage.routeName: (context) => MyHomePage(),
};
