import 'package:amverton/View/Startup/loading_branding_screen.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:amverton/Constant/routes.dart';
import 'package:amverton/Constant/theme.dart';
import 'package:amverton/Constant/theme_colors.dart';
import 'package:amverton/Constant/theme_size.dart';
import 'package:amverton/Model/address.dart';
import 'package:amverton/Model/app_settings.dart';
import 'package:amverton/Model/country.dart';
import 'package:amverton/Model/outlet.dart';
import 'package:amverton/Model/pages.dart';
import 'package:amverton/Model/bottom_bar.dart';
import 'package:amverton/Model/user.dart';
import 'package:amverton/Repository/connectivity_wrapper.dart';
import 'package:amverton/Repository/my_navigator_observer.dart';
import 'package:amverton/Repository/modify_assets.dart';
import 'package:amverton/View/Custom%20Widgets/default_loading_gif.dart';
import 'package:amverton/View/OrderMenu/Section/order_service_popup_template1.dart';
import 'package:amverton/View/OrderMenu/deliver_address_option.dart';
import 'package:amverton/View/OrderMenu/order_menu_outlet.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'firebase_options.dart';

void main() async {
  await Hive.initFlutter();
  Hive
    ..registerAdapter(UserAdapter())
    ..registerAdapter(SectionAdapter())
    ..registerAdapter(TemplateAdapter())
    ..registerAdapter(AssetAdapter())
    ..registerAdapter(BottomBarAdapter())
    ..registerAdapter(OutletAdapter())
    ..registerAdapter(OperationHourAdapter())
    ..registerAdapter(HourAdapter())
    ..registerAdapter(AppSettingsAdapter())
    ..registerAdapter(LoginsAdapter())
    ..registerAdapter(DeliveryOptionsAdapter())
    ..registerAdapter(AddressAdapter())
    ..registerAdapter(CountryAdapter())
    ..registerAdapter(StatesAdapter());

  await Hive.openBox('box');
  await Hive.openBox('boxGlobal');
  await Hive.openBox('boxColor');
  await Hive.openBox('boxMenu');

  EasyLoading.instance
    ..dismissOnTap = false
    ..userInteractions = false
    ..contentPadding = EdgeInsets.all(0)
    ..radius = 80
    ..maskType = EasyLoadingMaskType.black;
  // ..indicatorWidget = DefaultLoadingGif();
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark.copyWith(
    statusBarColor: Colors.transparent, // Android
    statusBarBrightness: Brightness.light, // IOS
  ));

  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  versionNumber = packageInfo.version;

  runApp(ProviderScope(child: MyApp()));
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

String versionNumber = "";
GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ConnectivityWrapper(
      navigatorKey: navigatorKey,
      child: MaterialApp(
        title: 'Concorde',
        navigatorObservers: [MyNavigatorObserver()],
        theme: theme(context),
        home: const LoadingBrandScreen(),
        routes: routes,
        navigatorKey: navigatorKey,
        builder: EasyLoading.init(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

late GlobalKey barGlobalKey;

class MyHomePage extends StatefulWidget {
  static String routeName = "/my_home_page";

  MyHomePage({
    Key? key,
  }) : super(key: key);

  @override
  _MyHomePageState createState() => _MyHomePageState();

  static void closeDailyPopup(BuildContext context) {
    _MyHomePageState? state =
        context.findAncestorStateOfType<_MyHomePageState>();
    state?.closeDailyPopup();
  }

  static void navigateToTab(BuildContext context, String tabName) {
    final state = context.findAncestorStateOfType<_MyHomePageState>();
    state?.navigateToTab(tabName);
  }
}

class _MyHomePageState extends State<MyHomePage> {
  late Box box;
  bool isLogin = false;

  late Future future;
  late Box boxGlobal;
  List bottomBarList = [];
  List<IconData> hugeIconsList = [
    HugeIcons.strokeRoundedHome01,         // index 0
    HugeIcons.strokeRoundedServingFood,    // index 1
    HugeIcons.strokeRoundedQrCode,
    HugeIcons.strokeRoundedBookmarkCheck01, // index 2
    HugeIcons.strokeRoundedUser03,         // index 3
  ];
  AppSettings? appSettings;

  int _selectedIndex = 0;
  List<Widget> widgetOptions = [];

  late bool dailyPopUp;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final args =
        ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>;
    dailyPopUp = args['dailyPopUp'];
  }

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  void navigateToTab(String tabName) {
    final bottomBarList = boxGlobal.get("bottomBar") ?? [];
    final tabIndex = bottomBarList.indexWhere((element) => element.name == tabName);
    if (tabIndex != -1) {
      selectTab(tabIndex);
    }
  }

  openBox() async {
    barGlobalKey = GlobalKey();
    box = await Hive.openBox('box');
    isLogin = box.get("is_login") ?? false;
    boxGlobal = await Hive.openBox('boxGlobal');
    bottomBarList = boxGlobal.get("bottomBar") ?? [];
    appSettings = boxGlobal.get("appSettings") ?? null;

    defaultBar = await getDefaultBar();
    widgetOptions.addAll(defaultBar);

    return boxGlobal;
  }
@override
Widget build(BuildContext context) {
  return FutureBuilder(
    future: future,
    builder: (BuildContext context, AsyncSnapshot snapshot) {
      if (snapshot.connectionState == ConnectionState.done) {
        return Stack(
          children: [
            Scaffold(
              body: IndexedStack(
                index: _selectedIndex,
                children: widgetOptions,
              ),
              bottomNavigationBar: BottomNavigationBar(
                key: barGlobalKey,
                selectedFontSize: h3,
                type: BottomNavigationBarType.fixed,
                backgroundColor: ThemeColors.light,
                currentIndex: _selectedIndex,
                onTap: changeIndexBottomBar,
                items: List.generate(bottomBarList.length, (index) {
                  final bool isSelected = _selectedIndex == index;

                  // Support both Hive object or map
                  final String title = bottomBarList[index] is BottomBar
                      ? bottomBarList[index].title
                      : bottomBarList[index]['title'] ?? 'Tab';

                  return BottomNavigationBarItem(
                    icon: Padding(
                      padding: EdgeInsets.only(top: defaultPadding),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                         
                          Icon(
                            hugeIconsList[index],
                            size: isSelected? 32.0 : 29.0,
                            color: isSelected
                                ? ThemeColors.primaryDark
                                 : ThemeColors.gray,
                            
                          ),

                          SizedBox(height: 4),
                          Text(
                            title,
                            style: TextStyle(
                              fontSize: 12,
                              color: isSelected
                                  ? ThemeColors.primaryDark
                                  : ThemeColors.gray,
                              fontWeight:
                                  isSelected ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                    label: '', // keep this empty if you’re using custom label
                  );
                }),
              ),
            ),
            if (dailyPopUp && dailyPopUpWidget != null) dailyPopUpWidget!,
          ],
        );
      }
      return Container();
    },
  );
}

  //function
  void refreshPage(int index) {
    print("Page $index refreshed");
    int homeIndex =
        bottomBarList.indexWhere((element) => element.name == "HOME");

    int orderMenuIndex =
        bottomBarList.indexWhere((element) => element.name == "ORDER");
    int accountIndex =
        bottomBarList.indexWhere((element) => element.name == "ACCOUNT");

    if (index == homeIndex) {
      homePageKey.currentState?.onRefresh();
    }

    if (index == orderMenuIndex) {
      orderMenuPageKey.currentState?.onRefresh(null);
    }

    if (index == accountIndex) {
      //todo temporary refresh home also, wait account template get from api then remove it
      homePageKey.currentState?.onRefresh();
      accountPageKey.currentState?.onRefresh(null);
    }
  }

  closeDailyPopup() {
    setState(() {
      dailyPopUp = false;
    });
  }

  void changeIndexBottomBar(int index) async {

    if (index == 2 && !isLogin) {
      Navigator.of(context, rootNavigator: true)
          .pushNamed("/login/1");
    } else {
      selectTab(index);
    }
  }

  void selectTab(int index) {
    setState(() {
      _selectedIndex = index;
      widgetOptions.removeAt(index);
      widgetOptions.insert(index, defaultBar[index]);
      refreshPage(index);
    });
  }

}
