# Testing Guide

This project uses **Vitest** and **React Testing Library** for comprehensive unit and integration testing.

## 🧪 Testing Stack

- **Vitest** - Fast unit test framework (Vite-native)
- **React Testing Library** - Component testing utilities
- **@testing-library/jest-dom** - Custom Jest matchers
- **@testing-library/user-event** - User interaction simulation
- **jsdom** - DOM environment for testing

## 🚀 Running Tests

```bash
# Run tests in watch mode
npm test

# Run tests once
npm run test:run

# Run tests with coverage
npm run test:coverage

# Run tests with UI
npm run test:ui
```

## 📊 Test Coverage

Current coverage: **59 tests passing** with good coverage across core functionality:

- **Stores**: 100% coverage (authStore, uiStore)
- **Services**: 100% coverage (API functions)
- **Hooks**: 96% coverage (useAuth)
- **Pages**: 100% coverage for tested components (Login, Dashboard)
- **Lib**: 70% coverage (axios configuration)

## 🏗️ Test Structure

```
src/
├── test/
│   ├── setup.ts              # Test configuration
│   ├── utils.tsx              # Test utilities and helpers
│   └── integration/           # Integration tests
├── store/__tests__/           # Store unit tests
├── hooks/__tests__/           # Hook unit tests
├── pages/__tests__/           # Component unit tests
├── services/__tests__/        # API service tests
└── lib/__tests__/             # Utility function tests
```

## 🔧 Test Configuration

### Vitest Config (vite.config.ts)
```typescript
test: {
  globals: true,
  environment: 'jsdom',
  setupFiles: ['./src/test/setup.ts'],
  css: true,
  coverage: {
    provider: 'v8',
    reporter: ['text', 'json', 'html'],
    exclude: [
      'node_modules/',
      'src/test/',
      '**/*.d.ts',
      '**/*.config.*',
      'dist/',
    ],
  },
}
```

### Test Setup (src/test/setup.ts)
- Configures @testing-library/jest-dom
- Mocks localStorage, matchMedia, IntersectionObserver
- Sets up environment variables

## 📝 Test Categories

### 1. Store Tests
**Location**: `src/store/__tests__/`

Tests Zustand stores for state management:
- **authStore.test.ts**: Authentication state management
- **uiStore.test.ts**: UI state (sidebar, theme, notifications)

### 2. Hook Tests  
**Location**: `src/hooks/__tests__/`

Tests custom React hooks:
- **useAuth.test.tsx**: Authentication hooks (login, logout, me)

### 3. Component Tests
**Location**: `src/pages/__tests__/`

Tests React components:
- **Login.test.tsx**: Login form component
- **Dashboard.test.tsx**: Dashboard page component

### 4. Service Tests
**Location**: `src/services/__tests__/`

Tests API service functions:
- **api.test.ts**: All API endpoints (auth, users, dashboard)

### 5. Utility Tests
**Location**: `src/lib/__tests__/`

Tests utility functions:
- **axios.test.ts**: Axios configuration and interceptors

### 6. Integration Tests
**Location**: `src/test/integration/`

Tests component integration:
- **auth-flow.test.tsx**: Authentication flow testing

## 🛠️ Testing Utilities

### Custom Render Function
```typescript
// src/test/utils.tsx
import { render } from './utils'; // Use this instead of @testing-library/react

// Provides QueryClient and Router context automatically
render(<YourComponent />);
```

### Mock Data Generators
```typescript
import { mockUser, mockUsers, mockDashboardStats } from './test/utils';
```

### Common Test Patterns

#### Testing Components
```typescript
import { render, screen } from '../test/utils';
import { vi } from 'vitest';

it('should render component', () => {
  render(<MyComponent />);
  expect(screen.getByText('Hello')).toBeInTheDocument();
});
```

#### Testing Hooks
```typescript
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const wrapper = ({ children }) => (
  <QueryClientProvider client={new QueryClient()}>
    {children}
  </QueryClientProvider>
);

const { result } = renderHook(() => useMyHook(), { wrapper });
```

#### Testing API Calls
```typescript
import { vi } from 'vitest';
import * as api from '../services/api';

vi.mock('../services/api');
const mockedApi = vi.mocked(api);

mockedApi.authAPI.login.mockResolvedValueOnce({ user, token });
```

## 🎯 Best Practices

### 1. Test Structure
- Use descriptive test names
- Group related tests with `describe` blocks
- Use `beforeEach` for setup
- Clean up mocks with `vi.clearAllMocks()`

### 2. Mocking
- Mock external dependencies
- Use `vi.mock()` for module mocking
- Mock API calls consistently
- Reset mocks between tests

### 3. Assertions
- Use specific matchers (`toBeInTheDocument`, `toHaveBeenCalledWith`)
- Test user interactions with `userEvent`
- Wait for async operations with `waitFor`
- Test error states and loading states

### 4. Coverage Goals
- Aim for >80% coverage on critical paths
- Focus on business logic and user interactions
- Don't test implementation details
- Test edge cases and error conditions

## 🚨 Common Issues & Solutions

### Router Conflicts
```typescript
// ❌ Don't nest routers
render(<App />); // App already has BrowserRouter

// ✅ Use MemoryRouter for testing
render(
  <MemoryRouter>
    <YourComponent />
  </MemoryRouter>
);
```

### Async Testing
```typescript
// ❌ Don't forget to wait
expect(screen.getByText('Loading')).toBeInTheDocument();

// ✅ Wait for async operations
await waitFor(() => {
  expect(screen.getByText('Data loaded')).toBeInTheDocument();
});
```

### Mock Cleanup
```typescript
beforeEach(() => {
  vi.clearAllMocks(); // Clear mock call history
  vi.resetModules();  // Reset module cache if needed
});
```

## 📈 Extending Tests

### Adding New Tests
1. Create test file next to the component/function
2. Follow naming convention: `*.test.ts` or `*.test.tsx`
3. Import from `../test/utils` for components
4. Add appropriate mocks and setup

### Testing New Features
1. Write tests first (TDD approach)
2. Test happy path and edge cases
3. Mock external dependencies
4. Verify user interactions
5. Check error handling

## 🔍 Debugging Tests

### Useful Commands
```bash
# Run specific test file
npm test -- Login.test.tsx

# Run tests matching pattern
npm test -- --grep "should login"

# Debug with console output
npm test -- --reporter=verbose

# Run tests in UI mode
npm run test:ui
```

### Debug Tips
- Use `screen.debug()` to see rendered HTML
- Add `console.log` in tests for debugging
- Use `--reporter=verbose` for detailed output
- Check mock call history with `vi.mocked(fn).mock.calls`

---

**Happy Testing! 🎉**
