
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">38.99% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>356/913</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">85% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>68/80</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">69.09% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>38/55</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">38.99% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>356/913</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="src"><a href="src/index.html">src</a></td>
	<td data-value="15.09" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 15%"></div><div class="cover-empty" style="width: 85%"></div></div>
	</td>
	<td data-value="15.09" class="pct low">15.09%</td>
	<td data-value="53" class="abs low">8/53</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="15.09" class="pct low">15.09%</td>
	<td data-value="53" class="abs low">8/53</td>
	</tr>

<tr>
	<td class="file low" data-value="src/components/Layout"><a href="src/components/Layout/index.html">src/components/Layout</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="136" class="abs low">0/136</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="136" class="abs low">0/136</td>
	</tr>

<tr>
	<td class="file low" data-value="src/hooks"><a href="src/hooks/index.html">src/hooks</a></td>
	<td data-value="43.3" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 43%"></div><div class="cover-empty" style="width: 57%"></div></div>
	</td>
	<td data-value="43.3" class="pct low">43.3%</td>
	<td data-value="127" class="abs low">55/127</td>
	<td data-value="83.33" class="pct high">83.33%</td>
	<td data-value="12" class="abs high">10/12</td>
	<td data-value="83.33" class="pct high">83.33%</td>
	<td data-value="12" class="abs high">10/12</td>
	<td data-value="43.3" class="pct low">43.3%</td>
	<td data-value="127" class="abs low">55/127</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/lib"><a href="src/lib/index.html">src/lib</a></td>
	<td data-value="70.27" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 70%"></div><div class="cover-empty" style="width: 30%"></div></div>
	</td>
	<td data-value="70.27" class="pct medium">70.27%</td>
	<td data-value="74" class="abs medium">52/74</td>
	<td data-value="90" class="pct high">90%</td>
	<td data-value="10" class="abs high">9/10</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="70.27" class="pct medium">70.27%</td>
	<td data-value="74" class="abs medium">52/74</td>
	</tr>

<tr>
	<td class="file low" data-value="src/pages"><a href="src/pages/index.html">src/pages</a></td>
	<td data-value="29.67" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 29%"></div><div class="cover-empty" style="width: 71%"></div></div>
	</td>
	<td data-value="29.67" class="pct low">29.67%</td>
	<td data-value="401" class="abs low">119/401</td>
	<td data-value="83.33" class="pct high">83.33%</td>
	<td data-value="24" class="abs high">20/24</td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="10" class="abs medium">6/10</td>
	<td data-value="29.67" class="pct low">29.67%</td>
	<td data-value="401" class="abs low">119/401</td>
	</tr>

<tr>
	<td class="file high" data-value="src/services"><a href="src/services/index.html">src/services</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="52" class="abs high">52/52</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="52" class="abs high">52/52</td>
	</tr>

<tr>
	<td class="file high" data-value="src/store"><a href="src/store/index.html">src/store</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="70" class="abs high">70/70</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="18" class="abs high">18/18</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="70" class="abs high">70/70</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-23T15:11:40.742Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    