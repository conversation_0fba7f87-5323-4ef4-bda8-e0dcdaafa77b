#!/usr/bin/env node

/**
 * Demo script to showcase the testing capabilities
 * Run with: node test-demo.js
 */

const { execSync } = require('child_process');

console.log('🧪 Maeko Admin Panel - Testing Demo\n');

console.log('📊 Running all tests...');
try {
  execSync('npm test -- --run --reporter=verbose', { stdio: 'inherit' });
  console.log('\n✅ All tests passed!\n');
} catch (error) {
  console.log('\n❌ Some tests failed. Check output above.\n');
}

console.log('📈 Generating coverage report...');
try {
  execSync('npm run test:coverage', { stdio: 'inherit' });
  console.log('\n✅ Coverage report generated!\n');
} catch (error) {
  console.log('\n❌ Coverage generation failed.\n');
}

console.log('🎯 Test Summary:');
console.log('- ✅ Store Tests: authStore, uiStore');
console.log('- ✅ Hook Tests: useAuth');
console.log('- ✅ Component Tests: Login, Dashboard');
console.log('- ✅ Service Tests: API functions');
console.log('- ✅ Integration Tests: Auth flow');
console.log('- ✅ Utility Tests: Axios configuration');

console.log('\n🚀 Available test commands:');
console.log('- npm test           # Run tests in watch mode');
console.log('- npm run test:run   # Run tests once');
console.log('- npm run test:coverage # Run with coverage');
console.log('- npm run test:ui    # Run with UI interface');

console.log('\n📚 See TESTING.md for detailed documentation');
console.log('🎉 Happy testing!');
