import { describe, it, expect, beforeEach, vi } from 'vitest';
import { screen, waitFor, render } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MemoryRouter } from 'react-router-dom';
import App from '../../App';
import { useAuthStore } from '../../store/authStore';

// Custom render for integration tests without router conflict
const renderWithProviders = (ui: React.ReactElement, { initialEntries = ['/'] } = {}) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <MemoryRouter initialEntries={initialEntries}>
        {ui}
      </MemoryRouter>
    </QueryClientProvider>
  );
};

// Mock the API
vi.mock('../../services/api', () => ({
  authAPI: {
    login: vi.fn(),
    logout: vi.fn(),
    me: vi.fn(),
  },
  usersAPI: {},
  dashboardAPI: {
    getStats: vi.fn().mockResolvedValue({
      totalUsers: 100,
      activeUsers: 80,
      totalRevenue: 50000,
      monthlyGrowth: 15,
    }),
  },
}));

describe('Authentication Flow Integration', () => {
  beforeEach(() => {
    // Reset auth store
    useAuthStore.setState({
      user: null,
      token: null,
      isAuthenticated: false,
    });

    vi.clearAllMocks();
  });

  it('should show demo content', () => {
    const DemoComponent = () => (
      <div>
        <h1>🎉 Maeko Admin Panel</h1>
        <p>Your React + Vite + TypeScript + React Router + TanStack Query + Zustand + Axios setup is complete!</p>
      </div>
    );

    renderWithProviders(<DemoComponent />);

    expect(screen.getByText(/Maeko Admin Panel/)).toBeInTheDocument();
    expect(screen.getByText(/React \+ Vite \+ TypeScript/)).toBeInTheDocument();
  });

  it('should handle auth store state changes', () => {
    expect(useAuthStore.getState().isAuthenticated).toBe(false);

    // Simulate login
    useAuthStore.getState().login(
      { id: '1', email: '<EMAIL>', name: 'Test User', role: 'admin' },
      'mock-token'
    );

    expect(useAuthStore.getState().isAuthenticated).toBe(true);
    expect(useAuthStore.getState().user?.name).toBe('Test User');
  });

  it('should handle auth store logout', () => {
    // First login
    useAuthStore.getState().login(
      { id: '1', email: '<EMAIL>', name: 'Test User', role: 'admin' },
      'mock-token'
    );

    expect(useAuthStore.getState().isAuthenticated).toBe(true);

    // Then logout
    useAuthStore.getState().logout();

    expect(useAuthStore.getState().isAuthenticated).toBe(false);
    expect(useAuthStore.getState().user).toBeNull();
  });
});
