.role-detail {
  padding: 20px;
}

.role-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.back-link {
  color: #3498db;
  text-decoration: none;
  font-size: 14px;
  display: inline-block;
}

.back-link:hover {
  text-decoration: underline;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.role-info-section {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.role-header h1 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 28px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.system-badge {
  background-color: #f39c12;
  color: white;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  font-weight: 600;
}

.role-description {
  margin: 0 0 20px 0;
  color: #7f8c8d;
  font-size: 16px;
  line-height: 1.5;
}

.role-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.stat-label {
  font-size: 12px;
  color: #95a5a6;
  text-transform: uppercase;
  font-weight: 600;
}

.stat-value {
  font-size: 20px;
  color: #2c3e50;
  font-weight: 600;
}

.role-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
}

.permissions-section,
.users-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.permissions-section h2,
.users-section h2 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 20px;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 10px;
}

.permissions-grid {
  display: grid;
  gap: 15px;
}

.permission-card {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 15px;
  background-color: #f8f9fa;
}

.permission-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.permission-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 14px;
}

.permission-resource {
  background-color: #3498db;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  text-transform: uppercase;
  font-weight: 600;
}

.permission-description {
  margin: 0 0 10px 0;
  color: #7f8c8d;
  font-size: 12px;
  line-height: 1.4;
}

.action-badge {
  background-color: #27ae60;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  text-transform: uppercase;
  font-weight: 600;
}

.users-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.user-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #f8f9fa;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #3498db;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
}

.user-info {
  flex: 1;
}

.user-info h4 {
  margin: 0 0 2px 0;
  color: #2c3e50;
  font-size: 14px;
}

.user-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 12px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #95a5a6;
}

.empty-state p {
  margin: 0 0 15px 0;
  font-size: 14px;
}

@media (max-width: 768px) {
  .role-detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .role-stats {
    flex-direction: column;
    gap: 15px;
  }
  
  .role-content {
    grid-template-columns: 1fr;
  }
  
  .user-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .user-actions {
    width: 100%;
  }
}
