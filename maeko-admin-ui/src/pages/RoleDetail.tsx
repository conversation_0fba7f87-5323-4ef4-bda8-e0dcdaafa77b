import { useState } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { useRole } from '../hooks/useRoles';
import { useUsers } from '../hooks/useUsers';
import UserRoleAssignment from '../components/Roles/UserRoleAssignment';
import RoleModal from '../components/Roles/RoleModal';
import './RoleDetail.css';

const RoleDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  
  const { data: role, isLoading, error } = useRole(id!);
  const { data: usersData } = useUsers({ limit: 100 }); // Get users for assignment

  if (isLoading) {
    return <div className="loading">Loading role details...</div>;
  }

  if (error) {
    return <div className="error">Failed to load role details</div>;
  }

  if (!role) {
    return <div className="error">Role not found</div>;
  }

  // Get users with this role
  const usersWithRole = usersData?.users.filter(user => user.role === role.name) || [];

  return (
    <div className="role-detail">
      <div className="role-detail-header">
        <Link to="/roles" className="back-link">← Back to Roles</Link>
        <div className="header-actions">
          <button 
            className="btn btn-secondary"
            onClick={() => setIsEditModalOpen(true)}
            disabled={role.isSystem}
          >
            Edit Role
          </button>
          <button 
            className="btn btn-primary"
            onClick={() => setIsAssignModalOpen(true)}
          >
            Assign Users
          </button>
        </div>
      </div>
      
      <div className="role-info-section">
        <div className="role-header">
          <h1>
            {role.name}
            {role.isSystem && <span className="system-badge">System Role</span>}
          </h1>
          <p className="role-description">{role.description}</p>
        </div>
        
        <div className="role-stats">
          <div className="stat-item">
            <span className="stat-label">Users Assigned</span>
            <span className="stat-value">{role.userCount}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Permissions</span>
            <span className="stat-value">{role.permissions.length}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Created</span>
            <span className="stat-value">{new Date(role.createdAt).toLocaleDateString()}</span>
          </div>
        </div>
      </div>
      
      <div className="role-content">
        <div className="permissions-section">
          <h2>Permissions</h2>
          <div className="permissions-grid">
            {role.permissions.map((permission) => (
              <div key={permission.id} className="permission-card">
                <div className="permission-header">
                  <h4>{permission.name}</h4>
                  <span className="permission-resource">{permission.resource}</span>
                </div>
                <p className="permission-description">{permission.description}</p>
                <div className="permission-action">
                  <span className="action-badge">{permission.action}</span>
                </div>
              </div>
            ))}
          </div>
          
          {role.permissions.length === 0 && (
            <div className="empty-state">
              <p>No permissions assigned to this role.</p>
            </div>
          )}
        </div>
        
        <div className="users-section">
          <h2>Assigned Users ({usersWithRole.length})</h2>
          <div className="users-list">
            {usersWithRole.map((user) => (
              <div key={user.id} className="user-item">
                <div className="user-avatar">
                  {user.name.charAt(0).toUpperCase()}
                </div>
                <div className="user-info">
                  <h4>{user.name}</h4>
                  <p>{user.email}</p>
                </div>
                <div className="user-actions">
                  <Link to={`/users/${user.id}`} className="btn btn-sm">
                    View Profile
                  </Link>
                </div>
              </div>
            ))}
          </div>
          
          {usersWithRole.length === 0 && (
            <div className="empty-state">
              <p>No users assigned to this role.</p>
              <button 
                className="btn btn-primary"
                onClick={() => setIsAssignModalOpen(true)}
              >
                Assign Users
              </button>
            </div>
          )}
        </div>
      </div>
      
      {isEditModalOpen && (
        <RoleModal
          role={role}
          onClose={() => setIsEditModalOpen(false)}
        />
      )}
      
      {isAssignModalOpen && (
        <UserRoleAssignment
          role={role}
          onClose={() => setIsAssignModalOpen(false)}
        />
      )}
    </div>
  );
};

export default RoleDetail;
