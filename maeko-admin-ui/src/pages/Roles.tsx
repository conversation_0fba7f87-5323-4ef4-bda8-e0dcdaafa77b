import { useState } from 'react';
import { Link } from 'react-router-dom';
import { useRoles, useDeleteRole } from '../hooks/useRoles';
import RoleModal from '../components/Roles/RoleModal';
import './Roles.css';

const Roles = () => {
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingRole, setEditingRole] = useState(null);
  
  const { data, isLoading, error } = useRoles({ page, limit: 10, search });
  const deleteRoleMutation = useDeleteRole();

  const handleDelete = (id: string, name: string, isSystem: boolean) => {
    if (isSystem) {
      alert('System roles cannot be deleted');
      return;
    }
    
    if (window.confirm(`Are you sure you want to delete role "${name}"?`)) {
      deleteRoleMutation.mutate(id);
    }
  };

  const handleEdit = (role: any) => {
    setEditingRole(role);
    setIsModalOpen(true);
  };

  const handleCreate = () => {
    setEditingRole(null);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingRole(null);
  };

  if (isLoading) {
    return <div className="loading">Loading roles...</div>;
  }

  if (error) {
    return <div className="error">Failed to load roles</div>;
  }

  return (
    <div className="roles-page">
      <div className="roles-header">
        <div className="header-content">
          <h1>Role Management</h1>
          <p>Manage user roles and permissions</p>
        </div>
        <button className="btn btn-primary" onClick={handleCreate}>
          Create New Role
        </button>
      </div>
      
      <div className="roles-controls">
        <div className="search-box">
          <input
            type="text"
            placeholder="Search roles..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
      </div>
      
      <div className="roles-grid">
        {data?.roles.map((role) => (
          <div key={role.id} className="role-card">
            <div className="role-header">
              <div className="role-info">
                <h3 className="role-name">
                  {role.name}
                  {role.isSystem && <span className="system-badge">System</span>}
                </h3>
                <p className="role-description">{role.description}</p>
              </div>
              <div className="role-stats">
                <span className="user-count">{role.userCount} users</span>
              </div>
            </div>
            
            <div className="role-permissions">
              <h4>Permissions ({role.permissions.length})</h4>
              <div className="permission-list">
                {role.permissions.slice(0, 3).map((permission) => (
                  <span key={permission.id} className="permission-tag">
                    {permission.name}
                  </span>
                ))}
                {role.permissions.length > 3 && (
                  <span className="permission-more">
                    +{role.permissions.length - 3} more
                  </span>
                )}
              </div>
            </div>
            
            <div className="role-actions">
              <Link to={`/roles/${role.id}`} className="btn btn-sm">
                View Details
              </Link>
              <button 
                className="btn btn-sm btn-secondary"
                onClick={() => handleEdit(role)}
                disabled={role.isSystem}
              >
                Edit
              </button>
              <button 
                className="btn btn-sm btn-danger"
                onClick={() => handleDelete(role.id, role.name, role.isSystem)}
                disabled={deleteRoleMutation.isPending || role.isSystem}
              >
                Delete
              </button>
            </div>
            
            <div className="role-meta">
              <small>Created: {new Date(role.createdAt).toLocaleDateString()}</small>
            </div>
          </div>
        ))}
      </div>
      
      {data && data.totalPages > 1 && (
        <div className="pagination">
          <button 
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1}
          >
            Previous
          </button>
          <span>Page {page} of {data.totalPages}</span>
          <button 
            onClick={() => setPage(p => Math.min(data.totalPages, p + 1))}
            disabled={page === data.totalPages}
          >
            Next
          </button>
        </div>
      )}
      
      {isModalOpen && (
        <RoleModal
          role={editingRole}
          onClose={handleModalClose}
        />
      )}
    </div>
  );
};

export default Roles;
