import { useUIStore } from '../store/uiStore';
import './Settings.css';

const Settings = () => {
  const { theme, setTheme } = useUIStore();

  return (
    <div className="settings-page">
      <div className="settings-header">
        <h1>Settings</h1>
        <p>Manage your application preferences</p>
      </div>
      
      <div className="settings-sections">
        <div className="settings-section">
          <h2>Appearance</h2>
          <div className="setting-item">
            <label>Theme</label>
            <select 
              value={theme} 
              onChange={(e) => setTheme(e.target.value as 'light' | 'dark')}
            >
              <option value="light">Light</option>
              <option value="dark">Dark</option>
            </select>
          </div>
        </div>
        
        <div className="settings-section">
          <h2>Account</h2>
          <div className="setting-item">
            <label>Change Password</label>
            <button className="btn btn-secondary">Update Password</button>
          </div>
          <div className="setting-item">
            <label>Two-Factor Authentication</label>
            <button className="btn btn-secondary">Enable 2FA</button>
          </div>
        </div>
        
        <div className="settings-section">
          <h2>Notifications</h2>
          <div className="setting-item">
            <label>
              <input type="checkbox" defaultChecked />
              Email notifications
            </label>
          </div>
          <div className="setting-item">
            <label>
              <input type="checkbox" defaultChecked />
              Push notifications
            </label>
          </div>
        </div>
        
        <div className="settings-section">
          <h2>System</h2>
          <div className="setting-item">
            <label>API Configuration</label>
            <button className="btn btn-secondary">Configure API</button>
          </div>
          <div className="setting-item">
            <label>Export Data</label>
            <button className="btn btn-secondary">Export</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
