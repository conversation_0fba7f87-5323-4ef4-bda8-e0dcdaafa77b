import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useUser } from '../hooks/useUsers';
import './UserDetail.css';

const UserDetail = () => {
  const { id } = useParams<{ id: string }>();
  const { data: user, isLoading, error } = useUser(id!);

  if (isLoading) {
    return <div className="loading">Loading user details...</div>;
  }

  if (error) {
    return <div className="error">Failed to load user details</div>;
  }

  if (!user) {
    return <div className="error">User not found</div>;
  }

  return (
    <div className="user-detail">
      <div className="user-detail-header">
        <Link to="/users" className="back-link">← Back to Users</Link>
        <h1>User Details</h1>
      </div>
      
      <div className="user-info-card">
        <div className="user-avatar">
          <div className="avatar-placeholder">
            {user.name.charAt(0).toUpperCase()}
          </div>
        </div>
        
        <div className="user-info">
          <h2>{user.name}</h2>
          <p className="user-email">{user.email}</p>
          <span className={`role-badge role-${user.role.toLowerCase()}`}>
            {user.role}
          </span>
        </div>
      </div>
      
      <div className="user-details-grid">
        <div className="detail-section">
          <h3>Account Information</h3>
          <div className="detail-item">
            <label>User ID:</label>
            <span>{user.id}</span>
          </div>
          <div className="detail-item">
            <label>Email:</label>
            <span>{user.email}</span>
          </div>
          <div className="detail-item">
            <label>Role:</label>
            <span>{user.role}</span>
          </div>
          <div className="detail-item">
            <label>Created:</label>
            <span>{new Date(user.createdAt).toLocaleString()}</span>
          </div>
          <div className="detail-item">
            <label>Last Updated:</label>
            <span>{new Date(user.updatedAt).toLocaleString()}</span>
          </div>
        </div>
        
        <div className="detail-section">
          <h3>Actions</h3>
          <div className="action-buttons">
            <button className="btn btn-primary">Edit User</button>
            <button className="btn btn-secondary">Reset Password</button>
            <button className="btn btn-danger">Deactivate User</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDetail;
