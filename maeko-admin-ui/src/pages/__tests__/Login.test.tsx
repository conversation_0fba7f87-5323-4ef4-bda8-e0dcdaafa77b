import { describe, it, expect, vi } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../test/utils';
import Login from '../Login';
import * as useAuthHook from '../../hooks/useAuth';

// Mock the useLogin hook
vi.mock('../../hooks/useAuth');
const mockedUseAuth = vi.mocked(useAuthHook);

describe('Login Component', () => {
  const mockMutate = vi.fn();
  const mockUseLogin = {
    mutate: mockMutate,
    isPending: false,
    error: null,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockedUseAuth.useLogin.mockReturnValue(mockUseLogin);
  });

  it('should render login form with all elements', () => {
    render(<Login />);

    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
    expect(screen.getByText(/demo credentials/i)).toBeInTheDocument();
  });

  it('should show demo credentials', () => {
    render(<Login />);

    expect(screen.getByText(/admin@maeko\.com/)).toBeInTheDocument();
    expect(screen.getByText(/admin123/)).toBeInTheDocument();
  });

  it('should handle form submission with valid data', async () => {
    const user = userEvent.setup();
    render(<Login />);

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.click(submitButton);

    expect(mockMutate).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123',
    });
  });

  it('should require email and password fields', async () => {
    const user = userEvent.setup();
    render(<Login />);

    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);

    // Form should not submit without required fields
    expect(mockMutate).not.toHaveBeenCalled();
  });

  it('should show loading state when login is pending', () => {
    mockedUseAuth.useLogin.mockReturnValue({
      ...mockUseLogin,
      isPending: true,
    });

    render(<Login />);

    expect(screen.getByText(/signing in/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /signing in/i })).toBeDisabled();
  });

  it('should show error message when login fails', () => {
    mockedUseAuth.useLogin.mockReturnValue({
      ...mockUseLogin,
      error: new Error('Invalid credentials'),
    });

    render(<Login />);

    expect(screen.getByText(/login failed/i)).toBeInTheDocument();
  });

  it('should update input values when typing', async () => {
    const user = userEvent.setup();
    render(<Login />);

    const emailInput = screen.getByLabelText(/email/i) as HTMLInputElement;
    const passwordInput = screen.getByLabelText(/password/i) as HTMLInputElement;

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'mypassword');

    expect(emailInput.value).toBe('<EMAIL>');
    expect(passwordInput.value).toBe('mypassword');
  });

  it('should have proper form accessibility', () => {
    render(<Login />);

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);

    expect(emailInput).toHaveAttribute('type', 'email');
    expect(emailInput).toHaveAttribute('required');
    expect(passwordInput).toHaveAttribute('type', 'password');
    expect(passwordInput).toHaveAttribute('required');
  });
});
