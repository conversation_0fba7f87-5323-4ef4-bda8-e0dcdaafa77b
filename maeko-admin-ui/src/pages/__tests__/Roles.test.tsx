import { describe, it, expect, vi } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../test/utils';
import Roles from '../Roles';
import * as useRolesHook from '../../hooks/useRoles';

// Mock the useRoles hook
vi.mock('../../hooks/useRoles');
const mockedUseRoles = vi.mocked(useRolesHook);

// Mock the RoleModal component
vi.mock('../../components/Roles/RoleModal', () => ({
  default: ({ onClose }: { onClose: () => void }) => (
    <div data-testid="role-modal">
      <button onClick={onClose}>Close Modal</button>
    </div>
  ),
}));

describe('Roles Component', () => {
  const mockDeleteRole = vi.fn();
  const mockRolesData = {
    roles: [
      {
        id: '1',
        name: 'Admin',
        description: 'Administrator role with full access',
        permissions: [
          { id: 'p1', name: 'Read Users', description: 'Can read users', resource: 'users', action: 'read' },
          { id: 'p2', name: 'Write Users', description: 'Can write users', resource: 'users', action: 'write' },
        ],
        userCount: 5,
        isSystem: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      },
      {
        id: '2',
        name: 'Editor',
        description: 'Editor role with limited access',
        permissions: [
          { id: 'p1', name: 'Read Users', description: 'Can read users', resource: 'users', action: 'read' },
        ],
        userCount: 3,
        isSystem: false,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      },
    ],
    total: 2,
    page: 1,
    totalPages: 1,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockedUseRoles.useRoles.mockReturnValue({
      data: mockRolesData,
      isLoading: false,
      error: null,
    } as any);
    mockedUseRoles.useDeleteRole.mockReturnValue({
      mutate: mockDeleteRole,
      isPending: false,
    } as any);
  });

  it('should render roles page with header', () => {
    render(<Roles />);

    expect(screen.getByText('Role Management')).toBeInTheDocument();
    expect(screen.getByText('Manage user roles and permissions')).toBeInTheDocument();
    expect(screen.getByText('Create New Role')).toBeInTheDocument();
  });

  it('should render search input', () => {
    render(<Roles />);

    const searchInput = screen.getByPlaceholderText('Search roles...');
    expect(searchInput).toBeInTheDocument();
  });

  it('should display roles in grid format', async () => {
    render(<Roles />);

    await waitFor(() => {
      expect(screen.getByText('Admin')).toBeInTheDocument();
      expect(screen.getByText('Editor')).toBeInTheDocument();
    });

    expect(screen.getByText('Administrator role with full access')).toBeInTheDocument();
    expect(screen.getByText('Editor role with limited access')).toBeInTheDocument();
  });

  it('should show system badge for system roles', async () => {
    render(<Roles />);

    await waitFor(() => {
      expect(screen.getByText('System')).toBeInTheDocument();
    });
  });

  it('should display user counts', async () => {
    render(<Roles />);

    await waitFor(() => {
      expect(screen.getByText('5 users')).toBeInTheDocument();
      expect(screen.getByText('3 users')).toBeInTheDocument();
    });
  });

  it('should display permissions count and preview', async () => {
    render(<Roles />);

    await waitFor(() => {
      expect(screen.getByText('Permissions (2)')).toBeInTheDocument();
      expect(screen.getByText('Permissions (1)')).toBeInTheDocument();
      expect(screen.getAllByText('Read Users')).toHaveLength(2); // Appears in both roles
      expect(screen.getByText('Write Users')).toBeInTheDocument();
    });
  });

  it('should open create modal when create button is clicked', async () => {
    const user = userEvent.setup();
    render(<Roles />);

    const createButton = screen.getByText('Create New Role');
    await user.click(createButton);

    expect(screen.getByTestId('role-modal')).toBeInTheDocument();
  });

  it('should handle role deletion for non-system roles', async () => {
    const user = userEvent.setup();
    // Mock window.confirm
    vi.stubGlobal('confirm', vi.fn(() => true));
    
    render(<Roles />);

    await waitFor(() => {
      const deleteButtons = screen.getAllByText('Delete');
      expect(deleteButtons).toHaveLength(2);
    });

    const deleteButtons = screen.getAllByText('Delete');
    await user.click(deleteButtons[1]); // Click delete for Editor role (non-system)

    expect(window.confirm).toHaveBeenCalledWith('Are you sure you want to delete role "Editor"?');
    expect(mockDeleteRole).toHaveBeenCalledWith('2');
  });

  it('should disable delete button for system roles', async () => {
    render(<Roles />);

    await waitFor(() => {
      const deleteButtons = screen.getAllByText('Delete');
      expect(deleteButtons).toHaveLength(2);

      // System role delete button should be disabled
      expect(deleteButtons[0]).toBeDisabled();
      // Non-system role delete button should be enabled
      expect(deleteButtons[1]).not.toBeDisabled();
    });
  });

  it('should show loading state', () => {
    mockedUseRoles.useRoles.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    } as any);

    render(<Roles />);

    expect(screen.getByText('Loading roles...')).toBeInTheDocument();
  });

  it('should show error state', () => {
    mockedUseRoles.useRoles.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error('Failed to load'),
    } as any);

    render(<Roles />);

    expect(screen.getByText('Failed to load roles')).toBeInTheDocument();
  });

  it('should handle search input changes', async () => {
    const user = userEvent.setup();
    render(<Roles />);

    const searchInput = screen.getByPlaceholderText('Search roles...');
    await user.type(searchInput, 'admin');

    expect(searchInput).toHaveValue('admin');
  });
});
