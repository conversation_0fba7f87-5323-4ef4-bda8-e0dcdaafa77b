import { describe, it, expect, vi } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import { render, mockDashboardStats } from '../../test/utils';
import Dashboard from '../Dashboard';
import * as api from '../../services/api';

// Mock the API
vi.mock('../../services/api');
const mockedApi = vi.mocked(api);

describe('Dashboard Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render dashboard header', async () => {
    mockedApi.dashboardAPI.getStats.mockResolvedValueOnce(mockDashboardStats);
    render(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Overview of your admin panel')).toBeInTheDocument();
    });
  });

  it('should show loading state initially', () => {
    mockedApi.dashboardAPI.getStats.mockImplementation(() => new Promise(() => {})); // Never resolves
    render(<Dashboard />);

    expect(screen.getByText(/loading dashboard/i)).toBeInTheDocument();
  });

  it('should display stats when data is loaded', async () => {
    mockedApi.dashboardAPI.getStats.mockResolvedValueOnce(mockDashboardStats);
    render(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('Total Users')).toBeInTheDocument();
      expect(screen.getByText('150')).toBeInTheDocument();
      
      expect(screen.getByText('Active Users')).toBeInTheDocument();
      expect(screen.getByText('120')).toBeInTheDocument();
      
      expect(screen.getByText('Total Revenue')).toBeInTheDocument();
      expect(screen.getByText('$50000')).toBeInTheDocument();
      
      expect(screen.getByText('Monthly Growth')).toBeInTheDocument();
      expect(screen.getByText('12.5%')).toBeInTheDocument();
    });
  });

  it('should show error state when API fails', async () => {
    mockedApi.dashboardAPI.getStats.mockRejectedValueOnce(new Error('API Error'));
    render(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText(/failed to load dashboard data/i)).toBeInTheDocument();
    });
  });

  it('should render dashboard sections', async () => {
    mockedApi.dashboardAPI.getStats.mockResolvedValueOnce(mockDashboardStats);
    render(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('Recent Activity')).toBeInTheDocument();
      expect(screen.getByText('Quick Actions')).toBeInTheDocument();
    });
  });

  it('should render action buttons', async () => {
    mockedApi.dashboardAPI.getStats.mockResolvedValueOnce(mockDashboardStats);
    render(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('Add New User')).toBeInTheDocument();
      expect(screen.getByText('View Reports')).toBeInTheDocument();
      expect(screen.getByText('System Settings')).toBeInTheDocument();
    });
  });

  it('should handle missing stats gracefully', async () => {
    mockedApi.dashboardAPI.getStats.mockResolvedValueOnce({
      totalUsers: undefined,
      activeUsers: undefined,
      totalRevenue: undefined,
      monthlyGrowth: undefined,
    });
    render(<Dashboard />);

    await waitFor(() => {
      const zeroElements = screen.getAllByText('0');
      expect(zeroElements.length).toBeGreaterThan(0); // Should show 0 for undefined values
    });
  });

  it('should have proper stat card structure', async () => {
    mockedApi.dashboardAPI.getStats.mockResolvedValueOnce(mockDashboardStats);
    render(<Dashboard />);

    await waitFor(() => {
      const statCards = screen.getAllByText(/Total Users|Active Users|Total Revenue|Monthly Growth/);
      expect(statCards).toHaveLength(4);
    });
  });
});
