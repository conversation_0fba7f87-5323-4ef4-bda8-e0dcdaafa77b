import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { usersAPI, type CreateUserRequest, type User } from '../services/api';

export const useUsers = (params?: { page?: number; limit?: number; search?: string }) => {
  return useQuery({
    queryKey: ['users', params],
    queryFn: () => usersAPI.getUsers(params),
    keepPreviousData: true, // Keep previous data while fetching new data
  });
};

export const useUser = (id: string) => {
  return useQuery({
    queryKey: ['users', id],
    queryFn: () => usersAPI.getUser(id),
    enabled: !!id,
  });
};

export const useCreateUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (userData: CreateUserRequest) => usersAPI.createUser(userData),
    onSuccess: () => {
      // Invalidate and refetch users list
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    meta: {
      successMessage: 'User created successfully',
    },
  });
};

export const useUpdateUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, userData }: { id: string; userData: Partial<User> }) =>
      usersAPI.updateUser(id, userData),
    onSuccess: (updatedUser) => {
      // Update the specific user in cache
      queryClient.setQueryData(['users', updatedUser.id], updatedUser);
      
      // Invalidate users list to refetch
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    meta: {
      successMessage: 'User updated successfully',
    },
  });
};

export const useDeleteUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => usersAPI.deleteUser(id),
    onSuccess: (_, deletedId) => {
      // Remove user from cache
      queryClient.removeQueries({ queryKey: ['users', deletedId] });
      
      // Invalidate users list
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    meta: {
      successMessage: 'User deleted successfully',
    },
  });
};
