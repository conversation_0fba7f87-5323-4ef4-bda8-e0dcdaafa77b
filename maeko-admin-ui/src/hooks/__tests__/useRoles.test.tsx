import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useRoles, useCreateRole, useUpdateRole, useDeleteRole } from '../useRoles';
import * as api from '../../services/api';

// Mock the API
vi.mock('../../services/api');
const mockedApi = vi.mocked(api);

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useRoles hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('useRoles', () => {
    it('should fetch roles with parameters', async () => {
      const mockRolesData = {
        roles: [
          {
            id: '1',
            name: 'Admin',
            description: 'Administrator role',
            permissions: [],
            userCount: 5,
            isSystem: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
        ],
        total: 1,
        page: 1,
        totalPages: 1,
      };

      mockedApi.rolesAPI.getRoles.mockResolvedValueOnce(mockRolesData);

      const wrapper = createWrapper();
      const { result } = renderHook(() => useRoles({ page: 1, limit: 10 }), { wrapper });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockedApi.rolesAPI.getRoles).toHaveBeenCalledWith({ page: 1, limit: 10 });
      expect(result.current.data).toEqual(mockRolesData);
    });
  });

  describe('useCreateRole', () => {
    it('should create a new role', async () => {
      const mockRole = {
        id: '2',
        name: 'Editor',
        description: 'Editor role',
        permissions: [],
        userCount: 0,
        isSystem: false,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      };

      mockedApi.rolesAPI.createRole.mockResolvedValueOnce(mockRole);

      const wrapper = createWrapper();
      const { result } = renderHook(() => useCreateRole(), { wrapper });

      const roleData = {
        name: 'Editor',
        description: 'Editor role',
        permissionIds: ['perm1', 'perm2'],
      };

      result.current.mutate(roleData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockedApi.rolesAPI.createRole).toHaveBeenCalledWith(roleData);
    });
  });

  describe('useUpdateRole', () => {
    it('should update an existing role', async () => {
      const mockUpdatedRole = {
        id: '1',
        name: 'Updated Admin',
        description: 'Updated administrator role',
        permissions: [],
        userCount: 5,
        isSystem: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-02T00:00:00Z',
      };

      mockedApi.rolesAPI.updateRole.mockResolvedValueOnce(mockUpdatedRole);

      const wrapper = createWrapper();
      const { result } = renderHook(() => useUpdateRole(), { wrapper });

      const updateData = {
        id: '1',
        roleData: {
          name: 'Updated Admin',
          description: 'Updated administrator role',
        },
      };

      result.current.mutate(updateData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockedApi.rolesAPI.updateRole).toHaveBeenCalledWith('1', updateData.roleData);
    });
  });

  describe('useDeleteRole', () => {
    it('should delete a role', async () => {
      mockedApi.rolesAPI.deleteRole.mockResolvedValueOnce(undefined);

      const wrapper = createWrapper();
      const { result } = renderHook(() => useDeleteRole(), { wrapper });

      result.current.mutate('1');

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockedApi.rolesAPI.deleteRole).toHaveBeenCalledWith('1');
    });
  });
});
