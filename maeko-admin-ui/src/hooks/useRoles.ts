import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { rolesAPI, permissionsAPI, type CreateRoleRequest, type UpdateRoleRequest, type Role } from '../services/api';

export const useRoles = (params?: { page?: number; limit?: number; search?: string }) => {
  return useQuery({
    queryKey: ['roles', params],
    queryFn: () => rolesAPI.getRoles(params),
    keepPreviousData: true,
  });
};

export const useRole = (id: string) => {
  return useQuery({
    queryKey: ['roles', id],
    queryFn: () => rolesAPI.getRole(id),
    enabled: !!id,
  });
};

export const usePermissions = () => {
  return useQuery({
    queryKey: ['permissions'],
    queryFn: () => permissionsAPI.getPermissions(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const usePermissionsByResource = () => {
  return useQuery({
    queryKey: ['permissions', 'by-resource'],
    queryFn: () => permissionsAPI.getPermissionsByResource(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useCreateRole = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (roleData: CreateRoleRequest) => rolesAPI.createRole(roleData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
    },
    meta: {
      successMessage: 'Role created successfully',
    },
  });
};

export const useUpdateRole = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, roleData }: { id: string; roleData: UpdateRoleRequest }) =>
      rolesAPI.updateRole(id, roleData),
    onSuccess: (updatedRole) => {
      queryClient.setQueryData(['roles', updatedRole.id], updatedRole);
      queryClient.invalidateQueries({ queryKey: ['roles'] });
    },
    meta: {
      successMessage: 'Role updated successfully',
    },
  });
};

export const useDeleteRole = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => rolesAPI.deleteRole(id),
    onSuccess: (_, deletedId) => {
      queryClient.removeQueries({ queryKey: ['roles', deletedId] });
      queryClient.invalidateQueries({ queryKey: ['roles'] });
    },
    meta: {
      successMessage: 'Role deleted successfully',
    },
  });
};

export const useAssignRoleToUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ userId, roleId }: { userId: string; roleId: string }) =>
      rolesAPI.assignRoleToUser(userId, roleId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['roles'] });
    },
    meta: {
      successMessage: 'Role assigned successfully',
    },
  });
};

export const useRemoveRoleFromUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (userId: string) => rolesAPI.removeRoleFromUser(userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['roles'] });
    },
    meta: {
      successMessage: 'Role removed successfully',
    },
  });
};
