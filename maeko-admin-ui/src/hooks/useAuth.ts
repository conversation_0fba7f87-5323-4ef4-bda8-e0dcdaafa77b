import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { authAPI, type LoginRequest } from '../services/api';
import { useAuthStore } from '../store/authStore';
import { useUIStore } from '../store/uiStore';

export const useLogin = () => {
  const login = useAuthStore((state) => state.login);
  const addNotification = useUIStore((state) => state.addNotification);
  
  return useMutation({
    mutationFn: (credentials: LoginRequest) => authAPI.login(credentials),
    onSuccess: (data) => {
      login(data.user, data.token);
      addNotification({
        type: 'success',
        message: `Welcome back, ${data.user.name}!`,
      });
    },
    onError: (error: any) => {
      addNotification({
        type: 'error',
        message: error?.response?.data?.message || 'Login failed',
      });
    },
  });
};

export const useLogout = () => {
  const logout = useAuthStore((state) => state.logout);
  const queryClient = useQueryClient();
  const addNotification = useUIStore((state) => state.addNotification);
  
  return useMutation({
    mutationFn: () => authAPI.logout(),
    onSuccess: () => {
      logout();
      queryClient.clear(); // Clear all cached data
      addNotification({
        type: 'success',
        message: 'Logged out successfully',
      });
    },
    onError: () => {
      // Even if logout fails on server, clear local state
      logout();
      queryClient.clear();
    },
  });
};

export const useMe = () => {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const updateUser = useAuthStore((state) => state.updateUser);
  
  return useQuery({
    queryKey: ['auth', 'me'],
    queryFn: () => authAPI.me(),
    enabled: isAuthenticated,
    onSuccess: (user) => {
      updateUser(user);
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};
