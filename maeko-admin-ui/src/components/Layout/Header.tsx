import { useAuthStore } from '../../store/authStore';
import { useLogout } from '../../hooks/useAuth';
import './Header.css';

const Header = () => {
  const user = useAuthStore((state) => state.user);
  const logoutMutation = useLogout();

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  return (
    <header className="header">
      <div className="header-content">
        <div className="header-left">
          <h2>Welcome back, {user?.name || 'Admin'}!</h2>
        </div>
        
        <div className="header-right">
          <div className="user-menu">
            <div className="user-info">
              <span className="user-name">{user?.name}</span>
              <span className="user-role">{user?.role}</span>
            </div>
            <button 
              className="logout-btn"
              onClick={handleLogout}
              disabled={logoutMutation.isPending}
            >
              {logoutMutation.isPending ? 'Logging out...' : 'Logout'}
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
