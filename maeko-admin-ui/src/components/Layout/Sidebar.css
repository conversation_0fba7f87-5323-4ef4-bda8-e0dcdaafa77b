.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 250px;
  background-color: #2c3e50;
  color: white;
  transition: width 0.3s ease;
  z-index: 1000;
}

.sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #34495e;
}

.logo {
  font-size: 18px;
  font-weight: bold;
  color: #ecf0f1;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: #ecf0f1;
  cursor: pointer;
  font-size: 16px;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.sidebar-toggle:hover {
  background-color: #34495e;
}

.sidebar-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #bdc3c7;
  text-decoration: none;
  transition: all 0.2s;
  border-left: 3px solid transparent;
}

.nav-link:hover {
  background-color: #34495e;
  color: #ecf0f1;
}

.nav-link.active {
  background-color: #3498db;
  color: white;
  border-left-color: #2980b9;
}

.nav-icon {
  font-size: 18px;
  margin-right: 12px;
  min-width: 20px;
}

.sidebar.collapsed .nav-label {
  display: none;
}

.sidebar.collapsed .nav-link {
  justify-content: center;
  padding: 15px 10px;
}

.sidebar.collapsed .nav-icon {
  margin-right: 0;
}
