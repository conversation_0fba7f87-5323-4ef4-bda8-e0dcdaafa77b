import { useState, useEffect } from 'react';
import { usePermissionsByResource, useCreateRole, useUpdateRole } from '../../hooks/useRoles';
import type { Role } from '../../services/api';
import './RoleModal.css';

interface RoleModalProps {
  role?: Role | null;
  onClose: () => void;
}

const RoleModal = ({ role, onClose }: RoleModalProps) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    permissionIds: [] as string[],
  });

  const { data: permissionsByResource, isLoading: permissionsLoading } = usePermissionsByResource();
  const createRoleMutation = useCreateRole();
  const updateRoleMutation = useUpdateRole();

  const isEditing = !!role;
  const isLoading = createRoleMutation.isPending || updateRoleMutation.isPending;

  useEffect(() => {
    if (role) {
      setFormData({
        name: role.name,
        description: role.description,
        permissionIds: role.permissions.map(p => p.id),
      });
    }
  }, [role]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (isEditing) {
        await updateRoleMutation.mutateAsync({
          id: role.id,
          roleData: formData,
        });
      } else {
        await createRoleMutation.mutateAsync(formData);
      }
      onClose();
    } catch (error) {
      // Error handling is done by the mutation
    }
  };

  const handlePermissionToggle = (permissionId: string) => {
    setFormData(prev => ({
      ...prev,
      permissionIds: prev.permissionIds.includes(permissionId)
        ? prev.permissionIds.filter(id => id !== permissionId)
        : [...prev.permissionIds, permissionId],
    }));
  };

  const handleSelectAllInResource = (resourcePermissions: any[], selectAll: boolean) => {
    const resourcePermissionIds = resourcePermissions.map(p => p.id);
    
    setFormData(prev => ({
      ...prev,
      permissionIds: selectAll
        ? [...new Set([...prev.permissionIds, ...resourcePermissionIds])]
        : prev.permissionIds.filter(id => !resourcePermissionIds.includes(id)),
    }));
  };

  if (permissionsLoading) {
    return (
      <div className="modal-overlay">
        <div className="modal">
          <div className="loading">Loading permissions...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal role-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>{isEditing ? 'Edit Role' : 'Create New Role'}</h2>
          <button className="modal-close" onClick={onClose}>×</button>
        </div>
        
        <form onSubmit={handleSubmit} className="modal-content">
          <div className="form-section">
            <div className="form-group">
              <label htmlFor="name">Role Name *</label>
              <input
                id="name"
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                required
                disabled={role?.isSystem}
                placeholder="Enter role name"
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="description">Description</label>
              <textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter role description"
                rows={3}
              />
            </div>
          </div>
          
          <div className="permissions-section">
            <h3>Permissions</h3>
            <p className="permissions-help">
              Select the permissions this role should have. Permissions are grouped by resource.
            </p>
            
            {permissionsByResource && Object.entries(permissionsByResource).map(([resource, permissions]) => {
              const selectedInResource = permissions.filter(p => formData.permissionIds.includes(p.id)).length;
              const allSelected = selectedInResource === permissions.length;
              const someSelected = selectedInResource > 0 && selectedInResource < permissions.length;
              
              return (
                <div key={resource} className="permission-group">
                  <div className="permission-group-header">
                    <label className="permission-group-label">
                      <input
                        type="checkbox"
                        checked={allSelected}
                        ref={(el) => {
                          if (el) el.indeterminate = someSelected;
                        }}
                        onChange={(e) => handleSelectAllInResource(permissions, e.target.checked)}
                      />
                      <span className="resource-name">{resource}</span>
                      <span className="permission-count">({selectedInResource}/{permissions.length})</span>
                    </label>
                  </div>
                  
                  <div className="permission-list">
                    {permissions.map((permission) => (
                      <label key={permission.id} className="permission-item">
                        <input
                          type="checkbox"
                          checked={formData.permissionIds.includes(permission.id)}
                          onChange={() => handlePermissionToggle(permission.id)}
                        />
                        <div className="permission-info">
                          <span className="permission-name">{permission.name}</span>
                          <span className="permission-description">{permission.description}</span>
                          <span className="permission-action">{permission.action}</span>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
          
          <div className="modal-actions">
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button 
              type="submit" 
              className="btn btn-primary"
              disabled={isLoading || !formData.name.trim()}
            >
              {isLoading ? 'Saving...' : (isEditing ? 'Update Role' : 'Create Role')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RoleModal;
