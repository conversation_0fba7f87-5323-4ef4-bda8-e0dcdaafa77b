.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.role-modal {
  width: 800px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #95a5a6;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: #2c3e50;
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.form-section {
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 2px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3498db;
}

.form-group input:disabled {
  background-color: #f8f9fa;
  color: #95a5a6;
  cursor: not-allowed;
}

.permissions-section h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 18px;
}

.permissions-help {
  margin: 0 0 20px 0;
  color: #7f8c8d;
  font-size: 14px;
}

.permission-group {
  margin-bottom: 25px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
}

.permission-group-header {
  background-color: #f8f9fa;
  padding: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.permission-group-label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  font-weight: 500;
  color: #2c3e50;
}

.resource-name {
  text-transform: capitalize;
  font-size: 16px;
}

.permission-count {
  color: #7f8c8d;
  font-size: 14px;
  font-weight: normal;
}

.permission-list {
  padding: 10px;
}

.permission-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 10px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.permission-item:hover {
  background-color: #f8f9fa;
}

.permission-item input[type="checkbox"] {
  margin-top: 2px;
  width: auto;
}

.permission-info {
  flex: 1;
}

.permission-name {
  display: block;
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
  margin-bottom: 2px;
}

.permission-description {
  display: block;
  color: #7f8c8d;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.permission-action {
  display: inline-block;
  background-color: #27ae60;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  text-transform: uppercase;
  font-weight: 600;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

@media (max-width: 768px) {
  .role-modal {
    width: 95vw;
    height: 95vh;
  }
  
  .modal-content {
    padding: 15px;
  }
  
  .permission-group-label {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .permission-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .modal-actions {
    flex-direction: column;
  }
}
