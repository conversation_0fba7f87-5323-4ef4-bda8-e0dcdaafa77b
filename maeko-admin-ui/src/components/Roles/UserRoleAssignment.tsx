import { useState, useMemo } from 'react';
import { useUsers } from '../../hooks/useUsers';
import { useAssignRoleToUser, useRemoveRoleFromUser } from '../../hooks/useRoles';
import type { Role } from '../../services/api';
import './UserRoleAssignment.css';

interface UserRoleAssignmentProps {
  role: Role;
  onClose: () => void;
}

const UserRoleAssignment = ({ role, onClose }: UserRoleAssignmentProps) => {
  const [search, setSearch] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  
  const { data: usersData, isLoading } = useUsers({ limit: 100 });
  const assignRoleMutation = useAssignRoleToUser();
  const removeRoleMutation = useRemoveRoleFromUser();

  const users = usersData?.users || [];
  
  // Filter and categorize users
  const { usersWithRole, usersWithoutRole, filteredUsers } = useMemo(() => {
    const withRole = users.filter(user => user.role === role.name);
    const withoutRole = users.filter(user => user.role !== role.name);
    
    const filtered = search
      ? users.filter(user => 
          user.name.toLowerCase().includes(search.toLowerCase()) ||
          user.email.toLowerCase().includes(search.toLowerCase())
        )
      : users;
    
    return {
      usersWithRole: withRole,
      usersWithoutRole: withoutRole,
      filteredUsers: filtered,
    };
  }, [users, role.name, search]);

  const handleUserToggle = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleAssignSelected = async () => {
    try {
      for (const userId of selectedUsers) {
        await assignRoleMutation.mutateAsync({ userId, roleId: role.id });
      }
      setSelectedUsers([]);
    } catch (error) {
      // Error handling is done by the mutation
    }
  };

  const handleRemoveRole = async (userId: string) => {
    if (window.confirm('Are you sure you want to remove this role from the user?')) {
      try {
        await removeRoleMutation.mutateAsync(userId);
      } catch (error) {
        // Error handling is done by the mutation
      }
    }
  };

  const isProcessing = assignRoleMutation.isPending || removeRoleMutation.isPending;

  if (isLoading) {
    return (
      <div className="modal-overlay">
        <div className="modal">
          <div className="loading">Loading users...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal user-assignment-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Assign Users to "{role.name}" Role</h2>
          <button className="modal-close" onClick={onClose}>×</button>
        </div>
        
        <div className="modal-content">
          <div className="search-section">
            <input
              type="text"
              placeholder="Search users..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="assignment-sections">
            {/* Users with this role */}
            <div className="assignment-section">
              <h3>Users with "{role.name}" role ({usersWithRole.length})</h3>
              <div className="user-list">
                {usersWithRole
                  .filter(user => !search || filteredUsers.includes(user))
                  .map((user) => (
                  <div key={user.id} className="user-item assigned">
                    <div className="user-avatar">
                      {user.name.charAt(0).toUpperCase()}
                    </div>
                    <div className="user-info">
                      <h4>{user.name}</h4>
                      <p>{user.email}</p>
                    </div>
                    <div className="user-actions">
                      <button
                        className="btn btn-sm btn-danger"
                        onClick={() => handleRemoveRole(user.id)}
                        disabled={isProcessing}
                      >
                        Remove Role
                      </button>
                    </div>
                  </div>
                ))}
                
                {usersWithRole.length === 0 && (
                  <div className="empty-state">
                    <p>No users currently have this role.</p>
                  </div>
                )}
              </div>
            </div>
            
            {/* Users without this role */}
            <div className="assignment-section">
              <h3>Available Users ({usersWithoutRole.length})</h3>
              
              {selectedUsers.length > 0 && (
                <div className="bulk-actions">
                  <button
                    className="btn btn-primary"
                    onClick={handleAssignSelected}
                    disabled={isProcessing}
                  >
                    Assign Role to {selectedUsers.length} user{selectedUsers.length !== 1 ? 's' : ''}
                  </button>
                  <button
                    className="btn btn-secondary"
                    onClick={() => setSelectedUsers([])}
                  >
                    Clear Selection
                  </button>
                </div>
              )}
              
              <div className="user-list">
                {usersWithoutRole
                  .filter(user => !search || filteredUsers.includes(user))
                  .map((user) => (
                  <div key={user.id} className="user-item">
                    <label className="user-checkbox">
                      <input
                        type="checkbox"
                        checked={selectedUsers.includes(user.id)}
                        onChange={() => handleUserToggle(user.id)}
                      />
                    </label>
                    <div className="user-avatar">
                      {user.name.charAt(0).toUpperCase()}
                    </div>
                    <div className="user-info">
                      <h4>{user.name}</h4>
                      <p>{user.email}</p>
                      <span className="current-role">Current role: {user.role}</span>
                    </div>
                    <div className="user-actions">
                      <button
                        className="btn btn-sm btn-primary"
                        onClick={() => assignRoleMutation.mutate({ userId: user.id, roleId: role.id })}
                        disabled={isProcessing}
                      >
                        Assign Role
                      </button>
                    </div>
                  </div>
                ))}
                
                {usersWithoutRole.length === 0 && (
                  <div className="empty-state">
                    <p>All users already have this role.</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        
        <div className="modal-actions">
          <button className="btn btn-secondary" onClick={onClose}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserRoleAssignment;
