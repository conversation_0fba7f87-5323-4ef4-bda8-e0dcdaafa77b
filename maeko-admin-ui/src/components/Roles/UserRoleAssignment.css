.user-assignment-modal {
  width: 900px;
  max-height: 90vh;
}

.search-section {
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  padding: 10px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
}

.assignment-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  max-height: 60vh;
  overflow: hidden;
}

.assignment-section {
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
}

.assignment-section h3 {
  margin: 0;
  padding: 15px;
  background-color: #f8f9fa;
  color: #2c3e50;
  font-size: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.bulk-actions {
  padding: 10px 15px;
  background-color: #e8f4fd;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  gap: 10px;
  align-items: center;
}

.user-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.user-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: white;
  transition: background-color 0.2s;
}

.user-item:hover {
  background-color: #f8f9fa;
}

.user-item.assigned {
  background-color: #e8f5e8;
  border-color: #27ae60;
}

.user-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #3498db;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-info h4 {
  margin: 0 0 2px 0;
  color: #2c3e50;
  font-size: 14px;
  font-weight: 500;
}

.user-info p {
  margin: 0 0 2px 0;
  color: #7f8c8d;
  font-size: 12px;
}

.current-role {
  display: inline-block;
  background-color: #95a5a6;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  text-transform: uppercase;
  font-weight: 600;
}

.user-actions {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #95a5a6;
}

.empty-state p {
  margin: 0 0 15px 0;
  font-size: 14px;
}

/* Scrollbar styling */
.user-list::-webkit-scrollbar {
  width: 6px;
}

.user-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.user-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.user-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

@media (max-width: 768px) {
  .user-assignment-modal {
    width: 95vw;
    height: 95vh;
  }
  
  .assignment-sections {
    grid-template-columns: 1fr;
    max-height: 70vh;
  }
  
  .user-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .user-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .bulk-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
}
