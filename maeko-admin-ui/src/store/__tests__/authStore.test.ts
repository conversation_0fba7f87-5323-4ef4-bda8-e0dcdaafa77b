import { describe, it, expect, beforeEach, vi } from 'vitest';
import { useAuthStore } from '../authStore';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('AuthStore', () => {
  beforeEach(() => {
    // Reset the store state before each test
    useAuthStore.setState({
      user: null,
      token: null,
      isAuthenticated: false,
    });
    
    // Clear all mocks
    vi.clearAllMocks();
  });

  describe('login', () => {
    it('should set user, token, and isAuthenticated to true', () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'admin',
      };
      const mockToken = 'mock-jwt-token';

      const { login } = useAuthStore.getState();
      login(mockUser, mockToken);

      const state = useAuthStore.getState();
      expect(state.user).toEqual(mockUser);
      expect(state.token).toBe(mockToken);
      expect(state.isAuthenticated).toBe(true);
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_token', mockToken);
    });
  });

  describe('logout', () => {
    it('should clear user, token, and set isAuthenticated to false', () => {
      // First login
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'admin',
      };
      const { login, logout } = useAuthStore.getState();
      login(mockUser, 'token');

      // Then logout
      logout();

      const state = useAuthStore.getState();
      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token');
    });
  });

  describe('updateUser', () => {
    it('should update user data when user is logged in', () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'admin',
      };
      
      const { login, updateUser } = useAuthStore.getState();
      login(mockUser, 'token');

      // Update user name
      updateUser({ name: 'Updated Name' });

      const state = useAuthStore.getState();
      expect(state.user?.name).toBe('Updated Name');
      expect(state.user?.email).toBe('<EMAIL>'); // Should remain unchanged
    });

    it('should not update user data when no user is logged in', () => {
      const { updateUser } = useAuthStore.getState();
      updateUser({ name: 'Updated Name' });

      const state = useAuthStore.getState();
      expect(state.user).toBeNull();
    });
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = useAuthStore.getState();
      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
      expect(state.isAuthenticated).toBe(false);
    });
  });
});
