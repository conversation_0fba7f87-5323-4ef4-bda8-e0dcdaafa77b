import { describe, it, expect, beforeEach, vi } from 'vitest';
import { useUIStore } from '../uiStore';

describe('UIStore', () => {
  beforeEach(() => {
    // Reset the store state before each test
    useUIStore.setState({
      sidebarCollapsed: false,
      theme: 'light',
      notifications: [],
    });
    
    // Clear all timers
    vi.clearAllTimers();
  });

  describe('sidebar', () => {
    it('should toggle sidebar state', () => {
      const { toggleSidebar } = useUIStore.getState();
      
      expect(useUIStore.getState().sidebarCollapsed).toBe(false);
      
      toggleSidebar();
      expect(useUIStore.getState().sidebarCollapsed).toBe(true);
      
      toggleSidebar();
      expect(useUIStore.getState().sidebarCollapsed).toBe(false);
    });

    it('should set sidebar collapsed state directly', () => {
      const { setSidebarCollapsed } = useUIStore.getState();
      
      setSidebarCollapsed(true);
      expect(useUIStore.getState().sidebarCollapsed).toBe(true);
      
      setSidebarCollapsed(false);
      expect(useUIStore.getState().sidebarCollapsed).toBe(false);
    });
  });

  describe('theme', () => {
    it('should set theme and update document attribute', () => {
      const { setTheme } = useUIStore.getState();
      
      setTheme('dark');
      expect(useUIStore.getState().theme).toBe('dark');
      expect(document.documentElement.getAttribute('data-theme')).toBe('dark');
      
      setTheme('light');
      expect(useUIStore.getState().theme).toBe('light');
      expect(document.documentElement.getAttribute('data-theme')).toBe('light');
    });
  });

  describe('notifications', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should add notification with auto-generated id and timestamp', () => {
      const { addNotification } = useUIStore.getState();
      
      addNotification({
        type: 'success',
        message: 'Test notification',
      });

      const state = useUIStore.getState();
      expect(state.notifications).toHaveLength(1);
      expect(state.notifications[0]).toMatchObject({
        type: 'success',
        message: 'Test notification',
      });
      expect(state.notifications[0].id).toBeDefined();
      expect(state.notifications[0].timestamp).toBeDefined();
    });

    it('should auto-remove notification after 5 seconds', () => {
      const { addNotification } = useUIStore.getState();
      
      addNotification({
        type: 'info',
        message: 'Auto-remove test',
      });

      expect(useUIStore.getState().notifications).toHaveLength(1);
      
      // Fast-forward time by 5 seconds
      vi.advanceTimersByTime(5000);
      
      expect(useUIStore.getState().notifications).toHaveLength(0);
    });

    it('should remove specific notification by id', () => {
      const { addNotification, removeNotification } = useUIStore.getState();
      
      addNotification({ type: 'success', message: 'First' });
      addNotification({ type: 'error', message: 'Second' });
      
      const state = useUIStore.getState();
      expect(state.notifications).toHaveLength(2);
      
      const firstNotificationId = state.notifications[0].id;
      removeNotification(firstNotificationId);
      
      const updatedState = useUIStore.getState();
      expect(updatedState.notifications).toHaveLength(1);
      expect(updatedState.notifications[0].message).toBe('Second');
    });

    it('should clear all notifications', () => {
      const { addNotification, clearNotifications } = useUIStore.getState();
      
      addNotification({ type: 'success', message: 'First' });
      addNotification({ type: 'error', message: 'Second' });
      addNotification({ type: 'warning', message: 'Third' });
      
      expect(useUIStore.getState().notifications).toHaveLength(3);
      
      clearNotifications();
      
      expect(useUIStore.getState().notifications).toHaveLength(0);
    });
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = useUIStore.getState();
      expect(state.sidebarCollapsed).toBe(false);
      expect(state.theme).toBe('light');
      expect(state.notifications).toEqual([]);
    });
  });
});
