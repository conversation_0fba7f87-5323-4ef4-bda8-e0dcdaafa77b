import { QueryClient } from '@tanstack/react-query';
import { useUIStore } from '../store/uiStore';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false;
        }
        return failureCount < 3;
      },
      refetchOnWindowFocus: false,
    },
    mutations: {
      onError: (error: any) => {
        // Global error handling for mutations
        const addNotification = useUIStore.getState().addNotification;
        
        const message = error?.response?.data?.message || 
                       error?.message || 
                       'An unexpected error occurred';
        
        addNotification({
          type: 'error',
          message,
        });
      },
      onSuccess: (data: any, variables: any, context: any) => {
        // Global success handling for mutations
        if (context?.successMessage) {
          const addNotification = useUIStore.getState().addNotification;
          addNotification({
            type: 'success',
            message: context.successMessage,
          });
        }
      },
    },
  },
});
