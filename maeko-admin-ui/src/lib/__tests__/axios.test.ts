import { describe, it, expect, beforeEach, vi } from 'vitest';
import axios from 'axios';

// Mock axios
vi.mock('axios');
const mockedAxios = vi.mocked(axios);

// Mock the axios instance
const mockAxiosInstance = {
  interceptors: {
    request: { use: vi.fn() },
    response: { use: vi.fn() },
  },
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
};

mockedAxios.create.mockReturnValue(mockAxiosInstance as any);

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock window.location
delete (window as any).location;
window.location = { href: '' } as any;

describe('Axios Configuration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Re-import the module to trigger axios.create call
    vi.resetModules();
  });

  it('should create axios instance with correct config', async () => {
    await import('../axios');

    expect(mockedAxios.create).toHaveBeenCalledWith({
      baseURL: 'http://localhost:3000/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });

  describe('Request Interceptor', () => {
    let requestInterceptor: any;

    beforeEach(async () => {
      vi.resetModules();
      await import('../axios');

      requestInterceptor = mockAxiosInstance.interceptors.request.use.mock.calls[0][0];
    });

    it('should add auth token to request headers when token exists', () => {
      localStorageMock.getItem.mockReturnValue('test-token');
      
      const config = { headers: {} };
      const result = requestInterceptor(config);

      expect(localStorageMock.getItem).toHaveBeenCalledWith('auth_token');
      expect(result.headers.Authorization).toBe('Bearer test-token');
    });

    it('should not add auth token when token does not exist', () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      const config = { headers: {} };
      const result = requestInterceptor(config);

      expect(localStorageMock.getItem).toHaveBeenCalledWith('auth_token');
      expect(result.headers.Authorization).toBeUndefined();
    });

    it('should handle request interceptor errors', () => {
      const error = new Error('Request error');
      const errorHandler = mockAxiosInstance.interceptors.request.use.mock.calls[0][1];

      expect(() => errorHandler(error)).rejects.toThrow('Request error');
    });
  });

  describe('Response Interceptor', () => {
    let responseInterceptor: any;
    let errorHandler: any;

    beforeEach(async () => {
      vi.resetModules();
      // Reset window.location.href
      window.location.href = '';
      await import('../axios');

      responseInterceptor = mockAxiosInstance.interceptors.response.use.mock.calls[0][0];
      errorHandler = mockAxiosInstance.interceptors.response.use.mock.calls[0][1];
    });

    it('should return response as-is on success', () => {
      const response = { data: { message: 'success' } };
      const result = responseInterceptor(response);
      
      expect(result).toBe(response);
    });

    it('should handle 401 unauthorized errors', () => {
      const error = {
        response: { status: 401 },
      };

      expect(() => errorHandler(error)).rejects.toThrow();
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token');
      expect(window.location.href).toBe('/login');
    });

    it('should handle 403 forbidden errors', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const error = {
        response: { status: 403 },
      };

      expect(() => errorHandler(error)).rejects.toThrow();
      expect(consoleSpy).toHaveBeenCalledWith('Access forbidden');
      
      consoleSpy.mockRestore();
    });

    it('should handle other errors without special treatment', () => {
      const error = {
        response: { status: 500 },
      };

      expect(() => errorHandler(error)).rejects.toThrow();
      expect(localStorageMock.removeItem).not.toHaveBeenCalled();
      expect(window.location.href).not.toBe('/login');
    });

    it('should handle errors without response object', () => {
      const error = new Error('Network error');

      expect(() => errorHandler(error)).rejects.toThrow('Network error');
    });
  });
});
