import api from "../lib/axios";

// Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
}

export interface CreateUserRequest {
  email: string;
  name: string;
  password: string;
  role: string;
}

// Role and Permission Types
export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
  createdAt: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  userCount: number;
  isSystem: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateRoleRequest {
  name: string;
  description: string;
  permissionIds: string[];
}

export interface UpdateRoleRequest {
  name?: string;
  description?: string;
  permissionIds?: string[];
}

// Auth API
export const authAPI = {
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    // TODO: Remove this mock data
    // const response = await api.post('/auth/login', credentials);
    // return response.data;
    return {
      user: {
        id: "1",
        email: "<EMAIL>",
        name: "Test User",
        role: "admin",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
      token: "mockToken",
    };
  },

  logout: async (): Promise<void> => {
    await api.post("/auth/logout");
  },

  me: async (): Promise<User> => {
    const response = await api.get("/auth/me");
    return response.data;
  },

  refreshToken: async (): Promise<{ token: string }> => {
    const response = await api.post("/auth/refresh");
    return response.data;
  },
};

// Users API
export const usersAPI = {
  getUsers: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<{
    users: User[];
    total: number;
    page: number;
    totalPages: number;
  }> => {
    const response = await api.get("/users", { params });
    return response.data;
  },

  getUser: async (id: string): Promise<User> => {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },

  createUser: async (userData: CreateUserRequest): Promise<User> => {
    const response = await api.post("/users", userData);
    return response.data;
  },

  updateUser: async (id: string, userData: Partial<User>): Promise<User> => {
    const response = await api.put(`/users/${id}`, userData);
    return response.data;
  },

  deleteUser: async (id: string): Promise<void> => {
    await api.delete(`/users/${id}`);
  },
};

// Roles API
export const rolesAPI = {
  getRoles: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<{
    roles: Role[];
    total: number;
    page: number;
    totalPages: number;
  }> => {
    // const response = await api.get("/roles", { params });
    // return response.data;
    return {
      total: 1,
      page: 1,
      totalPages: 1,
      roles: [
        {
          id: "1",
          name: "Admin",
          description: "Administrator role",
          permissions: [],
          userCount: 5,
          isSystem: true,
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        },
      ],
    };
  },

  getRole: async (id: string): Promise<Role> => {
    const response = await api.get(`/roles/${id}`);
    return response.data;
  },

  createRole: async (roleData: CreateRoleRequest): Promise<Role> => {
    const response = await api.post("/roles", roleData);
    return response.data;
  },

  updateRole: async (
    id: string,
    roleData: UpdateRoleRequest
  ): Promise<Role> => {
    const response = await api.put(`/roles/${id}`, roleData);
    return response.data;
  },

  deleteRole: async (id: string): Promise<void> => {
    await api.delete(`/roles/${id}`);
  },

  assignRoleToUser: async (userId: string, roleId: string): Promise<void> => {
    await api.post(`/users/${userId}/role`, { roleId });
  },

  removeRoleFromUser: async (userId: string): Promise<void> => {
    await api.delete(`/users/${userId}/role`);
  },
};

// Permissions API
export const permissionsAPI = {
  getPermissions: async (): Promise<Permission[]> => {
    const response = await api.get("/permissions");
    return response.data;
  },

  getPermissionsByResource: async (): Promise<Record<string, Permission[]>> => {
    const response = await api.get("/permissions/by-resource");
    return response.data;
  },
};

// Dashboard API
export const dashboardAPI = {
  getStats: async (): Promise<{
    totalUsers: number;
    activeUsers: number;
    totalRevenue: number;
    monthlyGrowth: number;
  }> => {
    const response = await api.get("/dashboard/stats");
    return response.data;
  },

  getChartData: async (
    period: "7d" | "30d" | "90d"
  ): Promise<{
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      borderColor: string;
      backgroundColor: string;
    }>;
  }> => {
    const response = await api.get(`/dashboard/chart?period=${period}`);
    return response.data;
  },
};
