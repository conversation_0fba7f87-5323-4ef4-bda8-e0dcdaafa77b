import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock the axios lib first
vi.mock('../../lib/axios', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

// Mock axios module
vi.mock('axios', () => ({
  default: {
    create: vi.fn(() => ({
      interceptors: {
        request: { use: vi.fn() },
        response: { use: vi.fn() },
      },
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
    })),
  },
}));

import api from '../../lib/axios';
import { authAPI, usersAPI, dashboardAPI } from '../api';

const mockedApi = vi.mocked(api);

describe('API Services', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('authAPI', () => {
    describe('login', () => {
      it('should make POST request to /auth/login', async () => {
        const mockResponse = {
          data: {
            user: { id: '1', email: '<EMAIL>', name: 'Test User', role: 'admin' },
            token: 'mock-token',
          },
        };
        mockedApi.post.mockResolvedValueOnce(mockResponse);

        const credentials = { email: '<EMAIL>', password: 'password' };
        const result = await authAPI.login(credentials);

        expect(mockedApi.post).toHaveBeenCalledWith('/auth/login', credentials);
        expect(result).toEqual(mockResponse.data);
      });
    });

    describe('logout', () => {
      it('should make POST request to /auth/logout', async () => {
        mockedApi.post.mockResolvedValueOnce({ data: {} });

        await authAPI.logout();

        expect(mockedApi.post).toHaveBeenCalledWith('/auth/logout');
      });
    });

    describe('me', () => {
      it('should make GET request to /auth/me', async () => {
        const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User', role: 'admin' };
        mockedApi.get.mockResolvedValueOnce({ data: mockUser });

        const result = await authAPI.me();

        expect(mockedApi.get).toHaveBeenCalledWith('/auth/me');
        expect(result).toEqual(mockUser);
      });
    });

    describe('refreshToken', () => {
      it('should make POST request to /auth/refresh', async () => {
        const mockResponse = { data: { token: 'new-token' } };
        mockedApi.post.mockResolvedValueOnce(mockResponse);

        const result = await authAPI.refreshToken();

        expect(mockedApi.post).toHaveBeenCalledWith('/auth/refresh');
        expect(result).toEqual(mockResponse.data);
      });
    });
  });

  describe('usersAPI', () => {
    describe('getUsers', () => {
      it('should make GET request to /users with params', async () => {
        const mockResponse = {
          data: {
            users: [{ id: '1', name: 'User 1' }],
            total: 1,
            page: 1,
            totalPages: 1,
          },
        };
        mockedApi.get.mockResolvedValueOnce(mockResponse);

        const params = { page: 1, limit: 10, search: 'test' };
        const result = await usersAPI.getUsers(params);

        expect(mockedApi.get).toHaveBeenCalledWith('/users', { params });
        expect(result).toEqual(mockResponse.data);
      });

      it('should make GET request to /users without params', async () => {
        const mockResponse = {
          data: {
            users: [],
            total: 0,
            page: 1,
            totalPages: 1,
          },
        };
        mockedApi.get.mockResolvedValueOnce(mockResponse);

        const result = await usersAPI.getUsers();

        expect(mockedApi.get).toHaveBeenCalledWith('/users', { params: undefined });
        expect(result).toEqual(mockResponse.data);
      });
    });

    describe('getUser', () => {
      it('should make GET request to /users/:id', async () => {
        const mockUser = { id: '1', name: 'Test User' };
        mockedApi.get.mockResolvedValueOnce({ data: mockUser });

        const result = await usersAPI.getUser('1');

        expect(mockedApi.get).toHaveBeenCalledWith('/users/1');
        expect(result).toEqual(mockUser);
      });
    });

    describe('createUser', () => {
      it('should make POST request to /users', async () => {
        const userData = {
          email: '<EMAIL>',
          name: 'New User',
          password: 'password',
          role: 'user',
        };
        const mockResponse = { data: { id: '2', ...userData } };
        mockedApi.post.mockResolvedValueOnce(mockResponse);

        const result = await usersAPI.createUser(userData);

        expect(mockedApi.post).toHaveBeenCalledWith('/users', userData);
        expect(result).toEqual(mockResponse.data);
      });
    });

    describe('updateUser', () => {
      it('should make PUT request to /users/:id', async () => {
        const userData = { name: 'Updated Name' };
        const mockResponse = { data: { id: '1', ...userData } };
        mockedApi.put.mockResolvedValueOnce(mockResponse);

        const result = await usersAPI.updateUser('1', userData);

        expect(mockedApi.put).toHaveBeenCalledWith('/users/1', userData);
        expect(result).toEqual(mockResponse.data);
      });
    });

    describe('deleteUser', () => {
      it('should make DELETE request to /users/:id', async () => {
        mockedApi.delete.mockResolvedValueOnce({ data: {} });

        await usersAPI.deleteUser('1');

        expect(mockedApi.delete).toHaveBeenCalledWith('/users/1');
      });
    });
  });

  describe('dashboardAPI', () => {
    describe('getStats', () => {
      it('should make GET request to /dashboard/stats', async () => {
        const mockStats = {
          totalUsers: 100,
          activeUsers: 80,
          totalRevenue: 50000,
          monthlyGrowth: 15,
        };
        mockedApi.get.mockResolvedValueOnce({ data: mockStats });

        const result = await dashboardAPI.getStats();

        expect(mockedApi.get).toHaveBeenCalledWith('/dashboard/stats');
        expect(result).toEqual(mockStats);
      });
    });

    describe('getChartData', () => {
      it('should make GET request to /dashboard/chart with period', async () => {
        const mockChartData = {
          labels: ['Jan', 'Feb', 'Mar'],
          datasets: [{ label: 'Users', data: [10, 20, 30], borderColor: '#3498db', backgroundColor: '#3498db' }],
        };
        mockedApi.get.mockResolvedValueOnce({ data: mockChartData });

        const result = await dashboardAPI.getChartData('30d');

        expect(mockedApi.get).toHaveBeenCalledWith('/dashboard/chart?period=30d');
        expect(result).toEqual(mockChartData);
      });
    });
  });
});
