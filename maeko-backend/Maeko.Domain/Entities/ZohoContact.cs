
using System.Runtime.InteropServices;
using System.Text.Json.Serialization;

namespace Maeko.Domain.Entities.ZohoContacts
{
    public class ZohoContactsResponse
    {
        public List<ZohoContact> data { get; set; } = new();
        public ZohoInfo info { get; set; } = new();
    }

    public class ZohoInfo
    {
        public int per_page { get; set; }
        public int count { get; set; }
        public int page { get; set; }
        public string? sort_by { get; set; }
        public string? sort_order { get; set; }
        public bool more_records { get; set; }
    }

    public class ZohoContact
    {
        public ZohoUser? Owner { get; set; }
        public string? Email { get; set; }
        public string? MAEKO_customer { get; set; }
        public string? Visitor_Score { get; set; }
        public string? Other_Phone { get; set; }
        public string? Mailing_State { get; set; }
        public string? Blast_Campaign { get; set; }
        public string? Other_State { get; set; }
        public string? Other_Country { get; set; }
        public string? BS_Contact_ID { get; set; }
        public string? Last_Activity_Time { get; set; }
        public string? Department { get; set; }
        public string? Unsubscribed_Mode { get; set; }

        [JsonPropertyName("$process_flow")]
        public bool? ProcessFlow { get; set; }

        public string? Need_for_Food_Waste_Composter { get; set; }
        public string? Mailing_Country { get; set; }

        [JsonPropertyName("$locked_for_me")]
        public bool? LockedForMe { get; set; }

        public string? Second_Mobile { get; set; }
        public string? id { get; set; }

        [JsonPropertyName("$approved")]
        public bool? Approved { get; set; }

        public string? BS_C_Modified_by { get; set; }

        [JsonPropertyName("$approval")]
        public ZohoApproval? Approval { get; set; }

        public string? Referral_Name { get; set; }
        public string? First_Visited_URL { get; set; }
        public string? Days_Visited { get; set; }
        public string? Other_City { get; set; }
        public string? Attitude { get; set; }
        public DateTime Created_Time { get; set; }

        [JsonPropertyName("$followed")]
        public bool? Followed { get; set; }

        [JsonPropertyName("$editable")]
        public bool? Editable { get; set; }

        public string? BS_C_Created_by { get; set; }
        public string? Last_Visited_Time { get; set; }
        public ZohoUser? Created_By { get; set; }

        [JsonPropertyName("$zia_owner_assignment")]
        public string? ZiaOwnerAssignment { get; set; }

        public string? Secondary_Email { get; set; }

        [JsonPropertyName("$is_duplicate")]
        public bool? IsDuplicate { get; set; }

        public string? Product_Category { get; set; }
        public string? Description { get; set; }
        public string? Mailing_Zip { get; set; }
        public int? Number_Of_Chats { get; set; }
        public string? Lead_Source_From { get; set; }

        [JsonPropertyName("$review_process")]
        public ZohoReviewProcess? ReviewProcess { get; set; }

        public string? Other_Zip { get; set; }
        public string? Mailing_Street { get; set; }
        public int? Average_Time_Spent_Minutes { get; set; }

        [JsonPropertyName("$layout_id")]
        public ZohoLayout? LayoutId { get; set; }

        public string? Prefix { get; set; }
        public string? Full_Name { get; set; }
        public string? Record_Image { get; set; }
        public string? Lead_Status { get; set; }
        public ZohoUser? Modified_By { get; set; }

        [JsonPropertyName("$review")]
        public string? Review { get; set; }

        public List<string>? Treatment_Method { get; set; }
        public string? Phone { get; set; }
        public string? Email_Batch { get; set; }
        public ZohoAccount? Account_Name { get; set; }
        public bool Email_Opt_Out { get; set; }
        public DateTime Modified_Time { get; set; }
        public bool? Sync_to_FSM { get; set; }
        public string? Mailing_City { get; set; }
        public string? Unsubscribed_Time { get; set; }
        public string? Contact_Owner_in_Bee_Solution { get; set; }
        public string? Other_Street { get; set; }
        public string? Join_Composting_Programme { get; set; }
        public string? Job_Title { get; set; }
        public string? Mobile { get; set; }
        public string? First_Visited_Time { get; set; }
        public string? Update_1 { get; set; }
        public string? Update_2 { get; set; }
        public string? Last_Name { get; set; }

        [JsonPropertyName("$in_merge")]
        public bool? InMerge { get; set; }

        public string? Referrer { get; set; }
        public bool? Locked__s { get; set; }
        public string? Lead_Source { get; set; }
        public List<string>? Tag { get; set; }
        public string? Fax { get; set; }

        [JsonPropertyName("$approval_state")]
        public string? ApprovalState { get; set; }

        public string? Survey_by { get; set; }
        public string? Social_Media_Channel { get; set; }
    }

    public class ZohoUser
    {
        public string? name { get; set; }
        public string? id { get; set; }
        public string? email { get; set; }
    }

    public class ZohoAccount
    {
        public string? name { get; set; }
        public string? id { get; set; }
    }

    public class ZohoApproval
    {
        public bool? approve { get; set; }
        public bool? reject { get; set; }
        public bool? resubmit { get; set; }

        [JsonPropertyName("delegate")]
        public bool @delegate { get; set; }
        public bool takeover { get; set; }
    }

    public class ZohoReviewProcess
    {
        public bool approve { get; set; }
        public bool reject { get; set; }
        public bool resubmit { get; set; }
    }

    public class ZohoLayout
    {
        public string? display_label { get; set; }
        public string? name { get; set; }
        public string? id { get; set; }
    }
}
