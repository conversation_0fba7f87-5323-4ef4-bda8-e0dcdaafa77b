using System.IdentityModel.Tokens.Jwt;
using Maeko.Domain.Entities;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text;
using Maeko.Application.Services.Jwt;
using Microsoft.IdentityModel.Tokens;

namespace Maeko.Infrastructure.Services.Jwt;

public class JwtService : IJwtService
{
    private readonly JwtSettings _settings;

    public JwtService(IOptions<JwtSettings> options)
    {
        _settings = options.Value;
    }

    public string GenerateJwtToken(string role, string token)
    {
        var claims = new[]
        {
            new Claim("Token", token),
            new Claim(ClaimTypes.Role, "Admin")
        };

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_settings.Key));
        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var jwtToken = new JwtSecurityToken(
            issuer: _settings.Issuer,
            audience: _settings.Audience,
            claims: claims,
            expires: DateTime.Now.AddMinutes(Convert.ToDouble(_settings.ExpiresInMinutes)),
            signingCredentials: creds
        );
        
        var finalToken = new JwtSecurityTokenHandler().WriteToken(jwtToken);

        return finalToken;
    }

}
