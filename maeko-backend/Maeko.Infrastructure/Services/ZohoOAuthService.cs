using Maeko.Application.Services.Zoho;
using Maeko.Domain.Entities;
using Maeko.Domain.Entities.ZohoContacts;
using Microsoft.Extensions.Options;
using System.Net.Http.Headers;
using System.Text.Json;
using Maeko.Application.Services.Jwt;

namespace Maeko.Infrastructure.Services.Zoho;

public class ZohoOAuthService : IZohoOAuthService
{
    private readonly HttpClient _httpClient;
    private readonly ZohoSettings _settings;
    private readonly IJwtService _jwtService;

    public ZohoOAuthService(HttpClient httpClient, IOptions<ZohoSettings> options, IJwtService jwtService)
    {
        _httpClient = httpClient;
        _settings = options.Value;
        _jwtService = jwtService;
    }
    public Task<string> GenerateAuthorizationUrlAsync()
    {
        var url = $"{_settings.AccountsBaseUrl}/oauth/v2/auth" +
                  $"?scope={_settings.Scopes}" +
                  $"&client_id={_settings.ClientId}" +
                  $"&response_type=code" +
                  $"&access_type=offline" +
                  $"&redirect_uri={Uri.EscapeDataString(_settings.RedirectUri)}"+
                  $"&prompt=login";

        return Task.FromResult(url);
    }

    public async Task<string> GetAccessTokenAsync(string code)
    {
        var clientId = _settings.ClientId;
        var clientSecret = _settings.ClientSecret;
        var refreshToken = _settings.RefreshToken;

        var content = new FormUrlEncodedContent(new[]
        {
            new KeyValuePair<string, string>("code", code),
            new KeyValuePair<string, string>("client_id", clientId),
            new KeyValuePair<string, string>("client_secret", clientSecret),
            new KeyValuePair<string, string>("grant_type", "authorization_code"),
            new KeyValuePair<string, string>("redirect_uri", _settings.RedirectUri)
        });

        var response = await _httpClient.PostAsync($"{_settings.AccountsBaseUrl}/oauth/v2/token", content);
        response.EnsureSuccessStatusCode();

        var responseContent = await response.Content.ReadAsStringAsync();
        var tokenData = JsonDocument.Parse(responseContent);
        return _jwtService.GenerateJwtToken("Admin", tokenData.RootElement.GetProperty("access_token").GetString() ?? "");
    }

    public async Task<string> RefreshAccessTokenAsync()
    {
        var clientId = _settings.ClientId;
        var clientSecret = _settings.ClientSecret;
        var refreshToken = _settings.RefreshToken;

        var content = new FormUrlEncodedContent(new[]
        {
            new KeyValuePair<string, string>("refresh_token", refreshToken),
            new KeyValuePair<string, string>("client_id", clientId),
            new KeyValuePair<string, string>("client_secret", clientSecret),
            new KeyValuePair<string, string>("grant_type", "refresh_token")
        });

        var response = await _httpClient.PostAsync($"{_settings.AccountsBaseUrl}/oauth/v2/token", content);
        response.EnsureSuccessStatusCode();

        var responseContent = await response.Content.ReadAsStringAsync();
        var tokenData = JsonDocument.Parse(responseContent);
        return tokenData.RootElement.GetProperty("access_token").GetString();
    }

    public async Task<List<ZohoContact>> GetContactsAsync()
    {
        var accessToken = await RefreshAccessTokenAsync();

        var request = new HttpRequestMessage(HttpMethod.Get, $"{_settings.ApiBaseUrl}/crm/v2/Contacts");
        request.Headers.Authorization = new AuthenticationHeaderValue("Zoho-oauthtoken", accessToken);

        var response = await _httpClient.SendAsync(request);
        response.EnsureSuccessStatusCode();

        //return await response.Content.ReadAsStringAsync();
        var json = await response.Content.ReadAsStringAsync();
        var contactsResponse = JsonSerializer.Deserialize<ZohoContactsResponse>(json);

        return contactsResponse?.data ?? new List<ZohoContact>();
    }

    public async Task<string> GetProfileAsync()
    {
        var accessToken = await RefreshAccessTokenAsync();

        var request = new HttpRequestMessage(HttpMethod.Get, $"{_settings.ApiBaseUrl}/crm/v2/users?type=CurrentUser");
        request.Headers.Authorization = new AuthenticationHeaderValue("Zoho-oauthtoken", accessToken);

        var response = await _httpClient.SendAsync(request);
        response.EnsureSuccessStatusCode();

        return await response.Content.ReadAsStringAsync();
    }

}
