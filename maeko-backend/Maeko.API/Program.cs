using System.Text;
using Maeko.Application.Services;
using Maeko.Application.Services.Jwt;
using Maeko.Application.Services.Zoho;
using Maeko.Domain.Entities;
using Maeko.Domain.Interfaces;
using Maeko.Infrastructure.Data;
using Maeko.Infrastructure.Services.Zoho;
using Maeko.Infrastructure.Services.Jwt;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Register DbContext
builder.Services.AddDbContext<AppDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));
builder.Services.Configure<ZohoSettings>(builder.Configuration.GetSection("Zoho"));
builder.Services.Configure<JwtSettings>(builder.Configuration.GetSection("Jwt"));

// Register repositories
builder.Services.AddScoped<IProductRepository, ProductService>();
builder.Services.AddScoped<IWeatherForecast, WeatherForecastService>();
builder.Services.AddScoped<IJwtService, JwtService>();
//builder.Services.AddScoped<IZohoOAuthService, ZohoOAuthService>();
builder.Services.AddHttpClient<IZohoOAuthService, ZohoOAuthService>();
builder.Services.AddHttpClient<ZohoUserSyncService>();

builder.Services.AddAuthentication("Bearer")
    .AddJwtBearer("Bearer", options =>
    {
        var config = builder.Configuration.GetSection("Jwt");
        var key = config["Key"];
        if (string.IsNullOrWhiteSpace(key))
        {
            throw new InvalidOperationException("JWT Key is missing in configuration.");
        }
        
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = config["Issuer"],
            ValidAudience = config["Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(key))
        };
    });

builder.Services.AddAuthorization();

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();
app.Run();
