using Microsoft.AspNetCore.Mvc;
using Maeko.Domain.Interfaces;
using Maeko.Domain.Entities;
using Microsoft.AspNetCore.Authorization;

[ApiController]
[Route("[controller]")]
[Authorize]
public class ProductsController : ControllerBase
{
    private readonly IProductRepository _repo;
    public ProductsController(IProductRepository repo)
    {
        _repo = repo;
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<Product>> Get(int id)
    {
        var product = await _repo.GetByIdAsync(id);
        if (product == null) return NotFound();
        return product;
    }

    [HttpPost]
    public async Task<IActionResult> Post(Product product)
    {
        await _repo.AddAsync(product);
        return CreatedAtAction(nameof(Get), new { id = product.Id }, product);
    }
}
