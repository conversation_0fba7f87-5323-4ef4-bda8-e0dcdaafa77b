using Maeko.Application.Services.Zoho;
using Maeko.Domain.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Net.Http;
using System.Threading.Tasks;

namespace Maeko.WebApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ZohoOAuthController : ControllerBase
    {
        private readonly IZohoOAuthService _zohoOAuthService;

        public ZohoOAuthController(IZohoOAuthService zohoOAuthService)
        {
            _zohoOAuthService = zohoOAuthService;
        }

        [HttpGet("auth-urls")]
        public async Task<IActionResult> GetAuthorizationUrl()
        {
            #if DEBUG
                        // For Swagger or testing: return the URL in JSON
                        var url = await _zohoOAuthService.GenerateAuthorizationUrlAsync();
                        return Ok(new { auth_url = url });
            #else
                // In production: redirect browser
                var url = await _zohoOAuthService.GenerateAuthorizationUrlAsync();
                return Redirect(url);
            #endif
        }

        [HttpGet("token")]
        public async Task<IActionResult> GetRefreshToken()
        {
            var token = await _zohoOAuthService.RefreshAccessTokenAsync();
            return Ok(new { access_token = token });
        }

        // Optional: callback endpoint to test OAuth redirect (if needed)
        [HttpGet("callback")]
        public async Task<IActionResult> Callback(
            [FromQuery] string code,
            [FromQuery] string location,
            [FromQuery(Name = "accounts-server")] string accountsServer)
        {
            //return Ok(new { code, location, accountsServer });
            var token = await _zohoOAuthService.GetAccessTokenAsync(code);
            return Ok(new { access_token = token });

        }

        [HttpGet("contacts")]
        public async Task<IActionResult> GetContacts()
        {
            var json = await _zohoOAuthService.GetContactsAsync();
            return Ok(json);
        }

        [HttpGet("my-profile")]
        public async Task<IActionResult> GetMyProfile()
        {
            var json = await _zohoOAuthService.GetProfileAsync();
            return Content(json, "application/json");
        }
    }
}
