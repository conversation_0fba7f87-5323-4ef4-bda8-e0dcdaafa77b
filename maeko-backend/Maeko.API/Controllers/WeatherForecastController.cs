using Maeko.Domain.Entities;
using Maeko.Domain.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace Maeko.API.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class WeatherForecastController : ControllerBase
    {
        private readonly IWeatherForecast _weatherForecast;
        private readonly ILogger<WeatherForecastController> _logger;
        public WeatherForecastController(IWeatherForecast weatherForecast, ILogger<WeatherForecastController> logger)
        {
            _weatherForecast = weatherForecast;
            _logger = logger;
        }

        [HttpGet(Name = "GetWeatherForecast")]
        public IEnumerable<WeatherForecast> Get()
        {
            var result = _weatherForecast.Get();
            return result;           
        }
    }
}
// This code defines a WeatherForecastController in an ASP.NET Core API application.