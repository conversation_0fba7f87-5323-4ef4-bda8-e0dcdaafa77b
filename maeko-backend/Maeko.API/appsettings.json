{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=.;Database=YourAppDb;Trusted_Connection=True;"}, "Jwt": {"Key": "YourSuperSecretKeyHereItMustBeAtLeast16CharactersLong", "Issuer": "MaekoApi", "Audience": "MaekoClient", "ExpiresInMinutes": "60"}, "Zoho": {"ClientId": "1000.DM9EDH0Q55OJUKJ9A4XK2V6N08KLJJ", "ClientSecret": "37eba247159518ae5b5e5c20fddf52d2452b9c2823", "RedirectUri": "http://localhost:5000/api/ZohoOAuth/callback", "ApiBaseUrl": "https://www.zohoapis.com", "AccountsBaseUrl": "https://accounts.zoho.com", "RefreshToken": "**********************************************************************", "Scopes": "ZohoCRM.modules.ALL,ZohoCRM.users.READ,ZohoCRM.org.READ", "OrgId": "optional for validation", "FrontendBaseUrl": "http://localhost:5173"}}