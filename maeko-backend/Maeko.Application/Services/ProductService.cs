using Maeko.Domain.Entities;
using Maeko.Domain.Interfaces;

namespace Maeko.Application.Services
{
    public class ProductService : IProductRepository
    {
        private readonly List<Product> _products = new();
        public async Task<Product?> GetByIdAsync(int id)
        {
            return await Task.FromResult(_products.FirstOrDefault(p => p.Id == id));
        }
        public async Task AddAsync(Product product)
        {
            _products.Add(product);
            await Task.CompletedTask;
        }
    }
}
