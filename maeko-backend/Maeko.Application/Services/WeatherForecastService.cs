using Maeko.Domain.Constants;
using Maeko.Domain.Entities;
using Maeko.Domain.Interfaces;

namespace Maeko.Application.Services
{
    public class WeatherForecastService : IWeatherForecast
    {
        public IEnumerable<WeatherForecast> Get()
        {
            return Enumerable.Range(1, 5).Select(index => new WeatherForecast
            {
                Date = DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
                TemperatureC = Random.Shared.Next(-20, 55),
                Summary = WeatherConstants.Summaries[Random.Shared.Next(WeatherConstants.Summaries.Length)]
            })
             .ToArray();
        }
    }
}
