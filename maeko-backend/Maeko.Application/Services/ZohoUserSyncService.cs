namespace Maeko.Application.Services.Zoho;

public class ZohoUserSyncService
{
    private readonly IZohoOAuthService _zohoOAuthService;

    public ZohoUserSyncService(IZohoOAuthService zohoOAuthService)
    {
        _zohoOAuthService = zohoOAuthService;
    }

    public async Task SyncUserAsync()
    {
        var accessToken = await _zohoOAuthService.RefreshAccessTokenAsync();
        // Call Zoho CRM API with token and sync contact data
    }

}
