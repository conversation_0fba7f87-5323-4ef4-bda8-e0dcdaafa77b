
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36310.24 d17.14
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Maeko.Domain", "Maeko.Domain\Maeko.Domain.csproj", "{BA6F4E95-684E-434D-BC6B-E0C5D9FEBE68}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Maeko.Application", "Maeko.Application\Maeko.Application.csproj", "{7907E54B-3E0C-43F8-A283-4FE9377078C5}"
	ProjectSection(ProjectDependencies) = postProject
		{BA6F4E95-684E-434D-BC6B-E0C5D9FEBE68} = {BA6F4E95-684E-434D-BC6B-E0C5D9FEBE68}
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Maeko.Infrastructure", "Maeko.Infrastructure\Maeko.Infrastructure.csproj", "{E07DEA7B-1A9F-4367-919D-AC7399EDAE8C}"
	ProjectSection(ProjectDependencies) = postProject
		{7907E54B-3E0C-43F8-A283-4FE9377078C5} = {7907E54B-3E0C-43F8-A283-4FE9377078C5}
		{BA6F4E95-684E-434D-BC6B-E0C5D9FEBE68} = {BA6F4E95-684E-434D-BC6B-E0C5D9FEBE68}
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Maeko.API", "Maeko.API\Maeko.API.csproj", "{D941A222-7E84-4E3F-A4F6-04F51D87B741}"
	ProjectSection(ProjectDependencies) = postProject
		{7907E54B-3E0C-43F8-A283-4FE9377078C5} = {7907E54B-3E0C-43F8-A283-4FE9377078C5}
		{BA6F4E95-684E-434D-BC6B-E0C5D9FEBE68} = {BA6F4E95-684E-434D-BC6B-E0C5D9FEBE68}
		{E07DEA7B-1A9F-4367-919D-AC7399EDAE8C} = {E07DEA7B-1A9F-4367-919D-AC7399EDAE8C}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{BA6F4E95-684E-434D-BC6B-E0C5D9FEBE68}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BA6F4E95-684E-434D-BC6B-E0C5D9FEBE68}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BA6F4E95-684E-434D-BC6B-E0C5D9FEBE68}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BA6F4E95-684E-434D-BC6B-E0C5D9FEBE68}.Release|Any CPU.Build.0 = Release|Any CPU
		{7907E54B-3E0C-43F8-A283-4FE9377078C5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7907E54B-3E0C-43F8-A283-4FE9377078C5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7907E54B-3E0C-43F8-A283-4FE9377078C5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7907E54B-3E0C-43F8-A283-4FE9377078C5}.Release|Any CPU.Build.0 = Release|Any CPU
		{E07DEA7B-1A9F-4367-919D-AC7399EDAE8C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E07DEA7B-1A9F-4367-919D-AC7399EDAE8C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E07DEA7B-1A9F-4367-919D-AC7399EDAE8C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E07DEA7B-1A9F-4367-919D-AC7399EDAE8C}.Release|Any CPU.Build.0 = Release|Any CPU
		{D941A222-7E84-4E3F-A4F6-04F51D87B741}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D941A222-7E84-4E3F-A4F6-04F51D87B741}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D941A222-7E84-4E3F-A4F6-04F51D87B741}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D941A222-7E84-4E3F-A4F6-04F51D87B741}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {7E6661CA-770A-4AE5-91A6-F95D8B51895C}
	EndGlobalSection
EndGlobal
