# 🧱 Project Architecture Overview

This project follows a **clean architecture** pattern with clear separation of concerns across four main layers:

| 🏢 Project              | 🔧 Role                        | 📦 What Goes Inside                              |
|------------------------|-------------------------------|--------------------------------------------------|
| **Maeko.API**          | Web entry point, REST API     | Controllers, DI container setup, Swagger config  |
| **Maeko.Domain**       | Business rules and contracts  | Core entities, interfaces, enums, value objects  |
| **Maeko.Application**  | Application orchestration     | Use case services, commands, DTOs, validation    |
| **Maeko.Infrastructure** | External system integration | EF Core DbContext, Repositories, API clients     |

---

### 🧠 Notes

- `Maeko.Domain` has **no dependencies** on any other project.
- `Maeko.Application` depends only on `Maeko.Domain`.
- `Maeko.Infrastructure` implements interfaces from `Domain` and is injected into `Application`.
- `Maeko.API` ties everything together via Dependency Injection.

This structure enables **testability, scalability, and long-term maintainability**.

---

### 🚀 Quick Start

To run the application:
1. Set `Maeko.API` as the startup project.
2. Run migrations and update the database if needed.
3. Launch via `Ctrl + F5` and test via Swagger UI at `/swagger`.

